# https://moonrepo.dev/docs/config/workspace
# yaml-language-server: $schema=https://moonrepo.dev/schemas/workspace.json
$schema: "https://moonrepo.dev/schemas/workspace.json"

vcs:
  manager: "git"
  provider: gitlab
  defaultBranch: "main"
  # @see: https://moonrepo.dev/docs/config/workspace#hooks
  hooks:
    pre-commit:
      - "moon run :lint :format --affected --status=staged"
      # - 'another-command'

hasher:
  optimization: "performance"

runner:
  cacheLifetime: "24 hours"

# Require a specific version of moon while running commands, otherwise fail.
# versionConstraint: '>=1.0.0'

# Extend and inherit an external configuration file. Must be a valid HTTPS URL or file system path.
# extends: './shared/workspace.yml'

# REQUIRED: A map of all projects found within the workspace, or a list or file system globs.
# When using a map, each entry requires a unique project ID as the map key, and a file system
# path to the project folder as the map value. File paths are relative from the workspace root,
# and cannot reference projects located outside the workspace boundary.
projects:
  globs:
    - "apps/*"
    - "packages/*"

# When enabled, will check for a newer moon version and send anonymous usage data to the
# moonrepo team. This data is used to improve the quality and reliability of the tool.
telemetry: false

# Monorepo package templates to use when generating new projects.
# @see: https://moonrepo.dev/docs/config/workspace#templates
generator:
  templates:
    - "git://github.com/zero-one-group/monorepo#main"
