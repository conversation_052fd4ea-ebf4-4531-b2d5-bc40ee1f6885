// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/auth/login": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "auth"
                ],
                "summary": "Login with user account",
                "parameters": [
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_LoginResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/face-aging/concerns/{id}": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "face-aging"
                ],
                "summary": "Face aging with concern",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Skin analyze ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.FaceAgingConcernRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_FaceAgingConcernResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/machine-sync-log": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "machine-sync-log"
                ],
                "summary": "Get many machine sync log",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationStatsResponse-domain_MachineSyncLog-domain_MachineSyncLogStatCount"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "machine-sync-log"
                ],
                "summary": "Create machine sync log",
                "parameters": [
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.CreateMachineSyncLog"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_MachineSyncLog"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/media/s3/upload/presign-url": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "media"
                ],
                "summary": "Upload media file to AWS S3",
                "parameters": [
                    {
                        "description": "Info file",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.MediaS3RequestPresignUrl"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_MediaS3Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/parameter-skin-evaluation": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "parameter-skin-evaluation"
                ],
                "summary": "Get many parameter skin evaluation detail",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_ParameterSkinEvaluation"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/parameter-skin-evaluation/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "parameter-skin-evaluation"
                ],
                "summary": "Get parameter skin evaluation detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Parameter Skin Evaluation ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_ParameterSkinEvaluation"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "parameter-skin-evaluation"
                ],
                "summary": "Update parameter skin evaluation detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Parameter Skin Evaluation ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.ParameterSkinEvaluationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_ParameterSkinEvaluation"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/recommendation-treatment/{id}": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "recommendation"
                ],
                "summary": "Recommendation treatment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Skin analyze ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_RecommendationTreatmentMLResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/recommendation/{id}": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "recommendation"
                ],
                "summary": "Get recommendation by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Skin Analyze ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_RecommendationResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/skin-analyze": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-analyze"
                ],
                "summary": "Get many skin analyzes",
                "parameters": [
                    {
                        "type": "string",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "id",
                            "name",
                            "created_at",
                            "updated_at"
                        ],
                        "type": "string",
                        "name": "sort_column",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "name": "sort_order",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_SkinAnalyze"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/skin-analyze/upload": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-analyze"
                ],
                "summary": "Upload skin analyze image",
                "parameters": [
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.SkinAnalyzeUploadRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SkinAnalyze"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/skin-analyze/{id}": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-analyze"
                ],
                "summary": "Get skin analyze by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Skin Analyze ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SkinAnalyze"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/skin-detection/pores/{id}": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-detection"
                ],
                "summary": "Get pore lines",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Skin Analyze ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "enum": [
                            "pores",
                            "acne",
                            "scar",
                            "pigment",
                            "wrinkles",
                            "sensitive",
                            "beautify"
                        ],
                        "type": "string",
                        "x-enum-varnames": [
                            "FaceAgingConcernPore",
                            "FaceAgingConcernAcne",
                            "FaceAgingConcernScar",
                            "FaceAgingConcernPigmentation",
                            "FaceAgingConcernWrinkle",
                            "FaceAgingConcernSensitive",
                            "FaceAgingConcernBeautify"
                        ],
                        "name": "concern",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SkinDetectionResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/skin-detection/wrinkles/{id}": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-detection"
                ],
                "summary": "Get wrinkle lines",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Skin Analyze ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "enum": [
                            "pores",
                            "acne",
                            "scar",
                            "pigment",
                            "wrinkles",
                            "sensitive",
                            "beautify"
                        ],
                        "type": "string",
                        "x-enum-varnames": [
                            "FaceAgingConcernPore",
                            "FaceAgingConcernAcne",
                            "FaceAgingConcernScar",
                            "FaceAgingConcernPigmentation",
                            "FaceAgingConcernWrinkle",
                            "FaceAgingConcernSensitive",
                            "FaceAgingConcernBeautify"
                        ],
                        "name": "concern",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SkinDetectionResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/skin-problem": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-problem"
                ],
                "summary": "Get many skin problem detail",
                "parameters": [
                    {
                        "type": "string",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "special",
                            "general"
                        ],
                        "type": "string",
                        "x-enum-varnames": [
                            "SkinProblemSpecial",
                            "SkinProblemGeneral"
                        ],
                        "name": "type",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_SkinProblemResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_Empty"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-problem"
                ],
                "summary": "Create new skin problem (for treatment-product)",
                "parameters": [
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.SkinProblemRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SkinProblemResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/skin-problem-indication": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-problem-indication"
                ],
                "summary": "Get many skin problem indication detail",
                "parameters": [
                    {
                        "type": "string",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_SkinProblemIndication"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_Empty"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-problem-indication"
                ],
                "summary": "Create new skin problem indication",
                "parameters": [
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.SkinProblemIndicationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SkinProblemIndication"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/skin-problem-indication/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-problem-indication"
                ],
                "summary": "Get skin problem indication detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Skin Problem Indication ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SkinProblemIndication"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-problem-indication"
                ],
                "summary": "Update skin problem indication detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Skin Problem Indication ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.SkinProblemIndicationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SkinProblemIndication"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-problem-indication"
                ],
                "summary": "Delete skin problem indication detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Skin Problem Indication ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SkinProblemIndication"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/skin-problem/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-problem"
                ],
                "summary": "Get skin problem detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Skin Problem ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SkinProblemResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-problem"
                ],
                "summary": "Update skin problem detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Skin Problem ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.SkinProblemRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SkinProblemResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "skin-problem"
                ],
                "summary": "Delete skin problem detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Skin Problem ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SkinProblem"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/summary/{id}": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "summary"
                ],
                "summary": "Get summary by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Skin Analyze ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SummaryResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/survey": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "survey"
                ],
                "summary": "Get many survey detail",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "example": [
                            "contraindication"
                        ],
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "example": [
                            "primary"
                        ],
                        "name": "groups",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "name": "is_static",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "question",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "id",
                            "question",
                            "category",
                            "group",
                            "mobile"
                        ],
                        "type": "string",
                        "name": "sort_column",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "name": "sort_order",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "example": [
                            "single_full"
                        ],
                        "name": "type",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_SurveyResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_Empty"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "survey"
                ],
                "summary": "Create new survey",
                "parameters": [
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.SurveyRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SurveyResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/survey/nested": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "survey"
                ],
                "summary": "Get many survey parent detail with child questions",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "name": "is_static",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "question",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "id",
                            "question",
                            "category",
                            "mobile"
                        ],
                        "type": "string",
                        "name": "sort_column",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "name": "sort_order",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "name": "type",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_SurveyResponseNested"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_Empty"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "survey"
                ],
                "summary": "Create new survey with child questions",
                "parameters": [
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.SurveyRequestNested"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SurveyResponseNested"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/survey/nested/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "survey"
                ],
                "summary": "Get survey detail by id with child questions",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Survey ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SurveyResponseNested"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "survey"
                ],
                "summary": "Update survey detail by id with child questions",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Survey ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.SurveyRequestNested"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SurveyResponseNested"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/survey/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "survey"
                ],
                "summary": "Get survey detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Survey ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SurveyResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "survey"
                ],
                "summary": "Update survey detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Survey ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.SurveyRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SurveyResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "survey"
                ],
                "summary": "Delete survey detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Survey ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_SurveyResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/treatment-category": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-category"
                ],
                "summary": "Get many treatment category detail",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_TreatmentCategoryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_Empty"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-category"
                ],
                "summary": "Create new treatment category",
                "parameters": [
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.TreatmentCategoryRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_TreatmentCategoryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/treatment-category/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-category"
                ],
                "summary": "Get treatment category detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Treatment Category ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_TreatmentCategoryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-category"
                ],
                "summary": "Update treatment category detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Treatment Category ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.TreatmentCategoryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_TreatmentCategoryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-category"
                ],
                "summary": "Delete treatment category detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Treatment Category ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_TreatmentCategoryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/treatment-interval": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-interval"
                ],
                "summary": "Get many treatment interval detail",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_TreatmentIntervalResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_Empty"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-interval"
                ],
                "summary": "Create new treatment interval",
                "parameters": [
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.TreatmentIntervalRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_TreatmentIntervalResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/treatment-interval/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-interval"
                ],
                "summary": "Get treatment interval detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Treatment Interval ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_TreatmentIntervalResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-interval"
                ],
                "summary": "Update treatment interval detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Treatment Interval ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.TreatmentIntervalRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_TreatmentIntervalResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-interval"
                ],
                "summary": "Delete treatment interval detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Treatment Interval ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_TreatmentIntervalResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/treatment-product": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-product"
                ],
                "summary": "Get many treatment or product detail",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "name": "category_ids",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "name": "is_top_recommendation",
                        "in": "query"
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "name": "max_price",
                        "in": "query"
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "name": "min_price",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "id",
                            "name",
                            "type",
                            "price",
                            "created_at",
                            "updated_at",
                            "top_recommendation_name"
                        ],
                        "type": "string",
                        "name": "sort_column",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "name": "sort_order",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "example": [
                            "treatment"
                        ],
                        "name": "types",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_TreatmentProductGetMany"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_Empty"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-product"
                ],
                "summary": "Create new treatment or product",
                "parameters": [
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.TreatmentProductRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_TreatmentProductResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/treatment-product/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-product"
                ],
                "summary": "Get treatment or product detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Treatment Product ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_TreatmentProductResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-product"
                ],
                "summary": "Update treatment or product detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Treatment Product ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.TreatmentProductRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_TreatmentProductResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "treatment-product"
                ],
                "summary": "Delete treatment or product detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Treatment Product ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_TreatmentProduct"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/user": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "Get many user detail",
                "parameters": [
                    {
                        "type": "string",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "example": [
                            "admin"
                        ],
                        "name": "roles",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_UserResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_Empty"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_Empty"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "Create new user",
                "parameters": [
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.UserRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_UserResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/user-survey": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user-survey"
                ],
                "summary": "Get many user survey detail",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                        "name": "skin_analyze_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                        "name": "user_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_UserSurvey"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.PaginationResponse-domain_Empty"
                        }
                    }
                }
            },
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user-survey"
                ],
                "summary": "Create new user survey",
                "parameters": [
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.UserSurveyRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_UserSurvey"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/user-survey/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user-survey"
                ],
                "summary": "Get user survey detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User Survey ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_UserSurvey"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/user/{id}": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "Get user detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_UserResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "Update user detail by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.UserRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_UserResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "Delete user by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_UserResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/user/{id}/update-password": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "Update user password by id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Request body",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.UpdatePasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/domain.SingleResponse-domain_Empty"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "domain.CreateMachineSyncLog": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "object"
                }
            }
        },
        "domain.Empty": {
            "type": "object"
        },
        "domain.FaceAgingArea": {
            "type": "string",
            "enum": [
                "upper",
                "mid",
                "lower"
            ],
            "x-enum-varnames": [
                "FaceAgingAreaUpper",
                "FaceAgingAreaMid",
                "FaceAgingAreaLower"
            ]
        },
        "domain.FaceAgingConcern": {
            "type": "string",
            "enum": [
                "pores",
                "acne",
                "scar",
                "pigment",
                "wrinkles",
                "sensitive",
                "beautify"
            ],
            "x-enum-varnames": [
                "FaceAgingConcernPore",
                "FaceAgingConcernAcne",
                "FaceAgingConcernScar",
                "FaceAgingConcernPigmentation",
                "FaceAgingConcernWrinkle",
                "FaceAgingConcernSensitive",
                "FaceAgingConcernBeautify"
            ]
        },
        "domain.FaceAgingConcernDetailMLRequest": {
            "type": "object",
            "properties": {
                "areas": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.FaceAgingArea"
                    }
                },
                "concern": {
                    "$ref": "#/definitions/domain.FaceAgingConcern"
                }
            }
        },
        "domain.FaceAgingConcernRequest": {
            "type": "object",
            "properties": {
                "concerns": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.FaceAgingConcernDetailMLRequest"
                    }
                },
                "is_beautify": {
                    "type": "boolean"
                }
            }
        },
        "domain.FaceAgingConcernResponse": {
            "type": "object",
            "properties": {
                "generated_images": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.FaceAgingGeneratedMLResponse"
                    }
                }
            }
        },
        "domain.FaceAgingGeneratedMLResponse": {
            "type": "object",
            "properties": {
                "concern": {
                    "$ref": "#/definitions/domain.FaceAgingConcern"
                },
                "generated_image_url": {
                    "type": "string"
                },
                "selected_area_url": {
                    "type": "string"
                }
            }
        },
        "domain.Gender": {
            "type": "string",
            "enum": [
                "male",
                "female"
            ],
            "x-enum-varnames": [
                "Male",
                "Female"
            ]
        },
        "domain.LoginRequest": {
            "type": "object",
            "required": [
                "email",
                "password"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "password": {
                    "type": "string",
                    "minLength": 8
                }
            }
        },
        "domain.LoginResponse": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string"
                }
            }
        },
        "domain.MachineSyncLog": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "integer"
                },
                "data": {
                    "type": "object"
                },
                "id": {
                    "type": "string"
                }
            }
        },
        "domain.MachineSyncLogStatCount": {
            "type": "object",
            "properties": {
                "error_count": {
                    "type": "integer"
                },
                "success_count": {
                    "type": "integer"
                }
            }
        },
        "domain.MediaS3RequestPresignUrl": {
            "type": "object",
            "required": [
                "filename"
            ],
            "properties": {
                "filename": {
                    "type": "string",
                    "example": "file.jpg"
                }
            }
        },
        "domain.MediaS3Response": {
            "type": "object",
            "properties": {
                "url": {
                    "type": "string"
                }
            }
        },
        "domain.PaginationData-domain_Empty": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.Empty"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total_data": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "domain.PaginationData-domain_ParameterSkinEvaluation": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.ParameterSkinEvaluation"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total_data": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "domain.PaginationData-domain_SkinAnalyze": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SkinAnalyze"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total_data": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "domain.PaginationData-domain_SkinProblemIndication": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SkinProblemIndication"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total_data": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "domain.PaginationData-domain_SkinProblemResponse": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SkinProblemResponse"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total_data": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "domain.PaginationData-domain_SurveyResponse": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SurveyResponse"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total_data": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "domain.PaginationData-domain_SurveyResponseNested": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SurveyResponseNested"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total_data": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "domain.PaginationData-domain_TreatmentCategoryResponse": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.TreatmentCategoryResponse"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total_data": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "domain.PaginationData-domain_TreatmentIntervalResponse": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.TreatmentIntervalResponse"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total_data": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "domain.PaginationData-domain_TreatmentProductGetMany": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.TreatmentProductGetMany"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total_data": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "domain.PaginationData-domain_UserResponse": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.UserResponse"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total_data": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "domain.PaginationData-domain_UserSurvey": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.UserSurvey"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total_data": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "domain.PaginationResponse-domain_Empty": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.PaginationData-domain_Empty"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.PaginationResponse-domain_ParameterSkinEvaluation": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.PaginationData-domain_ParameterSkinEvaluation"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.PaginationResponse-domain_SkinAnalyze": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.PaginationData-domain_SkinAnalyze"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.PaginationResponse-domain_SkinProblemIndication": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.PaginationData-domain_SkinProblemIndication"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.PaginationResponse-domain_SkinProblemResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.PaginationData-domain_SkinProblemResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.PaginationResponse-domain_SurveyResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.PaginationData-domain_SurveyResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.PaginationResponse-domain_SurveyResponseNested": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.PaginationData-domain_SurveyResponseNested"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.PaginationResponse-domain_TreatmentCategoryResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.PaginationData-domain_TreatmentCategoryResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.PaginationResponse-domain_TreatmentIntervalResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.PaginationData-domain_TreatmentIntervalResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.PaginationResponse-domain_TreatmentProductGetMany": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.PaginationData-domain_TreatmentProductGetMany"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.PaginationResponse-domain_UserResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.PaginationData-domain_UserResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.PaginationResponse-domain_UserSurvey": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.PaginationData-domain_UserSurvey"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.PaginationStatsData-domain_MachineSyncLog-domain_MachineSyncLogStatCount": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.MachineSyncLog"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "stats": {
                    "$ref": "#/definitions/domain.MachineSyncLogStatCount"
                },
                "total_data": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "domain.PaginationStatsResponse-domain_MachineSyncLog-domain_MachineSyncLogStatCount": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.PaginationStatsData-domain_MachineSyncLog-domain_MachineSyncLogStatCount"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.ParameterSkinEvaluation": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "lower_point": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "parameter_order": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                },
                "upper_point": {
                    "type": "integer"
                }
            }
        },
        "domain.ParameterSkinEvaluationRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "lower_point": {
                    "type": "integer",
                    "minimum": 0
                },
                "name": {
                    "type": "string"
                },
                "upper_point": {
                    "type": "integer",
                    "minimum": 0
                }
            }
        },
        "domain.RecommendationResponse": {
            "type": "object",
            "properties": {
                "summary": {
                    "type": "string"
                },
                "treatments": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.RecommendedTreatment"
                    }
                },
                "user_info": {
                    "$ref": "#/definitions/domain.UserInfo"
                }
            }
        },
        "domain.RecommendationTreatmentMLResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.Treatment"
                    }
                },
                "text": {
                    "type": "string"
                },
                "video": {
                    "type": "string"
                }
            }
        },
        "domain.RecommendedTreatment": {
            "type": "object",
            "properties": {
                "categories": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "description": {
                    "type": "string"
                },
                "is_top_recommendation": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "price": {
                    "type": "integer"
                },
                "quantity": {
                    "type": "integer"
                },
                "solved_concerns": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "domain.SingleResponse-domain_Empty": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.Empty"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_FaceAgingConcernResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.FaceAgingConcernResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_LoginResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.LoginResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_MachineSyncLog": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.MachineSyncLog"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_MediaS3Response": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.MediaS3Response"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_ParameterSkinEvaluation": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.ParameterSkinEvaluation"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_RecommendationResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.RecommendationResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_RecommendationTreatmentMLResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.RecommendationTreatmentMLResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_SkinAnalyze": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.SkinAnalyze"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_SkinDetectionResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.SkinDetectionResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_SkinProblem": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.SkinProblem"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_SkinProblemIndication": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.SkinProblemIndication"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_SkinProblemResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.SkinProblemResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_SummaryResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.SummaryResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_SurveyResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.SurveyResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_SurveyResponseNested": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.SurveyResponseNested"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_TreatmentCategoryResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.TreatmentCategoryResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_TreatmentIntervalResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.TreatmentIntervalResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_TreatmentProduct": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.TreatmentProduct"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_TreatmentProductResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.TreatmentProductResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_UserResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.UserResponse"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SingleResponse-domain_UserSurvey": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/domain.UserSurvey"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "domain.SkinAnalyze": {
            "type": "object",
            "properties": {
                "actual_age": {
                    "type": "integer"
                },
                "brown_area": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "evaluation_rate": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "input_date": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "path_images": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "path_pdf": {
                    "type": "string"
                },
                "phone_number": {
                    "type": "string"
                },
                "pl_texture": {
                    "type": "integer"
                },
                "rgb_pore": {
                    "type": "integer"
                },
                "rgb_spot": {
                    "type": "integer"
                },
                "rgb_wrinkle": {
                    "type": "integer"
                },
                "sensitive_area": {
                    "type": "integer"
                },
                "skin_age": {
                    "type": "integer"
                },
                "skin_condition": {
                    "type": "string"
                },
                "suggestion": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                },
                "uv_damage": {
                    "type": "integer"
                },
                "uv_moisture": {
                    "type": "integer"
                },
                "uv_pigmentation": {
                    "type": "integer"
                },
                "uv_porphyrin": {
                    "type": "integer"
                }
            }
        },
        "domain.SkinAnalyzeUploadRequest": {
            "type": "object",
            "properties": {
                "images": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "pdf": {
                    "type": "string"
                }
            }
        },
        "domain.SkinDetectionResponse": {
            "type": "object",
            "properties": {
                "generated_image_url": {
                    "type": "string"
                }
            }
        },
        "domain.SkinProblem": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.SkinProblemIndication": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.SkinProblemIndicationRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "name": {
                    "type": "string",
                    "example": "Brown Area"
                }
            }
        },
        "domain.SkinProblemRequest": {
            "type": "object",
            "required": [
                "name",
                "skin_problem_indication_ids"
            ],
            "properties": {
                "name": {
                    "type": "string",
                    "example": "Acne"
                },
                "skin_problem_indication_ids": {
                    "type": "array",
                    "maxItems": 4,
                    "uniqueItems": true,
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "domain.SkinProblemResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "id": {
                    "type": "string",
                    "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
                },
                "name": {
                    "type": "string",
                    "example": "Acne"
                },
                "skin_problem_indications": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SkinProblemIndication"
                    }
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.SkinProblemType": {
            "type": "string",
            "enum": [
                "special",
                "general"
            ],
            "x-enum-varnames": [
                "SkinProblemSpecial",
                "SkinProblemGeneral"
            ]
        },
        "domain.SummaryResponse": {
            "type": "object",
            "properties": {
                "summary": {
                    "type": "string"
                }
            }
        },
        "domain.SurveyAnswer": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "image_url": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "domain.SurveyCategory": {
            "type": "string",
            "enum": [
                "contraindication",
                "informational"
            ],
            "x-enum-varnames": [
                "Contraindication",
                "Informational"
            ]
        },
        "domain.SurveyRequest": {
            "type": "object",
            "required": [
                "answers",
                "category",
                "description",
                "question",
                "type"
            ],
            "properties": {
                "answers": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SurveyAnswer"
                    }
                },
                "category": {
                    "enum": [
                        "informational",
                        "contraindication"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/domain.SurveyCategory"
                        }
                    ]
                },
                "description": {
                    "type": "string"
                },
                "is_multiple": {
                    "type": "boolean"
                },
                "parent_question_answer": {
                    "type": "integer",
                    "minimum": 0
                },
                "parent_question_id": {
                    "type": "string",
                    "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
                },
                "question": {
                    "type": "string"
                },
                "question_order": {
                    "type": "integer",
                    "minimum": 0
                },
                "type": {
                    "$ref": "#/definitions/domain.SurveyType"
                }
            }
        },
        "domain.SurveyRequestChildQuestion": {
            "type": "object",
            "required": [
                "answers",
                "category",
                "description",
                "parent_question_answer",
                "question",
                "type"
            ],
            "properties": {
                "answers": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SurveyAnswer"
                    }
                },
                "category": {
                    "enum": [
                        "informational",
                        "contraindication"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/domain.SurveyCategory"
                        }
                    ]
                },
                "description": {
                    "type": "string"
                },
                "is_multiple": {
                    "type": "boolean"
                },
                "parent_question_answer": {
                    "type": "integer",
                    "minimum": 0
                },
                "question": {
                    "type": "string"
                },
                "question_order": {
                    "type": "integer",
                    "minimum": 0
                },
                "type": {
                    "$ref": "#/definitions/domain.SurveyType"
                }
            }
        },
        "domain.SurveyRequestNested": {
            "type": "object",
            "required": [
                "answers",
                "category",
                "description",
                "question",
                "type"
            ],
            "properties": {
                "answers": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SurveyAnswer"
                    }
                },
                "category": {
                    "enum": [
                        "informational",
                        "contraindication"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/domain.SurveyCategory"
                        }
                    ]
                },
                "child_questions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SurveyRequestChildQuestion"
                    }
                },
                "description": {
                    "type": "string"
                },
                "is_multiple": {
                    "type": "boolean"
                },
                "question": {
                    "type": "string"
                },
                "question_order": {
                    "type": "integer",
                    "minimum": 0
                },
                "type": {
                    "$ref": "#/definitions/domain.SurveyType"
                }
            }
        },
        "domain.SurveyResponse": {
            "type": "object",
            "properties": {
                "answers": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SurveyAnswer"
                    }
                },
                "category": {
                    "$ref": "#/definitions/domain.SurveyCategory"
                },
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string",
                    "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
                },
                "is_multiple": {
                    "type": "boolean"
                },
                "is_static": {
                    "type": "boolean"
                },
                "parent_question_answer": {
                    "type": "integer"
                },
                "parent_question_id": {
                    "type": "string"
                },
                "question": {
                    "type": "string"
                },
                "question_order": {
                    "type": "integer"
                },
                "type": {
                    "$ref": "#/definitions/domain.SurveyType"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.SurveyResponseNested": {
            "type": "object",
            "properties": {
                "answers": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SurveyAnswer"
                    }
                },
                "category": {
                    "$ref": "#/definitions/domain.SurveyCategory"
                },
                "child_questions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SurveyResponse"
                    }
                },
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string",
                    "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
                },
                "is_multiple": {
                    "type": "boolean"
                },
                "is_static": {
                    "type": "boolean"
                },
                "parent_question_answer": {
                    "type": "integer"
                },
                "parent_question_id": {
                    "type": "string"
                },
                "question": {
                    "type": "string"
                },
                "question_order": {
                    "type": "integer"
                },
                "type": {
                    "$ref": "#/definitions/domain.SurveyType"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.SurveyType": {
            "type": "string",
            "enum": [
                "single_full",
                "multiple_full",
                "horizontal_bar",
                "text",
                "dropdown"
            ],
            "x-enum-varnames": [
                "SingleFull",
                "MultipleFull",
                "HorizontalBar",
                "Text",
                "SurveyDropdown"
            ]
        },
        "domain.Treatment": {
            "type": "object",
            "properties": {
                "concern": {
                    "type": "string"
                },
                "concern_group": {
                    "type": "string"
                },
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "price": {
                    "type": "integer"
                },
                "sku": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.TreatmentCategory": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.TreatmentCategoryRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "name": {
                    "type": "string",
                    "example": "Facial"
                }
            }
        },
        "domain.TreatmentCategoryResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "id": {
                    "type": "string",
                    "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
                },
                "name": {
                    "type": "string",
                    "example": "Facial"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.TreatmentInterval": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "days": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.TreatmentIntervalRequest": {
            "type": "object",
            "required": [
                "days"
            ],
            "properties": {
                "days": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 0
                }
            }
        },
        "domain.TreatmentIntervalResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "days": {
                    "type": "integer",
                    "example": 0
                },
                "id": {
                    "type": "string",
                    "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.TreatmentProduct": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "duration_top_recommendation": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "interval_id": {
                    "type": "string"
                },
                "is_top_recommendation": {
                    "type": "boolean"
                },
                "item_code": {
                    "type": "string"
                },
                "media_url": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "notes": {
                    "type": "string"
                },
                "price": {
                    "type": "integer"
                },
                "quantity": {
                    "type": "integer"
                },
                "thumbnail_url": {
                    "type": "string"
                },
                "type": {
                    "$ref": "#/definitions/domain.TreatmentProductType"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.TreatmentProductGetMany": {
            "type": "object",
            "properties": {
                "category": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.TreatmentCategory"
                    }
                },
                "concern": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.TreatmentProductGetManyConcern"
                    }
                },
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "duration_top_recommendation": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "interval": {
                    "$ref": "#/definitions/domain.TreatmentInterval"
                },
                "interval_id": {
                    "type": "string"
                },
                "is_top_recommendation": {
                    "type": "boolean"
                },
                "item_code": {
                    "type": "string"
                },
                "media_url": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "notes": {
                    "type": "string"
                },
                "price": {
                    "type": "integer"
                },
                "quantity": {
                    "type": "integer"
                },
                "survey_questions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.TreatmentProductSurveyQuestion"
                    }
                },
                "thumbnail_url": {
                    "type": "string"
                },
                "type": {
                    "$ref": "#/definitions/domain.TreatmentProductType"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.TreatmentProductGetManyConcern": {
            "type": "object",
            "properties": {
                "concern_indications": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SkinProblemIndication"
                    }
                },
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.TreatmentProductRequest": {
            "type": "object",
            "required": [
                "description",
                "item_code",
                "name",
                "type"
            ],
            "properties": {
                "category_ids": {
                    "type": "array",
                    "maxItems": 3,
                    "uniqueItems": true,
                    "items": {
                        "type": "string"
                    }
                },
                "concern_ids": {
                    "type": "array",
                    "maxItems": 6,
                    "uniqueItems": true,
                    "items": {
                        "type": "string"
                    }
                },
                "description": {
                    "type": "string"
                },
                "duration_top_recommendation": {
                    "type": "integer",
                    "example": 1747776517509
                },
                "interval_id": {
                    "type": "string"
                },
                "is_top_recommendation": {
                    "type": "boolean"
                },
                "item_code": {
                    "type": "string",
                    "example": "SPDT69420"
                },
                "media_url": {
                    "description": "AWS S3 object key",
                    "type": "string",
                    "example": "media/path/to/file"
                },
                "name": {
                    "type": "string"
                },
                "notes": {
                    "type": "string"
                },
                "price": {
                    "type": "integer",
                    "minimum": 0
                },
                "quantity": {
                    "type": "integer",
                    "minimum": 0
                },
                "survey_questions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.TreatmentProductSurveyQuestionInput"
                    }
                },
                "thumbnail_url": {
                    "description": "AWS S3 object key",
                    "type": "string",
                    "example": "media/path/to/file"
                },
                "type": {
                    "$ref": "#/definitions/domain.TreatmentProductType"
                }
            }
        },
        "domain.TreatmentProductResponse": {
            "type": "object",
            "properties": {
                "category": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.TreatmentCategory"
                    }
                },
                "concern": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SkinProblem"
                    }
                },
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "duration_top_recommendation": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "interval": {
                    "$ref": "#/definitions/domain.TreatmentInterval"
                },
                "is_top_recommendation": {
                    "type": "boolean"
                },
                "item_code": {
                    "type": "string"
                },
                "media_url": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "notes": {
                    "type": "string"
                },
                "price": {
                    "type": "integer"
                },
                "quantity": {
                    "type": "integer"
                },
                "survey_questions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.TreatmentProductSurveyQuestion"
                    }
                },
                "thumbnail_url": {
                    "type": "string"
                },
                "type": {
                    "$ref": "#/definitions/domain.TreatmentProductType"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.TreatmentProductSurveyQuestion": {
            "type": "object",
            "properties": {
                "answers": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.SurveyAnswer"
                    }
                },
                "id": {
                    "type": "string"
                },
                "question": {
                    "type": "string"
                },
                "question_order": {
                    "type": "integer"
                },
                "selected_answer": {
                    "type": "integer"
                }
            }
        },
        "domain.TreatmentProductSurveyQuestionInput": {
            "type": "object",
            "properties": {
                "selected_answer": {
                    "type": "integer"
                },
                "survey_question_id": {
                    "type": "string"
                }
            }
        },
        "domain.TreatmentProductType": {
            "type": "string",
            "enum": [
                "treatment",
                "product"
            ],
            "x-enum-varnames": [
                "TreatmentType",
                "ProductType"
            ]
        },
        "domain.UpdatePasswordRequest": {
            "type": "object",
            "required": [
                "password"
            ],
            "properties": {
                "password": {
                    "type": "string",
                    "minLength": 8,
                    "example": "new-password123"
                }
            }
        },
        "domain.UserInfo": {
            "type": "object",
            "properties": {
                "evaluation_rate": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "skin_age": {
                    "type": "integer"
                }
            }
        },
        "domain.UserRequest": {
            "type": "object",
            "required": [
                "email",
                "name",
                "phone_number",
                "role"
            ],
            "properties": {
                "address": {
                    "type": "string",
                    "example": "123 Main St"
                },
                "birth_date": {
                    "type": "integer",
                    "example": 946684800000
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "gender": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/domain.Gender"
                        }
                    ],
                    "example": "male"
                },
                "name": {
                    "type": "string",
                    "example": "John Doe"
                },
                "password": {
                    "type": "string",
                    "minLength": 8,
                    "example": "password123"
                },
                "phone_number": {
                    "type": "string",
                    "example": "081234567890"
                },
                "role": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/domain.UserRole"
                        }
                    ],
                    "example": "admin"
                }
            }
        },
        "domain.UserResponse": {
            "type": "object",
            "properties": {
                "address": {
                    "type": "string",
                    "example": "123 Main St"
                },
                "birth_date": {
                    "type": "integer",
                    "example": 946684800000
                },
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "gender": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/domain.Gender"
                        }
                    ],
                    "example": "male"
                },
                "id": {
                    "type": "string",
                    "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
                },
                "name": {
                    "type": "string",
                    "example": "John Doe"
                },
                "phone_number": {
                    "type": "string",
                    "example": "081234567890"
                },
                "role": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/domain.UserRole"
                        }
                    ],
                    "example": "admin"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "domain.UserRole": {
            "type": "string",
            "enum": [
                "admin",
                "branch",
                "client"
            ],
            "x-enum-varnames": [
                "Admin",
                "Branch",
                "Client"
            ]
        },
        "domain.UserSurvey": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "integer"
                },
                "created_by": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "results": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.UserSurveyResult"
                    }
                },
                "skin_analyze_id": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "integer"
                },
                "updated_by": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "domain.UserSurveyRequest": {
            "type": "object",
            "required": [
                "results"
            ],
            "properties": {
                "results": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.UserSurveyResult"
                    }
                },
                "skin_analyze_id": {
                    "type": "string",
                    "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
                },
                "user_id": {
                    "type": "string",
                    "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
                }
            }
        },
        "domain.UserSurveyResult": {
            "type": "object",
            "properties": {
                "answers": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "question": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "Format value: Bearer xxx",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/",
	Schemes:          []string{"http"},
	Title:            "Aizer API",
	Description:      "This is the API documentation for the Aizer application.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
