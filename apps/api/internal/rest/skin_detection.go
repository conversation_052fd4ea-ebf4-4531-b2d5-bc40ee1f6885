package rest

import (
	internalMiddleware "api/internal/rest/middleware"
	"context"
	"net/http"
	"strings"
	"time"

	"github.com/labstack/echo/v4"

	"api/domain"
	"api/utils"
)

//go:generate mockery
type SkinDetectionService interface {
	DetectWrinkles(
		ctx context.Context,
		id string,
		queryParams *domain.SkinDetectionQueryParams,
	) (*domain.SkinDetectionResponse, error)
	DetectPores(
		ctx context.Context,
		id string,
		queryParams *domain.SkinDetectionQueryParams,
	) (*domain.SkinDetectionResponse, error)
}

type SkinDetectionHandler struct {
	Service SkinDetectionService
}

func NewSkinDetectionHandler(
	e *echo.Group,
	service SkinDetectionService,
) {
	handler := &SkinDetectionHandler{service}

	generationRoutes := e.Group("")
	generationRoutes.Use(internalMiddleware.SetRequestContextWithTimeout(60 * time.Second))

	generationRoutes.GET("/skin-detection/wrinkles/:id", handler.DetectWrinkles)
	generationRoutes.GET("/skin-detection/pores/:id", handler.DetectPores)
}

// @router /api/v1/skin-detection/wrinkles/{id} [get]
// @tags skin-detection
// @summary Get wrinkle lines
// @accept json
// @produce json
// @param id path string true "Skin Analyze ID"
// @param queryParams query domain.SkinDetectionQueryParams true "Query params"
// @success 200 {object} domain.SingleResponse[domain.SkinDetectionResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinDetectionHandler) DetectWrinkles(c echo.Context) (err error) {
	skinAnalyzeID := c.Param("id")

	if skinAnalyzeID == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	queryParams := new(domain.SkinDetectionQueryParams)
	err = c.Bind(queryParams)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Error binding query params: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	response, err := handler.Service.DetectWrinkles(ctx, skinAnalyzeID, queryParams)

	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(
				http.StatusNotFound,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusNotFound,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Get data failed: " + err.Error(),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.SkinDetectionResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *response,
			Message: "Get data successfully",
		},
	)
}

// @router /api/v1/skin-detection/pores/{id} [get]
// @tags skin-detection
// @summary Get pore lines
// @accept json
// @produce json
// @param id path string true "Skin Analyze ID"
// @param queryParams query domain.SkinDetectionQueryParams true "Query params"
// @success 200 {object} domain.SingleResponse[domain.SkinDetectionResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinDetectionHandler) DetectPores(c echo.Context) (err error) {
	skinAnalyzeID := c.Param("id")

	if skinAnalyzeID == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	queryParams := new(domain.SkinDetectionQueryParams)
	err = c.Bind(queryParams)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Error binding query params: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	response, err := handler.Service.DetectPores(ctx, skinAnalyzeID, queryParams)

	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(
				http.StatusNotFound,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusNotFound,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Get data failed: " + err.Error(),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.SkinDetectionResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *response,
			Message: "Get data successfully",
		},
	)
}
