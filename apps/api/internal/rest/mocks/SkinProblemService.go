// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewSkinProblemService creates a new instance of SkinProblemService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSkinProblemService(t interface {
	mock.TestingT
	Cleanup(func())
}) *SkinProblemService {
	mock := &SkinProblemService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SkinProblemService is an autogenerated mock type for the SkinProblemService type
type SkinProblemService struct {
	mock.Mock
}

type SkinProblemService_Expecter struct {
	mock *mock.Mock
}

func (_m *SkinProblemService) EXPECT() *SkinProblemService_Expecter {
	return &SkinProblemService_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type SkinProblemService
func (_mock *SkinProblemService) Create(ctx context.Context, request *domain.SkinProblemRequest) (*domain.SkinProblem, []domain.SkinProblemIndication, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *domain.SkinProblem
	var r1 []domain.SkinProblemIndication
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemRequest) (*domain.SkinProblem, []domain.SkinProblemIndication, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemRequest) *domain.SkinProblem); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinProblem)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinProblemRequest) []domain.SkinProblemIndication); ok {
		r1 = returnFunc(ctx, request)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]domain.SkinProblemIndication)
		}
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.SkinProblemRequest) error); ok {
		r2 = returnFunc(ctx, request)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// SkinProblemService_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type SkinProblemService_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *SkinProblemService_Expecter) Create(ctx interface{}, request interface{}) *SkinProblemService_Create_Call {
	return &SkinProblemService_Create_Call{Call: _e.mock.On("Create", ctx, request)}
}

func (_c *SkinProblemService_Create_Call) Run(run func(ctx context.Context, request *domain.SkinProblemRequest)) *SkinProblemService_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinProblemRequest))
	})
	return _c
}

func (_c *SkinProblemService_Create_Call) Return(skinProblem *domain.SkinProblem, skinProblemIndications []domain.SkinProblemIndication, err error) *SkinProblemService_Create_Call {
	_c.Call.Return(skinProblem, skinProblemIndications, err)
	return _c
}

func (_c *SkinProblemService_Create_Call) RunAndReturn(run func(ctx context.Context, request *domain.SkinProblemRequest) (*domain.SkinProblem, []domain.SkinProblemIndication, error)) *SkinProblemService_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByID provides a mock function for the type SkinProblemService
func (_mock *SkinProblemService) DeleteByID(ctx context.Context, id *string) (*domain.SkinProblem, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByID")
	}

	var r0 *domain.SkinProblem
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.SkinProblem, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.SkinProblem); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinProblem)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinProblemService_DeleteByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByID'
type SkinProblemService_DeleteByID_Call struct {
	*mock.Call
}

// DeleteByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SkinProblemService_Expecter) DeleteByID(ctx interface{}, id interface{}) *SkinProblemService_DeleteByID_Call {
	return &SkinProblemService_DeleteByID_Call{Call: _e.mock.On("DeleteByID", ctx, id)}
}

func (_c *SkinProblemService_DeleteByID_Call) Run(run func(ctx context.Context, id *string)) *SkinProblemService_DeleteByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *SkinProblemService_DeleteByID_Call) Return(skinProblem *domain.SkinProblem, err error) *SkinProblemService_DeleteByID_Call {
	_c.Call.Return(skinProblem, err)
	return _c
}

func (_c *SkinProblemService_DeleteByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.SkinProblem, error)) *SkinProblemService_DeleteByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type SkinProblemService
func (_mock *SkinProblemService) GetByID(ctx context.Context, id *string) (*domain.SkinProblemResponse, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.SkinProblemResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.SkinProblemResponse, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.SkinProblemResponse); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinProblemResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinProblemService_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type SkinProblemService_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SkinProblemService_Expecter) GetByID(ctx interface{}, id interface{}) *SkinProblemService_GetByID_Call {
	return &SkinProblemService_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *SkinProblemService_GetByID_Call) Run(run func(ctx context.Context, id *string)) *SkinProblemService_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *SkinProblemService_GetByID_Call) Return(skinProblemResponse *domain.SkinProblemResponse, err error) *SkinProblemService_GetByID_Call {
	_c.Call.Return(skinProblemResponse, err)
	return _c
}

func (_c *SkinProblemService_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.SkinProblemResponse, error)) *SkinProblemService_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type SkinProblemService
func (_mock *SkinProblemService) GetMany(ctx context.Context, filter *domain.SkinProblemFilter) ([]domain.SkinProblemResponse, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.SkinProblemResponse
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemFilter) ([]domain.SkinProblemResponse, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemFilter) []domain.SkinProblemResponse); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.SkinProblemResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinProblemFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.SkinProblemFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// SkinProblemService_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type SkinProblemService_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *SkinProblemService_Expecter) GetMany(ctx interface{}, filter interface{}) *SkinProblemService_GetMany_Call {
	return &SkinProblemService_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *SkinProblemService_GetMany_Call) Run(run func(ctx context.Context, filter *domain.SkinProblemFilter)) *SkinProblemService_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinProblemFilter))
	})
	return _c
}

func (_c *SkinProblemService_GetMany_Call) Return(skinProblemResponses []domain.SkinProblemResponse, n int, err error) *SkinProblemService_GetMany_Call {
	_c.Call.Return(skinProblemResponses, n, err)
	return _c
}

func (_c *SkinProblemService_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.SkinProblemFilter) ([]domain.SkinProblemResponse, int, error)) *SkinProblemService_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type SkinProblemService
func (_mock *SkinProblemService) UpdateByID(ctx context.Context, id *string, request *domain.SkinProblemRequest) (*domain.SkinProblem, []domain.SkinProblemIndication, error) {
	ret := _mock.Called(ctx, id, request)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 *domain.SkinProblem
	var r1 []domain.SkinProblemIndication
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.SkinProblemRequest) (*domain.SkinProblem, []domain.SkinProblemIndication, error)); ok {
		return returnFunc(ctx, id, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.SkinProblemRequest) *domain.SkinProblem); ok {
		r0 = returnFunc(ctx, id, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinProblem)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *domain.SkinProblemRequest) []domain.SkinProblemIndication); ok {
		r1 = returnFunc(ctx, id, request)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]domain.SkinProblemIndication)
		}
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *string, *domain.SkinProblemRequest) error); ok {
		r2 = returnFunc(ctx, id, request)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// SkinProblemService_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type SkinProblemService_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - id
//   - request
func (_e *SkinProblemService_Expecter) UpdateByID(ctx interface{}, id interface{}, request interface{}) *SkinProblemService_UpdateByID_Call {
	return &SkinProblemService_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, id, request)}
}

func (_c *SkinProblemService_UpdateByID_Call) Run(run func(ctx context.Context, id *string, request *domain.SkinProblemRequest)) *SkinProblemService_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*domain.SkinProblemRequest))
	})
	return _c
}

func (_c *SkinProblemService_UpdateByID_Call) Return(skinProblem *domain.SkinProblem, skinProblemIndications []domain.SkinProblemIndication, err error) *SkinProblemService_UpdateByID_Call {
	_c.Call.Return(skinProblem, skinProblemIndications, err)
	return _c
}

func (_c *SkinProblemService_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, id *string, request *domain.SkinProblemRequest) (*domain.SkinProblem, []domain.SkinProblemIndication, error)) *SkinProblemService_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}
