// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewSkinDetectionService creates a new instance of SkinDetectionService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSkinDetectionService(t interface {
	mock.TestingT
	Cleanup(func())
}) *SkinDetectionService {
	mock := &SkinDetectionService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SkinDetectionService is an autogenerated mock type for the SkinDetectionService type
type SkinDetectionService struct {
	mock.Mock
}

type SkinDetectionService_Expecter struct {
	mock *mock.Mock
}

func (_m *SkinDetectionService) EXPECT() *SkinDetectionService_Expecter {
	return &SkinDetectionService_Expecter{mock: &_m.Mock}
}

// DetectPores provides a mock function for the type SkinDetectionService
func (_mock *SkinDetectionService) DetectPores(ctx context.Context, id string, queryParams *domain.SkinDetectionQueryParams) (*domain.SkinDetectionResponse, error) {
	ret := _mock.Called(ctx, id, queryParams)

	if len(ret) == 0 {
		panic("no return value specified for DetectPores")
	}

	var r0 *domain.SkinDetectionResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.SkinDetectionQueryParams) (*domain.SkinDetectionResponse, error)); ok {
		return returnFunc(ctx, id, queryParams)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.SkinDetectionQueryParams) *domain.SkinDetectionResponse); ok {
		r0 = returnFunc(ctx, id, queryParams)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinDetectionResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *domain.SkinDetectionQueryParams) error); ok {
		r1 = returnFunc(ctx, id, queryParams)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinDetectionService_DetectPores_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DetectPores'
type SkinDetectionService_DetectPores_Call struct {
	*mock.Call
}

// DetectPores is a helper method to define mock.On call
//   - ctx
//   - id
//   - queryParams
func (_e *SkinDetectionService_Expecter) DetectPores(ctx interface{}, id interface{}, queryParams interface{}) *SkinDetectionService_DetectPores_Call {
	return &SkinDetectionService_DetectPores_Call{Call: _e.mock.On("DetectPores", ctx, id, queryParams)}
}

func (_c *SkinDetectionService_DetectPores_Call) Run(run func(ctx context.Context, id string, queryParams *domain.SkinDetectionQueryParams)) *SkinDetectionService_DetectPores_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*domain.SkinDetectionQueryParams))
	})
	return _c
}

func (_c *SkinDetectionService_DetectPores_Call) Return(skinDetectionResponse *domain.SkinDetectionResponse, err error) *SkinDetectionService_DetectPores_Call {
	_c.Call.Return(skinDetectionResponse, err)
	return _c
}

func (_c *SkinDetectionService_DetectPores_Call) RunAndReturn(run func(ctx context.Context, id string, queryParams *domain.SkinDetectionQueryParams) (*domain.SkinDetectionResponse, error)) *SkinDetectionService_DetectPores_Call {
	_c.Call.Return(run)
	return _c
}

// DetectWrinkles provides a mock function for the type SkinDetectionService
func (_mock *SkinDetectionService) DetectWrinkles(ctx context.Context, id string, queryParams *domain.SkinDetectionQueryParams) (*domain.SkinDetectionResponse, error) {
	ret := _mock.Called(ctx, id, queryParams)

	if len(ret) == 0 {
		panic("no return value specified for DetectWrinkles")
	}

	var r0 *domain.SkinDetectionResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.SkinDetectionQueryParams) (*domain.SkinDetectionResponse, error)); ok {
		return returnFunc(ctx, id, queryParams)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.SkinDetectionQueryParams) *domain.SkinDetectionResponse); ok {
		r0 = returnFunc(ctx, id, queryParams)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinDetectionResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *domain.SkinDetectionQueryParams) error); ok {
		r1 = returnFunc(ctx, id, queryParams)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinDetectionService_DetectWrinkles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DetectWrinkles'
type SkinDetectionService_DetectWrinkles_Call struct {
	*mock.Call
}

// DetectWrinkles is a helper method to define mock.On call
//   - ctx
//   - id
//   - queryParams
func (_e *SkinDetectionService_Expecter) DetectWrinkles(ctx interface{}, id interface{}, queryParams interface{}) *SkinDetectionService_DetectWrinkles_Call {
	return &SkinDetectionService_DetectWrinkles_Call{Call: _e.mock.On("DetectWrinkles", ctx, id, queryParams)}
}

func (_c *SkinDetectionService_DetectWrinkles_Call) Run(run func(ctx context.Context, id string, queryParams *domain.SkinDetectionQueryParams)) *SkinDetectionService_DetectWrinkles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*domain.SkinDetectionQueryParams))
	})
	return _c
}

func (_c *SkinDetectionService_DetectWrinkles_Call) Return(skinDetectionResponse *domain.SkinDetectionResponse, err error) *SkinDetectionService_DetectWrinkles_Call {
	_c.Call.Return(skinDetectionResponse, err)
	return _c
}

func (_c *SkinDetectionService_DetectWrinkles_Call) RunAndReturn(run func(ctx context.Context, id string, queryParams *domain.SkinDetectionQueryParams) (*domain.SkinDetectionResponse, error)) *SkinDetectionService_DetectWrinkles_Call {
	_c.Call.Return(run)
	return _c
}
