// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewMachineSyncLogService creates a new instance of MachineSyncLogService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMachineSyncLogService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MachineSyncLogService {
	mock := &MachineSyncLogService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MachineSyncLogService is an autogenerated mock type for the MachineSyncLogService type
type MachineSyncLogService struct {
	mock.Mock
}

type MachineSyncLogService_Expecter struct {
	mock *mock.Mock
}

func (_m *MachineSyncLogService) EXPECT() *MachineSyncLogService_Expecter {
	return &MachineSyncLogService_Expecter{mock: &_m.Mock}
}

// CreateMachineSyncLog provides a mock function for the type MachineSyncLogService
func (_mock *MachineSyncLogService) CreateMachineSyncLog(ctx context.Context, data *domain.MachineSyncLog) (*domain.MachineSyncLog, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for CreateMachineSyncLog")
	}

	var r0 *domain.MachineSyncLog
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.MachineSyncLog) (*domain.MachineSyncLog, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.MachineSyncLog) *domain.MachineSyncLog); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.MachineSyncLog)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.MachineSyncLog) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MachineSyncLogService_CreateMachineSyncLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateMachineSyncLog'
type MachineSyncLogService_CreateMachineSyncLog_Call struct {
	*mock.Call
}

// CreateMachineSyncLog is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *MachineSyncLogService_Expecter) CreateMachineSyncLog(ctx interface{}, data interface{}) *MachineSyncLogService_CreateMachineSyncLog_Call {
	return &MachineSyncLogService_CreateMachineSyncLog_Call{Call: _e.mock.On("CreateMachineSyncLog", ctx, data)}
}

func (_c *MachineSyncLogService_CreateMachineSyncLog_Call) Run(run func(ctx context.Context, data *domain.MachineSyncLog)) *MachineSyncLogService_CreateMachineSyncLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.MachineSyncLog))
	})
	return _c
}

func (_c *MachineSyncLogService_CreateMachineSyncLog_Call) Return(machineSyncLog *domain.MachineSyncLog, err error) *MachineSyncLogService_CreateMachineSyncLog_Call {
	_c.Call.Return(machineSyncLog, err)
	return _c
}

func (_c *MachineSyncLogService_CreateMachineSyncLog_Call) RunAndReturn(run func(ctx context.Context, data *domain.MachineSyncLog) (*domain.MachineSyncLog, error)) *MachineSyncLogService_CreateMachineSyncLog_Call {
	_c.Call.Return(run)
	return _c
}

// GetManyMachineSyncLog provides a mock function for the type MachineSyncLogService
func (_mock *MachineSyncLogService) GetManyMachineSyncLog(ctx context.Context, filter *domain.MachineSyncLogFilter) ([]*domain.MachineSyncLog, *domain.MachineSyncLogStatCount, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetManyMachineSyncLog")
	}

	var r0 []*domain.MachineSyncLog
	var r1 *domain.MachineSyncLogStatCount
	var r2 int
	var r3 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.MachineSyncLogFilter) ([]*domain.MachineSyncLog, *domain.MachineSyncLogStatCount, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.MachineSyncLogFilter) []*domain.MachineSyncLog); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*domain.MachineSyncLog)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.MachineSyncLogFilter) *domain.MachineSyncLogStatCount); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*domain.MachineSyncLogStatCount)
		}
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.MachineSyncLogFilter) int); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Get(2).(int)
	}
	if returnFunc, ok := ret.Get(3).(func(context.Context, *domain.MachineSyncLogFilter) error); ok {
		r3 = returnFunc(ctx, filter)
	} else {
		r3 = ret.Error(3)
	}
	return r0, r1, r2, r3
}

// MachineSyncLogService_GetManyMachineSyncLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetManyMachineSyncLog'
type MachineSyncLogService_GetManyMachineSyncLog_Call struct {
	*mock.Call
}

// GetManyMachineSyncLog is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *MachineSyncLogService_Expecter) GetManyMachineSyncLog(ctx interface{}, filter interface{}) *MachineSyncLogService_GetManyMachineSyncLog_Call {
	return &MachineSyncLogService_GetManyMachineSyncLog_Call{Call: _e.mock.On("GetManyMachineSyncLog", ctx, filter)}
}

func (_c *MachineSyncLogService_GetManyMachineSyncLog_Call) Run(run func(ctx context.Context, filter *domain.MachineSyncLogFilter)) *MachineSyncLogService_GetManyMachineSyncLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.MachineSyncLogFilter))
	})
	return _c
}

func (_c *MachineSyncLogService_GetManyMachineSyncLog_Call) Return(machineSyncLogs []*domain.MachineSyncLog, machineSyncLogStatCount *domain.MachineSyncLogStatCount, n int, err error) *MachineSyncLogService_GetManyMachineSyncLog_Call {
	_c.Call.Return(machineSyncLogs, machineSyncLogStatCount, n, err)
	return _c
}

func (_c *MachineSyncLogService_GetManyMachineSyncLog_Call) RunAndReturn(run func(ctx context.Context, filter *domain.MachineSyncLogFilter) ([]*domain.MachineSyncLog, *domain.MachineSyncLogStatCount, int, error)) *MachineSyncLogService_GetManyMachineSyncLog_Call {
	_c.Call.Return(run)
	return _c
}
