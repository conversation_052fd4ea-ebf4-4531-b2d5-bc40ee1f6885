// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewSummaryService creates a new instance of SummaryService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSummaryService(t interface {
	mock.TestingT
	Cleanup(func())
}) *SummaryService {
	mock := &SummaryService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SummaryService is an autogenerated mock type for the SummaryService type
type SummaryService struct {
	mock.Mock
}

type SummaryService_Expecter struct {
	mock *mock.Mock
}

func (_m *SummaryService) EXPECT() *SummaryService_Expecter {
	return &SummaryService_Expecter{mock: &_m.Mock}
}

// GetSummary provides a mock function for the type SummaryService
func (_mock *SummaryService) GetSummary(ctx context.Context, id string) (*domain.SummaryResponse, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetSummary")
	}

	var r0 *domain.SummaryResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*domain.SummaryResponse, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *domain.SummaryResponse); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SummaryResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SummaryService_GetSummary_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSummary'
type SummaryService_GetSummary_Call struct {
	*mock.Call
}

// GetSummary is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SummaryService_Expecter) GetSummary(ctx interface{}, id interface{}) *SummaryService_GetSummary_Call {
	return &SummaryService_GetSummary_Call{Call: _e.mock.On("GetSummary", ctx, id)}
}

func (_c *SummaryService_GetSummary_Call) Run(run func(ctx context.Context, id string)) *SummaryService_GetSummary_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *SummaryService_GetSummary_Call) Return(summaryResponse *domain.SummaryResponse, err error) *SummaryService_GetSummary_Call {
	_c.Call.Return(summaryResponse, err)
	return _c
}

func (_c *SummaryService_GetSummary_Call) RunAndReturn(run func(ctx context.Context, id string) (*domain.SummaryResponse, error)) *SummaryService_GetSummary_Call {
	_c.Call.Return(run)
	return _c
}
