// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewTreatmentCategoryService creates a new instance of TreatmentCategoryService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTreatmentCategoryService(t interface {
	mock.TestingT
	Cleanup(func())
}) *TreatmentCategoryService {
	mock := &TreatmentCategoryService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// TreatmentCategoryService is an autogenerated mock type for the TreatmentCategoryService type
type TreatmentCategoryService struct {
	mock.Mock
}

type TreatmentCategoryService_Expecter struct {
	mock *mock.Mock
}

func (_m *TreatmentCategoryService) EXPECT() *TreatmentCategoryService_Expecter {
	return &TreatmentCategoryService_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type TreatmentCategoryService
func (_mock *TreatmentCategoryService) Create(ctx context.Context, data *domain.TreatmentCategory) (*domain.TreatmentCategory, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *domain.TreatmentCategory
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentCategory) (*domain.TreatmentCategory, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentCategory) *domain.TreatmentCategory); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentCategory)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentCategory) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentCategoryService_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type TreatmentCategoryService_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *TreatmentCategoryService_Expecter) Create(ctx interface{}, data interface{}) *TreatmentCategoryService_Create_Call {
	return &TreatmentCategoryService_Create_Call{Call: _e.mock.On("Create", ctx, data)}
}

func (_c *TreatmentCategoryService_Create_Call) Run(run func(ctx context.Context, data *domain.TreatmentCategory)) *TreatmentCategoryService_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentCategory))
	})
	return _c
}

func (_c *TreatmentCategoryService_Create_Call) Return(treatmentCategory *domain.TreatmentCategory, err error) *TreatmentCategoryService_Create_Call {
	_c.Call.Return(treatmentCategory, err)
	return _c
}

func (_c *TreatmentCategoryService_Create_Call) RunAndReturn(run func(ctx context.Context, data *domain.TreatmentCategory) (*domain.TreatmentCategory, error)) *TreatmentCategoryService_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByID provides a mock function for the type TreatmentCategoryService
func (_mock *TreatmentCategoryService) DeleteByID(ctx context.Context, id *string) (*domain.TreatmentCategory, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByID")
	}

	var r0 *domain.TreatmentCategory
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.TreatmentCategory, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.TreatmentCategory); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentCategory)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentCategoryService_DeleteByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByID'
type TreatmentCategoryService_DeleteByID_Call struct {
	*mock.Call
}

// DeleteByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *TreatmentCategoryService_Expecter) DeleteByID(ctx interface{}, id interface{}) *TreatmentCategoryService_DeleteByID_Call {
	return &TreatmentCategoryService_DeleteByID_Call{Call: _e.mock.On("DeleteByID", ctx, id)}
}

func (_c *TreatmentCategoryService_DeleteByID_Call) Run(run func(ctx context.Context, id *string)) *TreatmentCategoryService_DeleteByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *TreatmentCategoryService_DeleteByID_Call) Return(treatmentCategory *domain.TreatmentCategory, err error) *TreatmentCategoryService_DeleteByID_Call {
	_c.Call.Return(treatmentCategory, err)
	return _c
}

func (_c *TreatmentCategoryService_DeleteByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.TreatmentCategory, error)) *TreatmentCategoryService_DeleteByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type TreatmentCategoryService
func (_mock *TreatmentCategoryService) GetByID(ctx context.Context, id *string) (*domain.TreatmentCategory, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.TreatmentCategory
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.TreatmentCategory, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.TreatmentCategory); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentCategory)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentCategoryService_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type TreatmentCategoryService_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *TreatmentCategoryService_Expecter) GetByID(ctx interface{}, id interface{}) *TreatmentCategoryService_GetByID_Call {
	return &TreatmentCategoryService_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *TreatmentCategoryService_GetByID_Call) Run(run func(ctx context.Context, id *string)) *TreatmentCategoryService_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *TreatmentCategoryService_GetByID_Call) Return(treatmentCategory *domain.TreatmentCategory, err error) *TreatmentCategoryService_GetByID_Call {
	_c.Call.Return(treatmentCategory, err)
	return _c
}

func (_c *TreatmentCategoryService_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.TreatmentCategory, error)) *TreatmentCategoryService_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type TreatmentCategoryService
func (_mock *TreatmentCategoryService) GetMany(ctx context.Context, filter *domain.TreatmentCategoryFilter) ([]domain.TreatmentCategory, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.TreatmentCategory
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentCategoryFilter) ([]domain.TreatmentCategory, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentCategoryFilter) []domain.TreatmentCategory); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.TreatmentCategory)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentCategoryFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.TreatmentCategoryFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// TreatmentCategoryService_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type TreatmentCategoryService_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *TreatmentCategoryService_Expecter) GetMany(ctx interface{}, filter interface{}) *TreatmentCategoryService_GetMany_Call {
	return &TreatmentCategoryService_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *TreatmentCategoryService_GetMany_Call) Run(run func(ctx context.Context, filter *domain.TreatmentCategoryFilter)) *TreatmentCategoryService_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentCategoryFilter))
	})
	return _c
}

func (_c *TreatmentCategoryService_GetMany_Call) Return(treatmentCategorys []domain.TreatmentCategory, n int, err error) *TreatmentCategoryService_GetMany_Call {
	_c.Call.Return(treatmentCategorys, n, err)
	return _c
}

func (_c *TreatmentCategoryService_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.TreatmentCategoryFilter) ([]domain.TreatmentCategory, int, error)) *TreatmentCategoryService_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type TreatmentCategoryService
func (_mock *TreatmentCategoryService) UpdateByID(ctx context.Context, input *domain.TreatmentCategory) (*domain.TreatmentCategory, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 *domain.TreatmentCategory
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentCategory) (*domain.TreatmentCategory, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentCategory) *domain.TreatmentCategory); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentCategory)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentCategory) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentCategoryService_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type TreatmentCategoryService_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *TreatmentCategoryService_Expecter) UpdateByID(ctx interface{}, input interface{}) *TreatmentCategoryService_UpdateByID_Call {
	return &TreatmentCategoryService_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, input)}
}

func (_c *TreatmentCategoryService_UpdateByID_Call) Run(run func(ctx context.Context, input *domain.TreatmentCategory)) *TreatmentCategoryService_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentCategory))
	})
	return _c
}

func (_c *TreatmentCategoryService_UpdateByID_Call) Return(treatmentCategory *domain.TreatmentCategory, err error) *TreatmentCategoryService_UpdateByID_Call {
	_c.Call.Return(treatmentCategory, err)
	return _c
}

func (_c *TreatmentCategoryService_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, input *domain.TreatmentCategory) (*domain.TreatmentCategory, error)) *TreatmentCategoryService_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}
