// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewSurveyService creates a new instance of SurveyService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSurveyService(t interface {
	mock.TestingT
	Cleanup(func())
}) *SurveyService {
	mock := &SurveyService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SurveyService is an autogenerated mock type for the SurveyService type
type SurveyService struct {
	mock.Mock
}

type SurveyService_Expecter struct {
	mock *mock.Mock
}

func (_m *SurveyService) EXPECT() *SurveyService_Expecter {
	return &SurveyService_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type SurveyService
func (_mock *SurveyService) Create(ctx context.Context, data *domain.Survey) (*domain.Survey, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *domain.Survey
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.Survey) (*domain.Survey, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.Survey) *domain.Survey); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Survey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.Survey) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyService_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type SurveyService_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *SurveyService_Expecter) Create(ctx interface{}, data interface{}) *SurveyService_Create_Call {
	return &SurveyService_Create_Call{Call: _e.mock.On("Create", ctx, data)}
}

func (_c *SurveyService_Create_Call) Run(run func(ctx context.Context, data *domain.Survey)) *SurveyService_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.Survey))
	})
	return _c
}

func (_c *SurveyService_Create_Call) Return(survey *domain.Survey, err error) *SurveyService_Create_Call {
	_c.Call.Return(survey, err)
	return _c
}

func (_c *SurveyService_Create_Call) RunAndReturn(run func(ctx context.Context, data *domain.Survey) (*domain.Survey, error)) *SurveyService_Create_Call {
	_c.Call.Return(run)
	return _c
}

// CreateNested provides a mock function for the type SurveyService
func (_mock *SurveyService) CreateNested(ctx context.Context, data *domain.SurveyRequestNested) (*domain.SurveyResponseNested, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for CreateNested")
	}

	var r0 *domain.SurveyResponseNested
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SurveyRequestNested) (*domain.SurveyResponseNested, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SurveyRequestNested) *domain.SurveyResponseNested); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SurveyResponseNested)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SurveyRequestNested) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyService_CreateNested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNested'
type SurveyService_CreateNested_Call struct {
	*mock.Call
}

// CreateNested is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *SurveyService_Expecter) CreateNested(ctx interface{}, data interface{}) *SurveyService_CreateNested_Call {
	return &SurveyService_CreateNested_Call{Call: _e.mock.On("CreateNested", ctx, data)}
}

func (_c *SurveyService_CreateNested_Call) Run(run func(ctx context.Context, data *domain.SurveyRequestNested)) *SurveyService_CreateNested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SurveyRequestNested))
	})
	return _c
}

func (_c *SurveyService_CreateNested_Call) Return(surveyResponseNested *domain.SurveyResponseNested, err error) *SurveyService_CreateNested_Call {
	_c.Call.Return(surveyResponseNested, err)
	return _c
}

func (_c *SurveyService_CreateNested_Call) RunAndReturn(run func(ctx context.Context, data *domain.SurveyRequestNested) (*domain.SurveyResponseNested, error)) *SurveyService_CreateNested_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByID provides a mock function for the type SurveyService
func (_mock *SurveyService) DeleteByID(ctx context.Context, id *string) (*domain.Survey, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByID")
	}

	var r0 *domain.Survey
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.Survey, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.Survey); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Survey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyService_DeleteByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByID'
type SurveyService_DeleteByID_Call struct {
	*mock.Call
}

// DeleteByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SurveyService_Expecter) DeleteByID(ctx interface{}, id interface{}) *SurveyService_DeleteByID_Call {
	return &SurveyService_DeleteByID_Call{Call: _e.mock.On("DeleteByID", ctx, id)}
}

func (_c *SurveyService_DeleteByID_Call) Run(run func(ctx context.Context, id *string)) *SurveyService_DeleteByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *SurveyService_DeleteByID_Call) Return(survey *domain.Survey, err error) *SurveyService_DeleteByID_Call {
	_c.Call.Return(survey, err)
	return _c
}

func (_c *SurveyService_DeleteByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.Survey, error)) *SurveyService_DeleteByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type SurveyService
func (_mock *SurveyService) GetByID(ctx context.Context, id *string) (*domain.Survey, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.Survey
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.Survey, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.Survey); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Survey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyService_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type SurveyService_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SurveyService_Expecter) GetByID(ctx interface{}, id interface{}) *SurveyService_GetByID_Call {
	return &SurveyService_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *SurveyService_GetByID_Call) Run(run func(ctx context.Context, id *string)) *SurveyService_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *SurveyService_GetByID_Call) Return(survey *domain.Survey, err error) *SurveyService_GetByID_Call {
	_c.Call.Return(survey, err)
	return _c
}

func (_c *SurveyService_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.Survey, error)) *SurveyService_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByIDNested provides a mock function for the type SurveyService
func (_mock *SurveyService) GetByIDNested(ctx context.Context, id *string) (*domain.SurveyResponseNested, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByIDNested")
	}

	var r0 *domain.SurveyResponseNested
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.SurveyResponseNested, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.SurveyResponseNested); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SurveyResponseNested)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyService_GetByIDNested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByIDNested'
type SurveyService_GetByIDNested_Call struct {
	*mock.Call
}

// GetByIDNested is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SurveyService_Expecter) GetByIDNested(ctx interface{}, id interface{}) *SurveyService_GetByIDNested_Call {
	return &SurveyService_GetByIDNested_Call{Call: _e.mock.On("GetByIDNested", ctx, id)}
}

func (_c *SurveyService_GetByIDNested_Call) Run(run func(ctx context.Context, id *string)) *SurveyService_GetByIDNested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *SurveyService_GetByIDNested_Call) Return(surveyResponseNested *domain.SurveyResponseNested, err error) *SurveyService_GetByIDNested_Call {
	_c.Call.Return(surveyResponseNested, err)
	return _c
}

func (_c *SurveyService_GetByIDNested_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.SurveyResponseNested, error)) *SurveyService_GetByIDNested_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type SurveyService
func (_mock *SurveyService) GetMany(ctx context.Context, filter *domain.SurveyFilter) ([]domain.Survey, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.Survey
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SurveyFilter) ([]domain.Survey, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SurveyFilter) []domain.Survey); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Survey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SurveyFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.SurveyFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// SurveyService_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type SurveyService_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *SurveyService_Expecter) GetMany(ctx interface{}, filter interface{}) *SurveyService_GetMany_Call {
	return &SurveyService_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *SurveyService_GetMany_Call) Run(run func(ctx context.Context, filter *domain.SurveyFilter)) *SurveyService_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SurveyFilter))
	})
	return _c
}

func (_c *SurveyService_GetMany_Call) Return(surveys []domain.Survey, n int, err error) *SurveyService_GetMany_Call {
	_c.Call.Return(surveys, n, err)
	return _c
}

func (_c *SurveyService_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.SurveyFilter) ([]domain.Survey, int, error)) *SurveyService_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// GetManyNested provides a mock function for the type SurveyService
func (_mock *SurveyService) GetManyNested(ctx context.Context, filter *domain.SurveyFilterNested) ([]domain.SurveyResponseNested, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetManyNested")
	}

	var r0 []domain.SurveyResponseNested
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SurveyFilterNested) ([]domain.SurveyResponseNested, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SurveyFilterNested) []domain.SurveyResponseNested); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.SurveyResponseNested)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SurveyFilterNested) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.SurveyFilterNested) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// SurveyService_GetManyNested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetManyNested'
type SurveyService_GetManyNested_Call struct {
	*mock.Call
}

// GetManyNested is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *SurveyService_Expecter) GetManyNested(ctx interface{}, filter interface{}) *SurveyService_GetManyNested_Call {
	return &SurveyService_GetManyNested_Call{Call: _e.mock.On("GetManyNested", ctx, filter)}
}

func (_c *SurveyService_GetManyNested_Call) Run(run func(ctx context.Context, filter *domain.SurveyFilterNested)) *SurveyService_GetManyNested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SurveyFilterNested))
	})
	return _c
}

func (_c *SurveyService_GetManyNested_Call) Return(surveyResponseNesteds []domain.SurveyResponseNested, n int, err error) *SurveyService_GetManyNested_Call {
	_c.Call.Return(surveyResponseNesteds, n, err)
	return _c
}

func (_c *SurveyService_GetManyNested_Call) RunAndReturn(run func(ctx context.Context, filter *domain.SurveyFilterNested) ([]domain.SurveyResponseNested, int, error)) *SurveyService_GetManyNested_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type SurveyService
func (_mock *SurveyService) UpdateByID(ctx context.Context, input *domain.Survey) (*domain.Survey, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 *domain.Survey
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.Survey) (*domain.Survey, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.Survey) *domain.Survey); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Survey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.Survey) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyService_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type SurveyService_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *SurveyService_Expecter) UpdateByID(ctx interface{}, input interface{}) *SurveyService_UpdateByID_Call {
	return &SurveyService_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, input)}
}

func (_c *SurveyService_UpdateByID_Call) Run(run func(ctx context.Context, input *domain.Survey)) *SurveyService_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.Survey))
	})
	return _c
}

func (_c *SurveyService_UpdateByID_Call) Return(survey *domain.Survey, err error) *SurveyService_UpdateByID_Call {
	_c.Call.Return(survey, err)
	return _c
}

func (_c *SurveyService_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, input *domain.Survey) (*domain.Survey, error)) *SurveyService_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByIDNested provides a mock function for the type SurveyService
func (_mock *SurveyService) UpdateByIDNested(ctx context.Context, id *string, data *domain.SurveyRequestNested) (*domain.SurveyResponseNested, error) {
	ret := _mock.Called(ctx, id, data)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByIDNested")
	}

	var r0 *domain.SurveyResponseNested
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.SurveyRequestNested) (*domain.SurveyResponseNested, error)); ok {
		return returnFunc(ctx, id, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.SurveyRequestNested) *domain.SurveyResponseNested); ok {
		r0 = returnFunc(ctx, id, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SurveyResponseNested)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *domain.SurveyRequestNested) error); ok {
		r1 = returnFunc(ctx, id, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyService_UpdateByIDNested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByIDNested'
type SurveyService_UpdateByIDNested_Call struct {
	*mock.Call
}

// UpdateByIDNested is a helper method to define mock.On call
//   - ctx
//   - id
//   - data
func (_e *SurveyService_Expecter) UpdateByIDNested(ctx interface{}, id interface{}, data interface{}) *SurveyService_UpdateByIDNested_Call {
	return &SurveyService_UpdateByIDNested_Call{Call: _e.mock.On("UpdateByIDNested", ctx, id, data)}
}

func (_c *SurveyService_UpdateByIDNested_Call) Run(run func(ctx context.Context, id *string, data *domain.SurveyRequestNested)) *SurveyService_UpdateByIDNested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*domain.SurveyRequestNested))
	})
	return _c
}

func (_c *SurveyService_UpdateByIDNested_Call) Return(surveyResponseNested *domain.SurveyResponseNested, err error) *SurveyService_UpdateByIDNested_Call {
	_c.Call.Return(surveyResponseNested, err)
	return _c
}

func (_c *SurveyService_UpdateByIDNested_Call) RunAndReturn(run func(ctx context.Context, id *string, data *domain.SurveyRequestNested) (*domain.SurveyResponseNested, error)) *SurveyService_UpdateByIDNested_Call {
	_c.Call.Return(run)
	return _c
}
