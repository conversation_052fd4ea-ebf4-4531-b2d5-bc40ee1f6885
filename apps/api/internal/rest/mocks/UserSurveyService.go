// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewUserSurveyService creates a new instance of UserSurveyService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserSurveyService(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserSurveyService {
	mock := &UserSurveyService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// UserSurveyService is an autogenerated mock type for the UserSurveyService type
type UserSurveyService struct {
	mock.Mock
}

type UserSurveyService_Expecter struct {
	mock *mock.Mock
}

func (_m *UserSurveyService) EXPECT() *UserSurveyService_Expecter {
	return &UserSurveyService_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type UserSurveyService
func (_mock *UserSurveyService) Create(ctx context.Context, request *domain.UserSurveyRequest) (*domain.UserSurvey, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *domain.UserSurvey
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.UserSurveyRequest) (*domain.UserSurvey, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.UserSurveyRequest) *domain.UserSurvey); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.UserSurvey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.UserSurveyRequest) error); ok {
		r1 = returnFunc(ctx, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// UserSurveyService_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type UserSurveyService_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *UserSurveyService_Expecter) Create(ctx interface{}, request interface{}) *UserSurveyService_Create_Call {
	return &UserSurveyService_Create_Call{Call: _e.mock.On("Create", ctx, request)}
}

func (_c *UserSurveyService_Create_Call) Run(run func(ctx context.Context, request *domain.UserSurveyRequest)) *UserSurveyService_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.UserSurveyRequest))
	})
	return _c
}

func (_c *UserSurveyService_Create_Call) Return(userSurvey *domain.UserSurvey, err error) *UserSurveyService_Create_Call {
	_c.Call.Return(userSurvey, err)
	return _c
}

func (_c *UserSurveyService_Create_Call) RunAndReturn(run func(ctx context.Context, request *domain.UserSurveyRequest) (*domain.UserSurvey, error)) *UserSurveyService_Create_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type UserSurveyService
func (_mock *UserSurveyService) GetByID(ctx context.Context, id *string) (*domain.UserSurvey, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.UserSurvey
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.UserSurvey, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.UserSurvey); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.UserSurvey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// UserSurveyService_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type UserSurveyService_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *UserSurveyService_Expecter) GetByID(ctx interface{}, id interface{}) *UserSurveyService_GetByID_Call {
	return &UserSurveyService_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *UserSurveyService_GetByID_Call) Run(run func(ctx context.Context, id *string)) *UserSurveyService_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *UserSurveyService_GetByID_Call) Return(userSurvey *domain.UserSurvey, err error) *UserSurveyService_GetByID_Call {
	_c.Call.Return(userSurvey, err)
	return _c
}

func (_c *UserSurveyService_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.UserSurvey, error)) *UserSurveyService_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type UserSurveyService
func (_mock *UserSurveyService) GetMany(ctx context.Context, filter *domain.UserSurveyFilter) ([]domain.UserSurvey, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.UserSurvey
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.UserSurveyFilter) ([]domain.UserSurvey, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.UserSurveyFilter) []domain.UserSurvey); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.UserSurvey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.UserSurveyFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.UserSurveyFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// UserSurveyService_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type UserSurveyService_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *UserSurveyService_Expecter) GetMany(ctx interface{}, filter interface{}) *UserSurveyService_GetMany_Call {
	return &UserSurveyService_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *UserSurveyService_GetMany_Call) Run(run func(ctx context.Context, filter *domain.UserSurveyFilter)) *UserSurveyService_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.UserSurveyFilter))
	})
	return _c
}

func (_c *UserSurveyService_GetMany_Call) Return(userSurveys []domain.UserSurvey, n int, err error) *UserSurveyService_GetMany_Call {
	_c.Call.Return(userSurveys, n, err)
	return _c
}

func (_c *UserSurveyService_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.UserSurveyFilter) ([]domain.UserSurvey, int, error)) *UserSurveyService_GetMany_Call {
	_c.Call.Return(run)
	return _c
}
