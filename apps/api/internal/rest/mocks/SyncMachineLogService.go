// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewSyncMachineLogService creates a new instance of SyncMachineLogService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSyncMachineLogService(t interface {
	mock.TestingT
	Cleanup(func())
}) *SyncMachineLogService {
	mock := &SyncMachineLogService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SyncMachineLogService is an autogenerated mock type for the SyncMachineLogService type
type SyncMachineLogService struct {
	mock.Mock
}

type SyncMachineLogService_Expecter struct {
	mock *mock.Mock
}

func (_m *SyncMachineLogService) EXPECT() *SyncMachineLogService_Expecter {
	return &SyncMachineLogService_Expecter{mock: &_m.Mock}
}

// CreateSyncMachineLog provides a mock function for the type SyncMachineLogService
func (_mock *SyncMachineLogService) CreateSyncMachineLog(ctx context.Context, data *domain.MachineSyncLog) (*domain.MachineSyncLog, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for CreateSyncMachineLog")
	}

	var r0 *domain.MachineSyncLog
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.MachineSyncLog) (*domain.MachineSyncLog, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.MachineSyncLog) *domain.MachineSyncLog); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.MachineSyncLog)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.MachineSyncLog) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SyncMachineLogService_CreateSyncMachineLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateSyncMachineLog'
type SyncMachineLogService_CreateSyncMachineLog_Call struct {
	*mock.Call
}

// CreateSyncMachineLog is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *SyncMachineLogService_Expecter) CreateSyncMachineLog(ctx interface{}, data interface{}) *SyncMachineLogService_CreateSyncMachineLog_Call {
	return &SyncMachineLogService_CreateSyncMachineLog_Call{Call: _e.mock.On("CreateSyncMachineLog", ctx, data)}
}

func (_c *SyncMachineLogService_CreateSyncMachineLog_Call) Run(run func(ctx context.Context, data *domain.MachineSyncLog)) *SyncMachineLogService_CreateSyncMachineLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.MachineSyncLog))
	})
	return _c
}

func (_c *SyncMachineLogService_CreateSyncMachineLog_Call) Return(machineSyncLog *domain.MachineSyncLog, err error) *SyncMachineLogService_CreateSyncMachineLog_Call {
	_c.Call.Return(machineSyncLog, err)
	return _c
}

func (_c *SyncMachineLogService_CreateSyncMachineLog_Call) RunAndReturn(run func(ctx context.Context, data *domain.MachineSyncLog) (*domain.MachineSyncLog, error)) *SyncMachineLogService_CreateSyncMachineLog_Call {
	_c.Call.Return(run)
	return _c
}
