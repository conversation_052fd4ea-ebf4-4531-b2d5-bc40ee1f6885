// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewTreatmentProductService creates a new instance of TreatmentProductService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTreatmentProductService(t interface {
	mock.TestingT
	Cleanup(func())
}) *TreatmentProductService {
	mock := &TreatmentProductService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// TreatmentProductService is an autogenerated mock type for the TreatmentProductService type
type TreatmentProductService struct {
	mock.Mock
}

type TreatmentProductService_Expecter struct {
	mock *mock.Mock
}

func (_m *TreatmentProductService) EXPECT() *TreatmentProductService_Expecter {
	return &TreatmentProductService_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type TreatmentProductService
func (_mock *TreatmentProductService) Create(ctx context.Context, request *domain.TreatmentProductRequest) (*domain.TreatmentProduct, *domain.TreatmentProductSupplementaryData, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *domain.TreatmentProduct
	var r1 *domain.TreatmentProductSupplementaryData
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentProductRequest) (*domain.TreatmentProduct, *domain.TreatmentProductSupplementaryData, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentProductRequest) *domain.TreatmentProduct); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentProduct)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentProductRequest) *domain.TreatmentProductSupplementaryData); ok {
		r1 = returnFunc(ctx, request)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*domain.TreatmentProductSupplementaryData)
		}
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.TreatmentProductRequest) error); ok {
		r2 = returnFunc(ctx, request)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// TreatmentProductService_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type TreatmentProductService_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *TreatmentProductService_Expecter) Create(ctx interface{}, request interface{}) *TreatmentProductService_Create_Call {
	return &TreatmentProductService_Create_Call{Call: _e.mock.On("Create", ctx, request)}
}

func (_c *TreatmentProductService_Create_Call) Run(run func(ctx context.Context, request *domain.TreatmentProductRequest)) *TreatmentProductService_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentProductRequest))
	})
	return _c
}

func (_c *TreatmentProductService_Create_Call) Return(treatmentProduct *domain.TreatmentProduct, treatmentProductSupplementaryData *domain.TreatmentProductSupplementaryData, err error) *TreatmentProductService_Create_Call {
	_c.Call.Return(treatmentProduct, treatmentProductSupplementaryData, err)
	return _c
}

func (_c *TreatmentProductService_Create_Call) RunAndReturn(run func(ctx context.Context, request *domain.TreatmentProductRequest) (*domain.TreatmentProduct, *domain.TreatmentProductSupplementaryData, error)) *TreatmentProductService_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByID provides a mock function for the type TreatmentProductService
func (_mock *TreatmentProductService) DeleteByID(ctx context.Context, id *string) (*domain.TreatmentProduct, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByID")
	}

	var r0 *domain.TreatmentProduct
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.TreatmentProduct, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.TreatmentProduct); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentProduct)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentProductService_DeleteByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByID'
type TreatmentProductService_DeleteByID_Call struct {
	*mock.Call
}

// DeleteByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *TreatmentProductService_Expecter) DeleteByID(ctx interface{}, id interface{}) *TreatmentProductService_DeleteByID_Call {
	return &TreatmentProductService_DeleteByID_Call{Call: _e.mock.On("DeleteByID", ctx, id)}
}

func (_c *TreatmentProductService_DeleteByID_Call) Run(run func(ctx context.Context, id *string)) *TreatmentProductService_DeleteByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *TreatmentProductService_DeleteByID_Call) Return(treatmentProduct *domain.TreatmentProduct, err error) *TreatmentProductService_DeleteByID_Call {
	_c.Call.Return(treatmentProduct, err)
	return _c
}

func (_c *TreatmentProductService_DeleteByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.TreatmentProduct, error)) *TreatmentProductService_DeleteByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type TreatmentProductService
func (_mock *TreatmentProductService) GetByID(ctx context.Context, id *string) (*domain.TreatmentProductResponse, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.TreatmentProductResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.TreatmentProductResponse, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.TreatmentProductResponse); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentProductResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentProductService_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type TreatmentProductService_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *TreatmentProductService_Expecter) GetByID(ctx interface{}, id interface{}) *TreatmentProductService_GetByID_Call {
	return &TreatmentProductService_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *TreatmentProductService_GetByID_Call) Run(run func(ctx context.Context, id *string)) *TreatmentProductService_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *TreatmentProductService_GetByID_Call) Return(treatmentProductResponse *domain.TreatmentProductResponse, err error) *TreatmentProductService_GetByID_Call {
	_c.Call.Return(treatmentProductResponse, err)
	return _c
}

func (_c *TreatmentProductService_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.TreatmentProductResponse, error)) *TreatmentProductService_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type TreatmentProductService
func (_mock *TreatmentProductService) GetMany(ctx context.Context, filter *domain.TreatmentProductFilter) ([]domain.TreatmentProductGetMany, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.TreatmentProductGetMany
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentProductFilter) ([]domain.TreatmentProductGetMany, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentProductFilter) []domain.TreatmentProductGetMany); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.TreatmentProductGetMany)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentProductFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.TreatmentProductFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// TreatmentProductService_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type TreatmentProductService_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *TreatmentProductService_Expecter) GetMany(ctx interface{}, filter interface{}) *TreatmentProductService_GetMany_Call {
	return &TreatmentProductService_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *TreatmentProductService_GetMany_Call) Run(run func(ctx context.Context, filter *domain.TreatmentProductFilter)) *TreatmentProductService_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentProductFilter))
	})
	return _c
}

func (_c *TreatmentProductService_GetMany_Call) Return(treatmentProductGetManys []domain.TreatmentProductGetMany, n int, err error) *TreatmentProductService_GetMany_Call {
	_c.Call.Return(treatmentProductGetManys, n, err)
	return _c
}

func (_c *TreatmentProductService_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.TreatmentProductFilter) ([]domain.TreatmentProductGetMany, int, error)) *TreatmentProductService_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type TreatmentProductService
func (_mock *TreatmentProductService) UpdateByID(ctx context.Context, id *string, request *domain.TreatmentProductRequest) (*domain.TreatmentProduct, *domain.TreatmentProductSupplementaryData, error) {
	ret := _mock.Called(ctx, id, request)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 *domain.TreatmentProduct
	var r1 *domain.TreatmentProductSupplementaryData
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.TreatmentProductRequest) (*domain.TreatmentProduct, *domain.TreatmentProductSupplementaryData, error)); ok {
		return returnFunc(ctx, id, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.TreatmentProductRequest) *domain.TreatmentProduct); ok {
		r0 = returnFunc(ctx, id, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentProduct)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *domain.TreatmentProductRequest) *domain.TreatmentProductSupplementaryData); ok {
		r1 = returnFunc(ctx, id, request)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*domain.TreatmentProductSupplementaryData)
		}
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *string, *domain.TreatmentProductRequest) error); ok {
		r2 = returnFunc(ctx, id, request)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// TreatmentProductService_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type TreatmentProductService_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - id
//   - request
func (_e *TreatmentProductService_Expecter) UpdateByID(ctx interface{}, id interface{}, request interface{}) *TreatmentProductService_UpdateByID_Call {
	return &TreatmentProductService_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, id, request)}
}

func (_c *TreatmentProductService_UpdateByID_Call) Run(run func(ctx context.Context, id *string, request *domain.TreatmentProductRequest)) *TreatmentProductService_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*domain.TreatmentProductRequest))
	})
	return _c
}

func (_c *TreatmentProductService_UpdateByID_Call) Return(treatmentProduct *domain.TreatmentProduct, treatmentProductSupplementaryData *domain.TreatmentProductSupplementaryData, err error) *TreatmentProductService_UpdateByID_Call {
	_c.Call.Return(treatmentProduct, treatmentProductSupplementaryData, err)
	return _c
}

func (_c *TreatmentProductService_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, id *string, request *domain.TreatmentProductRequest) (*domain.TreatmentProduct, *domain.TreatmentProductSupplementaryData, error)) *TreatmentProductService_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}
