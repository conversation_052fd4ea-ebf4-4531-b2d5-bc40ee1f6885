// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewSkinProblemIndicationService creates a new instance of SkinProblemIndicationService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSkinProblemIndicationService(t interface {
	mock.TestingT
	Cleanup(func())
}) *SkinProblemIndicationService {
	mock := &SkinProblemIndicationService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SkinProblemIndicationService is an autogenerated mock type for the SkinProblemIndicationService type
type SkinProblemIndicationService struct {
	mock.Mock
}

type SkinProblemIndicationService_Expecter struct {
	mock *mock.Mock
}

func (_m *SkinProblemIndicationService) EXPECT() *SkinProblemIndicationService_Expecter {
	return &SkinProblemIndicationService_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type SkinProblemIndicationService
func (_mock *SkinProblemIndicationService) Create(ctx context.Context, request *domain.SkinProblemIndicationRequest) (*domain.SkinProblemIndication, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *domain.SkinProblemIndication
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemIndicationRequest) (*domain.SkinProblemIndication, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemIndicationRequest) *domain.SkinProblemIndication); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinProblemIndication)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinProblemIndicationRequest) error); ok {
		r1 = returnFunc(ctx, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinProblemIndicationService_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type SkinProblemIndicationService_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *SkinProblemIndicationService_Expecter) Create(ctx interface{}, request interface{}) *SkinProblemIndicationService_Create_Call {
	return &SkinProblemIndicationService_Create_Call{Call: _e.mock.On("Create", ctx, request)}
}

func (_c *SkinProblemIndicationService_Create_Call) Run(run func(ctx context.Context, request *domain.SkinProblemIndicationRequest)) *SkinProblemIndicationService_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinProblemIndicationRequest))
	})
	return _c
}

func (_c *SkinProblemIndicationService_Create_Call) Return(skinProblemIndication *domain.SkinProblemIndication, err error) *SkinProblemIndicationService_Create_Call {
	_c.Call.Return(skinProblemIndication, err)
	return _c
}

func (_c *SkinProblemIndicationService_Create_Call) RunAndReturn(run func(ctx context.Context, request *domain.SkinProblemIndicationRequest) (*domain.SkinProblemIndication, error)) *SkinProblemIndicationService_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByID provides a mock function for the type SkinProblemIndicationService
func (_mock *SkinProblemIndicationService) DeleteByID(ctx context.Context, id *string) (*domain.SkinProblemIndication, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByID")
	}

	var r0 *domain.SkinProblemIndication
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.SkinProblemIndication, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.SkinProblemIndication); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinProblemIndication)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinProblemIndicationService_DeleteByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByID'
type SkinProblemIndicationService_DeleteByID_Call struct {
	*mock.Call
}

// DeleteByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SkinProblemIndicationService_Expecter) DeleteByID(ctx interface{}, id interface{}) *SkinProblemIndicationService_DeleteByID_Call {
	return &SkinProblemIndicationService_DeleteByID_Call{Call: _e.mock.On("DeleteByID", ctx, id)}
}

func (_c *SkinProblemIndicationService_DeleteByID_Call) Run(run func(ctx context.Context, id *string)) *SkinProblemIndicationService_DeleteByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *SkinProblemIndicationService_DeleteByID_Call) Return(skinProblemIndication *domain.SkinProblemIndication, err error) *SkinProblemIndicationService_DeleteByID_Call {
	_c.Call.Return(skinProblemIndication, err)
	return _c
}

func (_c *SkinProblemIndicationService_DeleteByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.SkinProblemIndication, error)) *SkinProblemIndicationService_DeleteByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type SkinProblemIndicationService
func (_mock *SkinProblemIndicationService) GetByID(ctx context.Context, id *string) (*domain.SkinProblemIndication, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.SkinProblemIndication
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.SkinProblemIndication, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.SkinProblemIndication); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinProblemIndication)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinProblemIndicationService_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type SkinProblemIndicationService_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SkinProblemIndicationService_Expecter) GetByID(ctx interface{}, id interface{}) *SkinProblemIndicationService_GetByID_Call {
	return &SkinProblemIndicationService_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *SkinProblemIndicationService_GetByID_Call) Run(run func(ctx context.Context, id *string)) *SkinProblemIndicationService_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *SkinProblemIndicationService_GetByID_Call) Return(skinProblemIndication *domain.SkinProblemIndication, err error) *SkinProblemIndicationService_GetByID_Call {
	_c.Call.Return(skinProblemIndication, err)
	return _c
}

func (_c *SkinProblemIndicationService_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.SkinProblemIndication, error)) *SkinProblemIndicationService_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type SkinProblemIndicationService
func (_mock *SkinProblemIndicationService) GetMany(ctx context.Context, filter *domain.SkinProblemIndicationFilter) ([]domain.SkinProblemIndication, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.SkinProblemIndication
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemIndicationFilter) ([]domain.SkinProblemIndication, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemIndicationFilter) []domain.SkinProblemIndication); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.SkinProblemIndication)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinProblemIndicationFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.SkinProblemIndicationFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// SkinProblemIndicationService_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type SkinProblemIndicationService_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *SkinProblemIndicationService_Expecter) GetMany(ctx interface{}, filter interface{}) *SkinProblemIndicationService_GetMany_Call {
	return &SkinProblemIndicationService_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *SkinProblemIndicationService_GetMany_Call) Run(run func(ctx context.Context, filter *domain.SkinProblemIndicationFilter)) *SkinProblemIndicationService_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinProblemIndicationFilter))
	})
	return _c
}

func (_c *SkinProblemIndicationService_GetMany_Call) Return(skinProblemIndications []domain.SkinProblemIndication, n int, err error) *SkinProblemIndicationService_GetMany_Call {
	_c.Call.Return(skinProblemIndications, n, err)
	return _c
}

func (_c *SkinProblemIndicationService_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.SkinProblemIndicationFilter) ([]domain.SkinProblemIndication, int, error)) *SkinProblemIndicationService_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type SkinProblemIndicationService
func (_mock *SkinProblemIndicationService) UpdateByID(ctx context.Context, id *string, request *domain.SkinProblemIndicationRequest) (*domain.SkinProblemIndication, error) {
	ret := _mock.Called(ctx, id, request)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 *domain.SkinProblemIndication
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.SkinProblemIndicationRequest) (*domain.SkinProblemIndication, error)); ok {
		return returnFunc(ctx, id, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.SkinProblemIndicationRequest) *domain.SkinProblemIndication); ok {
		r0 = returnFunc(ctx, id, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinProblemIndication)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *domain.SkinProblemIndicationRequest) error); ok {
		r1 = returnFunc(ctx, id, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinProblemIndicationService_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type SkinProblemIndicationService_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - id
//   - request
func (_e *SkinProblemIndicationService_Expecter) UpdateByID(ctx interface{}, id interface{}, request interface{}) *SkinProblemIndicationService_UpdateByID_Call {
	return &SkinProblemIndicationService_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, id, request)}
}

func (_c *SkinProblemIndicationService_UpdateByID_Call) Run(run func(ctx context.Context, id *string, request *domain.SkinProblemIndicationRequest)) *SkinProblemIndicationService_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*domain.SkinProblemIndicationRequest))
	})
	return _c
}

func (_c *SkinProblemIndicationService_UpdateByID_Call) Return(skinProblemIndication *domain.SkinProblemIndication, err error) *SkinProblemIndicationService_UpdateByID_Call {
	_c.Call.Return(skinProblemIndication, err)
	return _c
}

func (_c *SkinProblemIndicationService_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, id *string, request *domain.SkinProblemIndicationRequest) (*domain.SkinProblemIndication, error)) *SkinProblemIndicationService_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}
