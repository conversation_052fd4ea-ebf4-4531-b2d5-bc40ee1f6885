// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewUserService creates a new instance of UserService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserService(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserService {
	mock := &UserService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// UserService is an autogenerated mock type for the UserService type
type UserService struct {
	mock.Mock
}

type UserService_Expecter struct {
	mock *mock.Mock
}

func (_m *UserService) EXPECT() *UserService_Expecter {
	return &UserService_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type UserService
func (_mock *UserService) Create(ctx context.Context, request *domain.User) (*domain.User, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *domain.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.User) (*domain.User, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.User) *domain.User); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.User) error); ok {
		r1 = returnFunc(ctx, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// UserService_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type UserService_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *UserService_Expecter) Create(ctx interface{}, request interface{}) *UserService_Create_Call {
	return &UserService_Create_Call{Call: _e.mock.On("Create", ctx, request)}
}

func (_c *UserService_Create_Call) Run(run func(ctx context.Context, request *domain.User)) *UserService_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.User))
	})
	return _c
}

func (_c *UserService_Create_Call) Return(user *domain.User, err error) *UserService_Create_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *UserService_Create_Call) RunAndReturn(run func(ctx context.Context, request *domain.User) (*domain.User, error)) *UserService_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByID provides a mock function for the type UserService
func (_mock *UserService) DeleteByID(ctx context.Context, id *string) (*domain.User, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByID")
	}

	var r0 *domain.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.User, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.User); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// UserService_DeleteByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByID'
type UserService_DeleteByID_Call struct {
	*mock.Call
}

// DeleteByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *UserService_Expecter) DeleteByID(ctx interface{}, id interface{}) *UserService_DeleteByID_Call {
	return &UserService_DeleteByID_Call{Call: _e.mock.On("DeleteByID", ctx, id)}
}

func (_c *UserService_DeleteByID_Call) Run(run func(ctx context.Context, id *string)) *UserService_DeleteByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *UserService_DeleteByID_Call) Return(user *domain.User, err error) *UserService_DeleteByID_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *UserService_DeleteByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.User, error)) *UserService_DeleteByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type UserService
func (_mock *UserService) GetByID(ctx context.Context, id *string) (*domain.User, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.User, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.User); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// UserService_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type UserService_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *UserService_Expecter) GetByID(ctx interface{}, id interface{}) *UserService_GetByID_Call {
	return &UserService_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *UserService_GetByID_Call) Run(run func(ctx context.Context, id *string)) *UserService_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *UserService_GetByID_Call) Return(user *domain.User, err error) *UserService_GetByID_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *UserService_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.User, error)) *UserService_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type UserService
func (_mock *UserService) GetMany(ctx context.Context, filter *domain.UserFilter) (*domain.PaginationData[domain.UserResponse], error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 *domain.PaginationData[domain.UserResponse]
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.UserFilter) (*domain.PaginationData[domain.UserResponse], error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.UserFilter) *domain.PaginationData[domain.UserResponse]); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.PaginationData[domain.UserResponse])
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.UserFilter) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// UserService_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type UserService_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *UserService_Expecter) GetMany(ctx interface{}, filter interface{}) *UserService_GetMany_Call {
	return &UserService_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *UserService_GetMany_Call) Run(run func(ctx context.Context, filter *domain.UserFilter)) *UserService_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.UserFilter))
	})
	return _c
}

func (_c *UserService_GetMany_Call) Return(paginationData *domain.PaginationData[domain.UserResponse], err error) *UserService_GetMany_Call {
	_c.Call.Return(paginationData, err)
	return _c
}

func (_c *UserService_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.UserFilter) (*domain.PaginationData[domain.UserResponse], error)) *UserService_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type UserService
func (_mock *UserService) UpdateByID(ctx context.Context, request *domain.User) (*domain.User, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 *domain.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.User) (*domain.User, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.User) *domain.User); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.User) error); ok {
		r1 = returnFunc(ctx, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// UserService_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type UserService_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *UserService_Expecter) UpdateByID(ctx interface{}, request interface{}) *UserService_UpdateByID_Call {
	return &UserService_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, request)}
}

func (_c *UserService_UpdateByID_Call) Run(run func(ctx context.Context, request *domain.User)) *UserService_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.User))
	})
	return _c
}

func (_c *UserService_UpdateByID_Call) Return(user *domain.User, err error) *UserService_UpdateByID_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *UserService_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, request *domain.User) (*domain.User, error)) *UserService_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePassword provides a mock function for the type UserService
func (_mock *UserService) UpdatePassword(ctx context.Context, id *string, password *string) error {
	ret := _mock.Called(ctx, id, password)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePassword")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string) error); ok {
		r0 = returnFunc(ctx, id, password)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// UserService_UpdatePassword_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePassword'
type UserService_UpdatePassword_Call struct {
	*mock.Call
}

// UpdatePassword is a helper method to define mock.On call
//   - ctx
//   - id
//   - password
func (_e *UserService_Expecter) UpdatePassword(ctx interface{}, id interface{}, password interface{}) *UserService_UpdatePassword_Call {
	return &UserService_UpdatePassword_Call{Call: _e.mock.On("UpdatePassword", ctx, id, password)}
}

func (_c *UserService_UpdatePassword_Call) Run(run func(ctx context.Context, id *string, password *string)) *UserService_UpdatePassword_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*string))
	})
	return _c
}

func (_c *UserService_UpdatePassword_Call) Return(err error) *UserService_UpdatePassword_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *UserService_UpdatePassword_Call) RunAndReturn(run func(ctx context.Context, id *string, password *string) error) *UserService_UpdatePassword_Call {
	_c.Call.Return(run)
	return _c
}
