// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewTreatmentIntervalService creates a new instance of TreatmentIntervalService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTreatmentIntervalService(t interface {
	mock.TestingT
	Cleanup(func())
}) *TreatmentIntervalService {
	mock := &TreatmentIntervalService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// TreatmentIntervalService is an autogenerated mock type for the TreatmentIntervalService type
type TreatmentIntervalService struct {
	mock.Mock
}

type TreatmentIntervalService_Expecter struct {
	mock *mock.Mock
}

func (_m *TreatmentIntervalService) EXPECT() *TreatmentIntervalService_Expecter {
	return &TreatmentIntervalService_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type TreatmentIntervalService
func (_mock *TreatmentIntervalService) Create(ctx context.Context, data *domain.TreatmentInterval) (*domain.TreatmentInterval, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *domain.TreatmentInterval
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentInterval) (*domain.TreatmentInterval, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentInterval) *domain.TreatmentInterval); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentInterval)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentInterval) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentIntervalService_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type TreatmentIntervalService_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *TreatmentIntervalService_Expecter) Create(ctx interface{}, data interface{}) *TreatmentIntervalService_Create_Call {
	return &TreatmentIntervalService_Create_Call{Call: _e.mock.On("Create", ctx, data)}
}

func (_c *TreatmentIntervalService_Create_Call) Run(run func(ctx context.Context, data *domain.TreatmentInterval)) *TreatmentIntervalService_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentInterval))
	})
	return _c
}

func (_c *TreatmentIntervalService_Create_Call) Return(treatmentInterval *domain.TreatmentInterval, err error) *TreatmentIntervalService_Create_Call {
	_c.Call.Return(treatmentInterval, err)
	return _c
}

func (_c *TreatmentIntervalService_Create_Call) RunAndReturn(run func(ctx context.Context, data *domain.TreatmentInterval) (*domain.TreatmentInterval, error)) *TreatmentIntervalService_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByID provides a mock function for the type TreatmentIntervalService
func (_mock *TreatmentIntervalService) DeleteByID(ctx context.Context, id *string) (*domain.TreatmentInterval, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByID")
	}

	var r0 *domain.TreatmentInterval
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.TreatmentInterval, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.TreatmentInterval); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentInterval)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentIntervalService_DeleteByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByID'
type TreatmentIntervalService_DeleteByID_Call struct {
	*mock.Call
}

// DeleteByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *TreatmentIntervalService_Expecter) DeleteByID(ctx interface{}, id interface{}) *TreatmentIntervalService_DeleteByID_Call {
	return &TreatmentIntervalService_DeleteByID_Call{Call: _e.mock.On("DeleteByID", ctx, id)}
}

func (_c *TreatmentIntervalService_DeleteByID_Call) Run(run func(ctx context.Context, id *string)) *TreatmentIntervalService_DeleteByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *TreatmentIntervalService_DeleteByID_Call) Return(treatmentInterval *domain.TreatmentInterval, err error) *TreatmentIntervalService_DeleteByID_Call {
	_c.Call.Return(treatmentInterval, err)
	return _c
}

func (_c *TreatmentIntervalService_DeleteByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.TreatmentInterval, error)) *TreatmentIntervalService_DeleteByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type TreatmentIntervalService
func (_mock *TreatmentIntervalService) GetByID(ctx context.Context, id *string) (*domain.TreatmentInterval, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.TreatmentInterval
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.TreatmentInterval, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.TreatmentInterval); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentInterval)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentIntervalService_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type TreatmentIntervalService_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *TreatmentIntervalService_Expecter) GetByID(ctx interface{}, id interface{}) *TreatmentIntervalService_GetByID_Call {
	return &TreatmentIntervalService_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *TreatmentIntervalService_GetByID_Call) Run(run func(ctx context.Context, id *string)) *TreatmentIntervalService_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *TreatmentIntervalService_GetByID_Call) Return(treatmentInterval *domain.TreatmentInterval, err error) *TreatmentIntervalService_GetByID_Call {
	_c.Call.Return(treatmentInterval, err)
	return _c
}

func (_c *TreatmentIntervalService_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.TreatmentInterval, error)) *TreatmentIntervalService_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type TreatmentIntervalService
func (_mock *TreatmentIntervalService) GetMany(ctx context.Context, filter *domain.TreatmentIntervalFilter) ([]domain.TreatmentInterval, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.TreatmentInterval
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentIntervalFilter) ([]domain.TreatmentInterval, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentIntervalFilter) []domain.TreatmentInterval); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.TreatmentInterval)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentIntervalFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.TreatmentIntervalFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// TreatmentIntervalService_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type TreatmentIntervalService_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *TreatmentIntervalService_Expecter) GetMany(ctx interface{}, filter interface{}) *TreatmentIntervalService_GetMany_Call {
	return &TreatmentIntervalService_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *TreatmentIntervalService_GetMany_Call) Run(run func(ctx context.Context, filter *domain.TreatmentIntervalFilter)) *TreatmentIntervalService_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentIntervalFilter))
	})
	return _c
}

func (_c *TreatmentIntervalService_GetMany_Call) Return(treatmentIntervals []domain.TreatmentInterval, n int, err error) *TreatmentIntervalService_GetMany_Call {
	_c.Call.Return(treatmentIntervals, n, err)
	return _c
}

func (_c *TreatmentIntervalService_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.TreatmentIntervalFilter) ([]domain.TreatmentInterval, int, error)) *TreatmentIntervalService_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type TreatmentIntervalService
func (_mock *TreatmentIntervalService) UpdateByID(ctx context.Context, input *domain.TreatmentInterval) (*domain.TreatmentInterval, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 *domain.TreatmentInterval
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentInterval) (*domain.TreatmentInterval, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentInterval) *domain.TreatmentInterval); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentInterval)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentInterval) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentIntervalService_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type TreatmentIntervalService_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *TreatmentIntervalService_Expecter) UpdateByID(ctx interface{}, input interface{}) *TreatmentIntervalService_UpdateByID_Call {
	return &TreatmentIntervalService_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, input)}
}

func (_c *TreatmentIntervalService_UpdateByID_Call) Run(run func(ctx context.Context, input *domain.TreatmentInterval)) *TreatmentIntervalService_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentInterval))
	})
	return _c
}

func (_c *TreatmentIntervalService_UpdateByID_Call) Return(treatmentInterval *domain.TreatmentInterval, err error) *TreatmentIntervalService_UpdateByID_Call {
	_c.Call.Return(treatmentInterval, err)
	return _c
}

func (_c *TreatmentIntervalService_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, input *domain.TreatmentInterval) (*domain.TreatmentInterval, error)) *TreatmentIntervalService_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}
