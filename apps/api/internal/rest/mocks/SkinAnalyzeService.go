// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewSkinAnalyzeService creates a new instance of SkinAnalyzeService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSkinAnalyzeService(t interface {
	mock.TestingT
	Cleanup(func())
}) *SkinAnalyzeService {
	mock := &SkinAnalyzeService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SkinAnalyzeService is an autogenerated mock type for the SkinAnalyzeService type
type SkinAnalyzeService struct {
	mock.Mock
}

type SkinAnalyzeService_Expecter struct {
	mock *mock.Mock
}

func (_m *SkinAnalyzeService) EXPECT() *SkinAnalyzeService_Expecter {
	return &SkinAnalyzeService_Expecter{mock: &_m.Mock}
}

// CreateSkinAnalyze provides a mock function for the type SkinAnalyzeService
func (_mock *SkinAnalyzeService) CreateSkinAnalyze(ctx context.Context, data *domain.SkinAnalyzeUploadRequest) (*domain.SkinAnalyze, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for CreateSkinAnalyze")
	}

	var r0 *domain.SkinAnalyze
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinAnalyzeUploadRequest) (*domain.SkinAnalyze, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinAnalyzeUploadRequest) *domain.SkinAnalyze); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinAnalyze)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinAnalyzeUploadRequest) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinAnalyzeService_CreateSkinAnalyze_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateSkinAnalyze'
type SkinAnalyzeService_CreateSkinAnalyze_Call struct {
	*mock.Call
}

// CreateSkinAnalyze is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *SkinAnalyzeService_Expecter) CreateSkinAnalyze(ctx interface{}, data interface{}) *SkinAnalyzeService_CreateSkinAnalyze_Call {
	return &SkinAnalyzeService_CreateSkinAnalyze_Call{Call: _e.mock.On("CreateSkinAnalyze", ctx, data)}
}

func (_c *SkinAnalyzeService_CreateSkinAnalyze_Call) Run(run func(ctx context.Context, data *domain.SkinAnalyzeUploadRequest)) *SkinAnalyzeService_CreateSkinAnalyze_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinAnalyzeUploadRequest))
	})
	return _c
}

func (_c *SkinAnalyzeService_CreateSkinAnalyze_Call) Return(skinAnalyze *domain.SkinAnalyze, err error) *SkinAnalyzeService_CreateSkinAnalyze_Call {
	_c.Call.Return(skinAnalyze, err)
	return _c
}

func (_c *SkinAnalyzeService_CreateSkinAnalyze_Call) RunAndReturn(run func(ctx context.Context, data *domain.SkinAnalyzeUploadRequest) (*domain.SkinAnalyze, error)) *SkinAnalyzeService_CreateSkinAnalyze_Call {
	_c.Call.Return(run)
	return _c
}

// GetManySkinAnalyzes provides a mock function for the type SkinAnalyzeService
func (_mock *SkinAnalyzeService) GetManySkinAnalyzes(ctx context.Context, filter *domain.SkinAnalyzeFilter) ([]*domain.SkinAnalyze, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetManySkinAnalyzes")
	}

	var r0 []*domain.SkinAnalyze
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinAnalyzeFilter) ([]*domain.SkinAnalyze, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinAnalyzeFilter) []*domain.SkinAnalyze); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*domain.SkinAnalyze)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinAnalyzeFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.SkinAnalyzeFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// SkinAnalyzeService_GetManySkinAnalyzes_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetManySkinAnalyzes'
type SkinAnalyzeService_GetManySkinAnalyzes_Call struct {
	*mock.Call
}

// GetManySkinAnalyzes is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *SkinAnalyzeService_Expecter) GetManySkinAnalyzes(ctx interface{}, filter interface{}) *SkinAnalyzeService_GetManySkinAnalyzes_Call {
	return &SkinAnalyzeService_GetManySkinAnalyzes_Call{Call: _e.mock.On("GetManySkinAnalyzes", ctx, filter)}
}

func (_c *SkinAnalyzeService_GetManySkinAnalyzes_Call) Run(run func(ctx context.Context, filter *domain.SkinAnalyzeFilter)) *SkinAnalyzeService_GetManySkinAnalyzes_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinAnalyzeFilter))
	})
	return _c
}

func (_c *SkinAnalyzeService_GetManySkinAnalyzes_Call) Return(skinAnalyzes []*domain.SkinAnalyze, n int, err error) *SkinAnalyzeService_GetManySkinAnalyzes_Call {
	_c.Call.Return(skinAnalyzes, n, err)
	return _c
}

func (_c *SkinAnalyzeService_GetManySkinAnalyzes_Call) RunAndReturn(run func(ctx context.Context, filter *domain.SkinAnalyzeFilter) ([]*domain.SkinAnalyze, int, error)) *SkinAnalyzeService_GetManySkinAnalyzes_Call {
	_c.Call.Return(run)
	return _c
}

// GetSkinAnalyzeByID provides a mock function for the type SkinAnalyzeService
func (_mock *SkinAnalyzeService) GetSkinAnalyzeByID(ctx context.Context, id string) (*domain.SkinAnalyze, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetSkinAnalyzeByID")
	}

	var r0 *domain.SkinAnalyze
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*domain.SkinAnalyze, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *domain.SkinAnalyze); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinAnalyze)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinAnalyzeService_GetSkinAnalyzeByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSkinAnalyzeByID'
type SkinAnalyzeService_GetSkinAnalyzeByID_Call struct {
	*mock.Call
}

// GetSkinAnalyzeByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SkinAnalyzeService_Expecter) GetSkinAnalyzeByID(ctx interface{}, id interface{}) *SkinAnalyzeService_GetSkinAnalyzeByID_Call {
	return &SkinAnalyzeService_GetSkinAnalyzeByID_Call{Call: _e.mock.On("GetSkinAnalyzeByID", ctx, id)}
}

func (_c *SkinAnalyzeService_GetSkinAnalyzeByID_Call) Run(run func(ctx context.Context, id string)) *SkinAnalyzeService_GetSkinAnalyzeByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *SkinAnalyzeService_GetSkinAnalyzeByID_Call) Return(skinAnalyze *domain.SkinAnalyze, err error) *SkinAnalyzeService_GetSkinAnalyzeByID_Call {
	_c.Call.Return(skinAnalyze, err)
	return _c
}

func (_c *SkinAnalyzeService_GetSkinAnalyzeByID_Call) RunAndReturn(run func(ctx context.Context, id string) (*domain.SkinAnalyze, error)) *SkinAnalyzeService_GetSkinAnalyzeByID_Call {
	_c.Call.Return(run)
	return _c
}
