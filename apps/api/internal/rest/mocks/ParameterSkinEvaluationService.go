// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewParameterSkinEvaluationService creates a new instance of ParameterSkinEvaluationService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewParameterSkinEvaluationService(t interface {
	mock.TestingT
	Cleanup(func())
}) *ParameterSkinEvaluationService {
	mock := &ParameterSkinEvaluationService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// ParameterSkinEvaluationService is an autogenerated mock type for the ParameterSkinEvaluationService type
type ParameterSkinEvaluationService struct {
	mock.Mock
}

type ParameterSkinEvaluationService_Expecter struct {
	mock *mock.Mock
}

func (_m *ParameterSkinEvaluationService) EXPECT() *ParameterSkinEvaluationService_Expecter {
	return &ParameterSkinEvaluationService_Expecter{mock: &_m.Mock}
}

// GetByID provides a mock function for the type ParameterSkinEvaluationService
func (_mock *ParameterSkinEvaluationService) GetByID(ctx context.Context, id *string) (*domain.ParameterSkinEvaluation, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.ParameterSkinEvaluation
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.ParameterSkinEvaluation, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.ParameterSkinEvaluation); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.ParameterSkinEvaluation)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// ParameterSkinEvaluationService_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type ParameterSkinEvaluationService_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *ParameterSkinEvaluationService_Expecter) GetByID(ctx interface{}, id interface{}) *ParameterSkinEvaluationService_GetByID_Call {
	return &ParameterSkinEvaluationService_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *ParameterSkinEvaluationService_GetByID_Call) Run(run func(ctx context.Context, id *string)) *ParameterSkinEvaluationService_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *ParameterSkinEvaluationService_GetByID_Call) Return(parameterSkinEvaluation *domain.ParameterSkinEvaluation, err error) *ParameterSkinEvaluationService_GetByID_Call {
	_c.Call.Return(parameterSkinEvaluation, err)
	return _c
}

func (_c *ParameterSkinEvaluationService_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.ParameterSkinEvaluation, error)) *ParameterSkinEvaluationService_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type ParameterSkinEvaluationService
func (_mock *ParameterSkinEvaluationService) GetMany(ctx context.Context, filter *domain.ParameterSkinEvaluationFilter) ([]domain.ParameterSkinEvaluation, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.ParameterSkinEvaluation
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.ParameterSkinEvaluationFilter) ([]domain.ParameterSkinEvaluation, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.ParameterSkinEvaluationFilter) []domain.ParameterSkinEvaluation); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.ParameterSkinEvaluation)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.ParameterSkinEvaluationFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.ParameterSkinEvaluationFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// ParameterSkinEvaluationService_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type ParameterSkinEvaluationService_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *ParameterSkinEvaluationService_Expecter) GetMany(ctx interface{}, filter interface{}) *ParameterSkinEvaluationService_GetMany_Call {
	return &ParameterSkinEvaluationService_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *ParameterSkinEvaluationService_GetMany_Call) Run(run func(ctx context.Context, filter *domain.ParameterSkinEvaluationFilter)) *ParameterSkinEvaluationService_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.ParameterSkinEvaluationFilter))
	})
	return _c
}

func (_c *ParameterSkinEvaluationService_GetMany_Call) Return(parameterSkinEvaluations []domain.ParameterSkinEvaluation, n int, err error) *ParameterSkinEvaluationService_GetMany_Call {
	_c.Call.Return(parameterSkinEvaluations, n, err)
	return _c
}

func (_c *ParameterSkinEvaluationService_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.ParameterSkinEvaluationFilter) ([]domain.ParameterSkinEvaluation, int, error)) *ParameterSkinEvaluationService_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type ParameterSkinEvaluationService
func (_mock *ParameterSkinEvaluationService) UpdateByID(ctx context.Context, id *string, request *domain.ParameterSkinEvaluationRequest) (*domain.ParameterSkinEvaluation, error) {
	ret := _mock.Called(ctx, id, request)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 *domain.ParameterSkinEvaluation
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.ParameterSkinEvaluationRequest) (*domain.ParameterSkinEvaluation, error)); ok {
		return returnFunc(ctx, id, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.ParameterSkinEvaluationRequest) *domain.ParameterSkinEvaluation); ok {
		r0 = returnFunc(ctx, id, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.ParameterSkinEvaluation)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *domain.ParameterSkinEvaluationRequest) error); ok {
		r1 = returnFunc(ctx, id, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// ParameterSkinEvaluationService_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type ParameterSkinEvaluationService_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - id
//   - request
func (_e *ParameterSkinEvaluationService_Expecter) UpdateByID(ctx interface{}, id interface{}, request interface{}) *ParameterSkinEvaluationService_UpdateByID_Call {
	return &ParameterSkinEvaluationService_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, id, request)}
}

func (_c *ParameterSkinEvaluationService_UpdateByID_Call) Run(run func(ctx context.Context, id *string, request *domain.ParameterSkinEvaluationRequest)) *ParameterSkinEvaluationService_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*domain.ParameterSkinEvaluationRequest))
	})
	return _c
}

func (_c *ParameterSkinEvaluationService_UpdateByID_Call) Return(parameterSkinEvaluation *domain.ParameterSkinEvaluation, err error) *ParameterSkinEvaluationService_UpdateByID_Call {
	_c.Call.Return(parameterSkinEvaluation, err)
	return _c
}

func (_c *ParameterSkinEvaluationService_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, id *string, request *domain.ParameterSkinEvaluationRequest) (*domain.ParameterSkinEvaluation, error)) *ParameterSkinEvaluationService_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}
