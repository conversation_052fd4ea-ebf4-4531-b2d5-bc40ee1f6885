// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewFaceAgingService creates a new instance of FaceAgingService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewFaceAgingService(t interface {
	mock.TestingT
	Cleanup(func())
}) *FaceAgingService {
	mock := &FaceAgingService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// FaceAgingService is an autogenerated mock type for the FaceAgingService type
type FaceAgingService struct {
	mock.Mock
}

type FaceAgingService_Expecter struct {
	mock *mock.Mock
}

func (_m *FaceAgingService) EXPECT() *FaceAgingService_Expecter {
	return &FaceAgingService_Expecter{mock: &_m.Mock}
}

// FaceAgingWithConcern provides a mock function for the type FaceAgingService
func (_mock *FaceAgingService) FaceAgingWithConcern(ctx context.Context, id string, data *domain.FaceAgingConcernRequest) (*domain.FaceAgingConcernResponse, error) {
	ret := _mock.Called(ctx, id, data)

	if len(ret) == 0 {
		panic("no return value specified for FaceAgingWithConcern")
	}

	var r0 *domain.FaceAgingConcernResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.FaceAgingConcernRequest) (*domain.FaceAgingConcernResponse, error)); ok {
		return returnFunc(ctx, id, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.FaceAgingConcernRequest) *domain.FaceAgingConcernResponse); ok {
		r0 = returnFunc(ctx, id, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.FaceAgingConcernResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *domain.FaceAgingConcernRequest) error); ok {
		r1 = returnFunc(ctx, id, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// FaceAgingService_FaceAgingWithConcern_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FaceAgingWithConcern'
type FaceAgingService_FaceAgingWithConcern_Call struct {
	*mock.Call
}

// FaceAgingWithConcern is a helper method to define mock.On call
//   - ctx
//   - id
//   - data
func (_e *FaceAgingService_Expecter) FaceAgingWithConcern(ctx interface{}, id interface{}, data interface{}) *FaceAgingService_FaceAgingWithConcern_Call {
	return &FaceAgingService_FaceAgingWithConcern_Call{Call: _e.mock.On("FaceAgingWithConcern", ctx, id, data)}
}

func (_c *FaceAgingService_FaceAgingWithConcern_Call) Run(run func(ctx context.Context, id string, data *domain.FaceAgingConcernRequest)) *FaceAgingService_FaceAgingWithConcern_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*domain.FaceAgingConcernRequest))
	})
	return _c
}

func (_c *FaceAgingService_FaceAgingWithConcern_Call) Return(faceAgingConcernResponse *domain.FaceAgingConcernResponse, err error) *FaceAgingService_FaceAgingWithConcern_Call {
	_c.Call.Return(faceAgingConcernResponse, err)
	return _c
}

func (_c *FaceAgingService_FaceAgingWithConcern_Call) RunAndReturn(run func(ctx context.Context, id string, data *domain.FaceAgingConcernRequest) (*domain.FaceAgingConcernResponse, error)) *FaceAgingService_FaceAgingWithConcern_Call {
	_c.Call.Return(run)
	return _c
}
