// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewRecommendationService creates a new instance of RecommendationService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRecommendationService(t interface {
	mock.TestingT
	Cleanup(func())
}) *RecommendationService {
	mock := &RecommendationService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// RecommendationService is an autogenerated mock type for the RecommendationService type
type RecommendationService struct {
	mock.Mock
}

type RecommendationService_Expecter struct {
	mock *mock.Mock
}

func (_m *RecommendationService) EXPECT() *RecommendationService_Expecter {
	return &RecommendationService_Expecter{mock: &_m.<PERSON>ck}
}

// GetRecommendation provides a mock function for the type RecommendationService
func (_mock *RecommendationService) GetRecommendation(ctx context.Context, id string) (*domain.RecommendationResponse, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetRecommendation")
	}

	var r0 *domain.RecommendationResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*domain.RecommendationResponse, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *domain.RecommendationResponse); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.RecommendationResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// RecommendationService_GetRecommendation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRecommendation'
type RecommendationService_GetRecommendation_Call struct {
	*mock.Call
}

// GetRecommendation is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *RecommendationService_Expecter) GetRecommendation(ctx interface{}, id interface{}) *RecommendationService_GetRecommendation_Call {
	return &RecommendationService_GetRecommendation_Call{Call: _e.mock.On("GetRecommendation", ctx, id)}
}

func (_c *RecommendationService_GetRecommendation_Call) Run(run func(ctx context.Context, id string)) *RecommendationService_GetRecommendation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *RecommendationService_GetRecommendation_Call) Return(recommendationResponse *domain.RecommendationResponse, err error) *RecommendationService_GetRecommendation_Call {
	_c.Call.Return(recommendationResponse, err)
	return _c
}

func (_c *RecommendationService_GetRecommendation_Call) RunAndReturn(run func(ctx context.Context, id string) (*domain.RecommendationResponse, error)) *RecommendationService_GetRecommendation_Call {
	_c.Call.Return(run)
	return _c
}

// RecommendationTreatment provides a mock function for the type RecommendationService
func (_mock *RecommendationService) RecommendationTreatment(ctx context.Context, id string) (*domain.RecommendationTreatmentMLResponse, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for RecommendationTreatment")
	}

	var r0 *domain.RecommendationTreatmentMLResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*domain.RecommendationTreatmentMLResponse, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *domain.RecommendationTreatmentMLResponse); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.RecommendationTreatmentMLResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// RecommendationService_RecommendationTreatment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecommendationTreatment'
type RecommendationService_RecommendationTreatment_Call struct {
	*mock.Call
}

// RecommendationTreatment is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *RecommendationService_Expecter) RecommendationTreatment(ctx interface{}, id interface{}) *RecommendationService_RecommendationTreatment_Call {
	return &RecommendationService_RecommendationTreatment_Call{Call: _e.mock.On("RecommendationTreatment", ctx, id)}
}

func (_c *RecommendationService_RecommendationTreatment_Call) Run(run func(ctx context.Context, id string)) *RecommendationService_RecommendationTreatment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *RecommendationService_RecommendationTreatment_Call) Return(recommendationTreatmentMLResponse *domain.RecommendationTreatmentMLResponse, err error) *RecommendationService_RecommendationTreatment_Call {
	_c.Call.Return(recommendationTreatmentMLResponse, err)
	return _c
}

func (_c *RecommendationService_RecommendationTreatment_Call) RunAndReturn(run func(ctx context.Context, id string) (*domain.RecommendationTreatmentMLResponse, error)) *RecommendationService_RecommendationTreatment_Call {
	_c.Call.Return(run)
	return _c
}
