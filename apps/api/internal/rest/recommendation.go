package rest

import (
	"api/domain"
	internalMiddleware "api/internal/rest/middleware"
	"context"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
)

//go:generate mockery
type RecommendationService interface {
	RecommendationTreatment(
		ctx context.Context,
		id string,
	) (*domain.RecommendationTreatmentMLResponse, error)
	GetRecommendation(ctx context.Context, id string) (*domain.RecommendationResponse, error)
}

type RecommendationHandler struct {
	Service RecommendationService
}

func NewRecommendationHandler(
	e *echo.Group,
	service RecommendationService,
) {
	handler := &RecommendationHandler{
		Service: service,
	}

	generationRoutes := e.Group("")
	generationRoutes.Use(internalMiddleware.SetRequestContextWithTimeout(60 * time.Second))

	generationRoutes.POST("/recommendation-treatment/:id", handler.RecommendationTreatment)
	generationRoutes.GET("/recommendation/:id", handler.GetRecommendation)
}

// @router /api/v1/recommendation-treatment/{id} [post]
// @tags recommendation
// @summary Recommendation treatment
// @accept json
// @produce json
// @param id path string true "Skin analyze ID"
// @success 200 {object} domain.SingleResponse[domain.RecommendationTreatmentMLResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
// @failure 404 {object} domain.SingleResponse[domain.Empty]
// @failure 500 {object} domain.SingleResponse[domain.Empty]
func (handler *RecommendationHandler) RecommendationTreatment(c echo.Context) (err error) {
	skinAnalyzeID := c.Param("id")
	ctx := c.Request().Context()

	response, err := handler.Service.RecommendationTreatment(ctx, skinAnalyzeID)
	if err != nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Message: "Failed to process request",
			},
		)
	}

	return c.JSON(http.StatusOK, domain.SingleResponse[domain.RecommendationTreatmentMLResponse]{
		Code:    http.StatusOK,
		Message: "Generated image successfully",
		Data:    *response,
		Status:  "success",
	})
}

// @router /api/v1/recommendation/{id} [get]
// @tags recommendation
// @summary Get recommendation by ID
// @accept json
// @produce json
// @param id path string true "Skin Analyze ID"
// @success 200 {object} domain.SingleResponse[domain.RecommendationResponse]
// @failure 404 {object} domain.SingleResponse[domain.Empty]
func (handler *RecommendationHandler) GetRecommendation(c echo.Context) (err error) {
	id := c.Param("id")
	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  "error",
				Message: "Invalid ID",
			},
		)
	}

	ctx := c.Request().Context()
	recommendation, err := handler.Service.GetRecommendation(ctx, id)
	if err != nil {
		return c.JSON(
			http.StatusNotFound,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusNotFound,
				Status:  "error",
				Message: "Recommendation not found: " + err.Error(),
			},
		)
	}

	return c.JSON(http.StatusOK, domain.SingleResponse[domain.RecommendationResponse]{
		Code:    http.StatusOK,
		Status:  "success",
		Message: "Recommendation retrieved successfully",
		Data:    *recommendation,
	})
}
