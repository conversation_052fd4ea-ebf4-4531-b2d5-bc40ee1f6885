package rest_test

import (
	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestRecommendationHandler(t *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.RecommendationService)
		handler     = &rest.RecommendationHandler{mockService}
	)

	t.Run("Success recommendation treatment", func(t *testing.T) {
		skinAnalyzeID := "123"
		recommendationRes := &domain.RecommendationTreatmentMLResponse{
			Text:  "text",
			Video: "video",
			Data:  []domain.Treatment{},
		}
		mockService.On(
			"RecommendationTreatment",
			mock.Anything,
			mock.AnythingOfType("string"),
		).
			Return(recommendationRes, nil).
			Once()

		req := httptest.NewRequest(
			http.MethodPost,
			fmt.Sprintf("/recommendation-treatment/%s", skinAnalyzeID),
			nil,
		)
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetPath("/recommendation-treatment/:id")
		c.SetParamNames("id")
		c.SetParamValues(skinAnalyzeID)

		err := handler.RecommendationTreatment(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)
		mockService.AssertExpectations(t)
	})

}
