package rest_test

import (
	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestFaceAgingHandler(t *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.FaceAgingService)
		handler     = &rest.FaceAgingHandler{mockService}
	)

	t.Run("Success face aging with concern", func(t *testing.T) {
		faceAgingReq := &domain.FaceAgingConcernRequest{
			Concerns: []domain.FaceAgingConcernDetailMLRequest{
				{
					Concern: domain.FaceAgingConcernWrinkle,
					Areas: []domain.FaceAgingArea{
						domain.FaceAgingAreaUpper,
						domain.FaceAgingAreaLower,
					},
				},
			},
			IsBeautify: true,
		}
		reqBytes, err := json.<PERSON>(faceAgingReq)
		assert.NoError(t, err)
		skinAnalyzeID := "123"

		faceAgingRes := &domain.FaceAgingConcernResponse{
			GeneratedImages: []domain.FaceAgingGeneratedMLResponse{
				{
					Concern:              domain.FaceAgingConcernWrinkle,
					GeneratedImageURL:    "https://example.com/image.jpg",
					SelectedAreaImageURL: "https://example.com/image.png",
				},
			},
		}
		mockService.On(
			"FaceAgingWithConcern",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("*domain.FaceAgingConcernRequest"),
		).
			Return(faceAgingRes, nil).
			Once()

		req := httptest.NewRequest(
			http.MethodPost,
			fmt.Sprintf("/face-aging/concerns/%s", skinAnalyzeID),
			bytes.NewBuffer(reqBytes),
		)
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetPath("/face-aging/concerns/:id")
		c.SetParamNames("id")
		c.SetParamValues(skinAnalyzeID)

		err = handler.FaceAgingWithConcern(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)
		mockService.AssertExpectations(t)
	})
}
