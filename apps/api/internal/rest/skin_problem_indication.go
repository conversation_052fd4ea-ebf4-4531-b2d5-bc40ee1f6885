package rest

import (
	"context"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"

	"api/domain"
	"api/utils"
)

//go:generate mockery
type SkinProblemIndicationService interface {
	Create(
		ctx context.Context,
		request *domain.SkinProblemIndicationRequest,
	) (*domain.SkinProblemIndication, error)
	GetByID(
		ctx context.Context,
		id *string,
	) (*domain.SkinProblemIndication, error)
	UpdateByID(
		ctx context.Context,
		id *string,
		request *domain.SkinProblemIndicationRequest,
	) (*domain.SkinProblemIndication, error)
	DeleteByID(
		ctx context.Context,
		id *string,
	) (*domain.SkinProblemIndication, error)
	GetMany(
		ctx context.Context,
		filter *domain.SkinProblemIndicationFilter,
	) ([]domain.SkinProblemIndication, int, error)
}

type SkinProblemIndicationHandler struct {
	Service SkinProblemIndicationService
}

func NewSkinProblemIndicationHandler(
	e *echo.Group,
	service SkinProblemIndicationService,
) {
	handler := &SkinProblemIndicationHandler{service}

	e.POST("/skin-problem-indication", handler.Create)
	e.GET("/skin-problem-indication/:id", handler.GetByID)
	e.PUT("/skin-problem-indication/:id", handler.UpdateByID)
	e.DELETE("/skin-problem-indication/:id", handler.DeleteByID)
	e.GET("/skin-problem-indication", handler.GetMany)
}

// @router /api/v1/skin-problem-indication [post]
// @tags skin-problem-indication
// @summary Create new skin problem indication
// @security BearerAuth
// @accept json
// @produce json
// @param request body domain.SkinProblemIndicationRequest true "Request body"
// @success 201 {object} domain.SingleResponse[domain.SkinProblemIndication]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinProblemIndicationHandler) Create(c echo.Context) (err error) {
	var request *domain.SkinProblemIndicationRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Failed binding request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.Create(ctx, request)

	if err != nil {
		if strings.Contains(err.Error(), "already exist") {
			return c.JSON(
				http.StatusConflict,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusNotFound,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Create data failed: " + err.Error(),
			},
		)
	}

	return c.JSON(
		http.StatusCreated,
		domain.SingleResponse[domain.SkinProblemIndication]{
			Code:    http.StatusCreated,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Data created successfully",
		},
	)
}

// @router /api/v1/skin-problem-indication/{id} [get]
// @tags skin-problem-indication
// @summary Get skin problem indication detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Skin Problem Indication ID"
// @success 200 {object} domain.SingleResponse[domain.SkinProblemIndication]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinProblemIndicationHandler) GetByID(c echo.Context) (err error) {
	id := c.Param("id")

	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.GetByID(ctx, &id)

	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(
				http.StatusNotFound,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusNotFound,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Get data failed: " + err.Error(),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.SkinProblemIndication]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Get data successfully",
		},
	)
}

// @router /api/v1/skin-problem-indication/{id} [put]
// @tags skin-problem-indication
// @summary Update skin problem indication detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Skin Problem Indication ID"
// @param request body domain.SkinProblemIndicationRequest true "Request body"
// @success 200 {object} domain.SingleResponse[domain.SkinProblemIndication]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinProblemIndicationHandler) UpdateByID(c echo.Context) (err error) {
	id := c.Param("id")

	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	var request *domain.SkinProblemIndicationRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Failed binding request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.UpdateByID(ctx, &id, request)

	if err != nil {
		if strings.Contains(err.Error(), "already exist") {
			return c.JSON(
				http.StatusConflict,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusConflict,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		if strings.Contains(err.Error(), "not found") {
			return c.JSON(
				http.StatusNotFound,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusNotFound,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Update data failed: " + err.Error(),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.SkinProblemIndication]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Data updated successfully",
		},
	)
}

// @router /api/v1/skin-problem-indication/{id} [delete]
// @tags skin-problem-indication
// @summary Delete skin problem indication detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Skin Problem Indication ID"
// @success 200 {object} domain.SingleResponse[domain.SkinProblemIndication]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinProblemIndicationHandler) DeleteByID(c echo.Context) (err error) {
	id := c.Param("id")

	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.DeleteByID(ctx, &id)

	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(
				http.StatusNotFound,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusNotFound,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		if strings.Contains(err.Error(), "still used in") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Delete data failed: " + err.Error(),
				},
			)
		}

		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Delete data failed: " + err.Error(),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.SkinProblemIndication]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Data deleted successfully",
		},
	)
}

// @router /api/v1/skin-problem-indication [get]
// @tags skin-problem-indication
// @summary Get many skin problem indication detail
// @security BearerAuth
// @accept json
// @produce json
// @param filter query domain.SkinProblemIndicationFilter true "Skin Problem Indication Filter"
// @success 200 {object} domain.PaginationResponse[domain.SkinProblemIndication]
// @failure 400 {object} domain.PaginationResponse[domain.Empty]
func (handler *SkinProblemIndicationHandler) GetMany(c echo.Context) (err error) {
	filter := new(domain.SkinProblemIndicationFilter)
	err = c.Bind(filter)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Failed binding filter: " + err.Error(),
			},
		)
	}

	if err := c.Validate(filter); err != nil {
		if strings.Contains(err.Error(), "Page") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Page and page size must be both either not empty or empty",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid filter: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, totalData, err := handler.Service.GetMany(ctx, filter)

	if err != nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Get many data failed: " + err.Error(),
			},
		)
	}

	responseData := domain.PaginationData[domain.SkinProblemIndication]{
		Content:    data,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalData:  totalData,
		TotalPages: filter.GetTotalPages(totalData),
	}

	return c.JSON(
		http.StatusOK,
		domain.PaginationResponse[domain.SkinProblemIndication]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    responseData,
			Message: "Get many data successfully",
		},
	)
}
