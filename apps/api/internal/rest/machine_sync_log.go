package rest

import (
	"api/domain"
	"context"
	"net/http"

	"github.com/labstack/echo/v4"
)

//go:generate mockery
type MachineSyncLogService interface {
	CreateMachineSyncLog(
		ctx context.Context,
		data *domain.MachineSyncLog,
	) (*domain.MachineSyncLog, error)
	GetManyMachineSyncLog(
		ctx context.Context,
		filter *domain.MachineSyncLogFilter,
	) ([]*domain.MachineSyncLog, *domain.MachineSyncLogStatCount, int, error)
}

type MachineSyncLogHandler struct {
	Service MachineSyncLogService
}

func NewMachineSyncLogHandler(
	e *echo.Group,
	service MachineSyncLogService,
) {
	handler := &MachineSyncLogHandler{
		Service: service,
	}

	e.POST("/machine-sync-log", handler.CreateMachineSyncLog)
	e.GET("/machine-sync-log", handler.GetManyMachineSyncLogs)
}

// @router /api/v1/machine-sync-log [post]
// @tags machine-sync-log
// @summary Create machine sync log
// @accept json
// @produce json
// @param request body domain.CreateMachineSyncLog true "Request body"
// @success 201 {object} domain.SingleResponse[domain.MachineSyncLog]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (h *MachineSyncLogHandler) CreateMachineSyncLog(c echo.Context) (err error) {
	var request *domain.CreateMachineSyncLog
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  "error",
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	log := request.ToMachineSyncLog()
	createdLog, err := h.Service.CreateMachineSyncLog(ctx, &log)
	if err != nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  "error",
				Message: "Failed to create machine sync log: " + err.Error(),
			},
		)
	}

	return c.JSON(http.StatusCreated, domain.SingleResponse[domain.MachineSyncLog]{
		Code:    http.StatusCreated,
		Status:  "success",
		Message: "Machine sync log created successfully",
		Data:    *createdLog,
	})
}

// @router /api/v1/machine-sync-log [get]
// @tags machine-sync-log
// @summary Get many machine sync log
// @accept json
// @produce json
// @param filter query domain.MachineSyncLogFilter false "Filter parameters"
// @success 200 {object} domain.PaginationStatsResponse[domain.MachineSyncLog, domain.MachineSyncLogStatCount]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
// @failure 500 {object} domain.SingleResponse[domain.Empty]
func (handler *MachineSyncLogHandler) GetManyMachineSyncLogs(c echo.Context) (err error) {
	filter := new(domain.MachineSyncLogFilter)
	err = c.Bind(filter)
	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  "error",
				Message: "Invalid filter: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	logs, stats, total, err := handler.Service.GetManyMachineSyncLog(ctx, filter)
	if err != nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  "error",
				Message: "Failed to get machine sync logs: " + err.Error(),
			},
		)
	}

	res := domain.PaginationStatsData[*domain.MachineSyncLog, *domain.MachineSyncLogStatCount]{
		Content:    logs,
		Stats:      stats,
		TotalData:  total,
		TotalPages: filter.GetTotalPages(total),
		Page:       filter.Page,
		PageSize:   filter.PageSize,
	}

	return c.JSON(http.StatusOK, domain.PaginationStatsResponse[*domain.MachineSyncLog, *domain.MachineSyncLogStatCount]{
		Code:    http.StatusOK,
		Status:  "success",
		Message: "Machine sync logs retrieved successfully",
		Data:    res,
	})
}
