package rest_test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
)

func TestAuthRest(test *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.AuthService)
		handler     = &rest.AuthHandler{mockService}
	)

	reqBody := domain.LoginRequest{
		Email:    "<EMAIL>",
		Password: "test-password",
	}

	reqBytes, err := json.Marshal(reqBody)
	require.NoError(test, err)

	test.Run("Login API", func(t *testing.T) {
		svc := mockService.On(
			"GenerateToken",
			mock.Anything,
			mock.AnythingOfType("*domain.LoginRequest"),
		)

		t.Run("Success", func(t *testing.T) {
			token := "UwU"
			svc.
				Return(&token, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/auth/login",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Login(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed email not found", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Unauthorized")).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/auth/login",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Login(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusUnauthorized, rec.Code)
		})

		t.Run("Failed incorrect password", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Unauthorized")).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/auth/login",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Login(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusUnauthorized, rec.Code)
		})
	})
}
