package rest

import (
	"api/domain"
	internalMiddleware "api/internal/rest/middleware"
	"context"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
)

//go:generate mockery
type FaceAgingService interface {
	FaceAgingWithConcern(
		ctx context.Context,
		id string,
		data *domain.FaceAgingConcernRequest,
	) (*domain.FaceAgingConcernResponse, error)
}

type FaceAgingHandler struct {
	Service FaceAgingService
}

func NewFaceAgingHandler(
	e *echo.Group,
	service FaceAgingService,
) {
	handler := &FaceAgingHandler{
		Service: service,
	}

	generationRoutes := e.Group("")
	generationRoutes.Use(internalMiddleware.SetRequestContextWithTimeout(60 * time.Second))

	generationRoutes.POST("/face-aging/concerns/:id", handler.FaceAgingWithConcern)
}

// @router /api/v1/face-aging/concerns/{id} [post]
// @tags face-aging
// @summary Face aging with concern
// @accept json
// @produce json
// @param id path string true "Skin analyze ID"
// @param request body domain.FaceAgingConcernRequest true "Request body"
// @success 200 {object} domain.SingleResponse[domain.FaceAgingConcernResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
// @failure 404 {object} domain.SingleResponse[domain.Empty]
// @failure 500 {object} domain.SingleResponse[domain.Empty]
func (handler *FaceAgingHandler) FaceAgingWithConcern(c echo.Context) (err error) {
	var request *domain.FaceAgingConcernRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Message: "Invalid request",
			},
		)
	}

	skinAnalyzeID := c.Param("id")
	ctx := c.Request().Context()

	response, err := handler.Service.FaceAgingWithConcern(ctx, skinAnalyzeID, request)
	if err != nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Message: "Failed to process request",
			},
		)
	}

	return c.JSON(http.StatusOK, domain.SingleResponse[domain.FaceAgingConcernResponse]{
		Code:    http.StatusOK,
		Message: "Generated image successfully",
		Data:    *response,
		Status:  "success",
	})
}
