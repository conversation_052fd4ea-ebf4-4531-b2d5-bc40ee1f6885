package rest_test

import (
	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func TestSkinAnalyzeHandler(t *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.SkinAnalyzeService)
		handler     = &rest.SkinAnalyzeHandler{mockService}
	)

	actualAge := 30
	phoneNumber := "1234567890"
	evaluationRate := 5
	skinAge := 30
	skinCondition := "Good"
	plTexture := 75
	uvPorphyrin := 80
	uvPigmentation := 60
	uvMoisture := 70
	sensitiveArea := 70
	brownArea := 80
	uvDamage := 90
	suggestion := "Use sunscreen"
	skinAnalyze := &domain.SkinAnalyze{
		Name:           "<PERSON> Doe",
		ActualAge:      &actualAge,
		InputDate:      "2023-10-01",
		PhoneNumber:    &phoneNumber,
		EvaluationRate: &evaluationRate,
		SkinAge:        &skinAge,
		SkinCondition:  &skinCondition,
		RGBPore:        75,
		RGBSpot:        50,
		RGBWrinkle:     70,
		PLTexture:      &plTexture,
		UVPorphyrin:    &uvPorphyrin,
		UVPigmentation: &uvPigmentation,
		UVMoisture:     &uvMoisture,
		SensitiveArea:  &sensitiveArea,
		BrownArea:      &brownArea,
		UVDamage:       &uvDamage,
		Suggestion:     &suggestion,
		PathImages:     []string{"image1.jpg", "image2.jpg"},
		PathPDF:        "/path/to/pdf",
	}

	reqBody := domain.SkinAnalyzeUploadRequest{
		Images: []string{"image1.jpg", "image2.jpg"},
		Pdf:    "path/to/report.pdf",
	}

	reqBytes, err := json.Marshal(reqBody)
	require.NoError(t, err)

	t.Run("Success create skin analyze", func(t *testing.T) {
		mockService.On(
			"CreateSkinAnalyze",
			mock.Anything,
			mock.AnythingOfType("*domain.SkinAnalyzeUploadRequest"),
		).
			Return(skinAnalyze, nil).
			Once()

		req := httptest.NewRequest(
			http.MethodPost,
			"/skin-analyze/upload",
			bytes.NewReader(reqBytes),
		)

		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.CreateSkinAnalyze(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusCreated, rec.Code)
		mockService.AssertExpectations(t)
	})

	t.Run("Success get skin analyze by id", func(t *testing.T) {
		mockService.On(
			"GetSkinAnalyzeByID",
			mock.Anything,
			mock.AnythingOfType("string"),
		).
			Return(skinAnalyze, nil).
			Once()
		req := httptest.NewRequest(
			http.MethodGet,
			"/skin-analyze/123",
			nil,
		)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetPath("/skin-analyze/:id")
		c.SetParamNames("id")
		c.SetParamValues("123")
		err := handler.GetSkinAnalyzeByID(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)
		var response domain.SingleResponse[domain.SkinAnalyze]
		err = json.NewDecoder(rec.Body).Decode(&response)
		assert.NoError(t, err)
		assert.Equal(t, skinAnalyze.Name, response.Data.Name)
		mockService.AssertExpectations(t)
	})

	t.Run("Success get many skin analyzes", func(t *testing.T) {
		mockService.On(
			"GetManySkinAnalyzes",
			mock.Anything,
			mock.AnythingOfType("*domain.SkinAnalyzeFilter"),
		).
			Return([]*domain.SkinAnalyze{skinAnalyze}, 1, nil).
			Once()
		req := httptest.NewRequest(
			http.MethodGet,
			"/skin-analyze?page=1&page_size=10",
			nil,
		)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetPath("/skin-analyze")
		c.SetParamNames("page", "page_size")
		c.SetParamValues("1", "10")
		err := handler.GetManySkinAnalyzes(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)
		var response domain.PaginationResponse[domain.SkinAnalyze]
		err = json.NewDecoder(rec.Body).Decode(&response)
		assert.NoError(t, err)
		assert.Equal(t, 1, response.Data.TotalData)
		assert.Equal(t, 1, len(response.Data.Content))
		assert.Equal(t, skinAnalyze.Name, response.Data.Content[0].Name)
		mockService.AssertExpectations(t)
	})

}
