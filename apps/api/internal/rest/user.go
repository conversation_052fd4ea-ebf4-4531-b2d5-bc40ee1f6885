package rest

import (
	"context"
	"net/http"
	"regexp"
	"strings"

	"github.com/labstack/echo/v4"

	"api/domain"
	"api/utils"
)

//go:generate mockery
type UserService interface {
	Create(
		ctx context.Context,
		request *domain.User,
	) (*domain.User, error)
	GetByID(
		ctx context.Context,
		id *string,
	) (*domain.User, error)
	UpdateByID(
		ctx context.Context,
		request *domain.User,
	) (*domain.User, error)
	DeleteByID(
		ctx context.Context,
		id *string,
	) (*domain.User, error)
	GetMany(
		ctx context.Context,
		filter *domain.UserFilter,
	) (*domain.PaginationData[domain.UserResponse], error)
	UpdatePassword(
		ctx context.Context,
		id *string,
		password *string,
	) error
}

type UserHandler struct {
	Service UserService
}

func NewUserHandler(e *echo.Group, service UserService) {
	handler := &UserHandler{service}

	e.POST("/user", handler.Create)
	e.PUT("/user/:id", handler.UpdateByID)
	e.DELETE("/user/:id", handler.DeleteByID)
	e.GET("/user", handler.GetMany)
	e.PUT("/user/:id/update-password", handler.UpdatePassword)
}

func NewPublicUserHandler(e *echo.Group, service UserService) {
	handler := &UserHandler{service}

	e.GET("/user/:id", handler.GetByID)
}

// @router /api/v1/user [post]
// @tags user
// @summary Create new user
// @security BearerAuth
// @accept json
// @produce json
// @param request body domain.UserRequest true "Request body"
// @success 201 {object} domain.SingleResponse[domain.UserResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
// @failure 409 {object} domain.SingleResponse[domain.Empty]
// @failure 500 {object} domain.SingleResponse[domain.Empty]
func (handler *UserHandler) Create(c echo.Context) (err error) {
	var request *domain.UserRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	user := request.ToRepo()

	data, err := handler.Service.Create(ctx, &user)

	if err != nil {
		if strings.Contains(err.Error(), "already exist") {
			return c.JSON(
				http.StatusConflict,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusConflict,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Create user failed: " + err.Error(),
			},
		)
	}

	return c.JSON(
		http.StatusCreated,
		domain.SingleResponse[domain.UserResponse]{
			Code:    http.StatusCreated,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "User created successfully",
		},
	)
}

// @router /api/v1/user/{id} [get]
// @tags user
// @summary Get user detail by id
// @accept json
// @produce json
// @param id path string true "User ID"
// @success 200 {object} domain.SingleResponse[domain.UserResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
// @failure 404 {object} domain.SingleResponse[domain.Empty]
// @failure 500 {object} domain.SingleResponse[domain.Empty]
func (handler *UserHandler) GetByID(c echo.Context) (err error) {
	id := c.Param("id")

	ctx := c.Request().Context()
	data, err := handler.Service.GetByID(ctx, &id)

	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(
				http.StatusNotFound,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusNotFound,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Get user failed: " + err.Error(),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.UserResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "Get user successfully",
		},
	)
}

// @router /api/v1/user/{id} [put]
// @tags user
// @summary Update user detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "User ID"
// @param request body domain.UserRequest true "Request body"
// @success 200 {object} domain.SingleResponse[domain.UserResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
// @failure 404 {object} domain.SingleResponse[domain.Empty]
// @failure 500 {object} domain.SingleResponse[domain.Empty]
func (handler *UserHandler) UpdateByID(c echo.Context) (err error) {
	id := c.Param("id")
	var request *domain.UserRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	user := request.ToRepo()
	user.ID = id

	data, err := handler.Service.UpdateByID(ctx, &user)

	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(
				http.StatusNotFound,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusNotFound,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Update user failed: " + err.Error(),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.UserResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "User updated successfully",
		},
	)
}

// @router /api/v1/user/{id} [delete]
// @tags user
// @summary Delete user by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "User ID"
// @success 200 {object} domain.SingleResponse[domain.UserResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
// @failure 404 {object} domain.SingleResponse[domain.Empty]
// @failure 500 {object} domain.SingleResponse[domain.Empty]
func (handler *UserHandler) DeleteByID(c echo.Context) (err error) {
	id := c.Param("id")

	ctx := c.Request().Context()
	data, err := handler.Service.DeleteByID(ctx, &id)

	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(
				http.StatusNotFound,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusNotFound,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Delete user failed: " + err.Error(),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.UserResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "User deleted successfully",
		},
	)
}

// @router /api/v1/user [get]
// @tags user
// @summary Get many user detail
// @security BearerAuth
// @accept json
// @produce json
// @param filter query domain.UserFilter true "User Filter"
// @success 200 {object} domain.PaginationResponse[domain.UserResponse]
// @failure 400 {object} domain.PaginationResponse[domain.Empty]
// @failure 500 {object} domain.PaginationResponse[domain.Empty]
func (handler *UserHandler) GetMany(c echo.Context) (err error) {
	var filter domain.UserFilter
	if err := c.Bind(&filter); err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.PaginationResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(filter); err != nil {
		if strings.Contains(err.Error(), "Page") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Page and page size must be both either not empty or empty",
				},
			)
		}

		// Check domain for list of roles.
		validateFilterRoles := regexp.MustCompile(`UserFilter\.Roles.*oneof`)
		if validateFilterRoles.MatchString(err.Error()) {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Roles must be one of: admin, branch, client",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid filter: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.GetMany(ctx, &filter)

	if err != nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.PaginationResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Get users failed: " + err.Error(),
			},
		)
	}

	// prevent nil pointer dereference error.
	if data == nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Get many data failed: data is nil",
			},
		)
	}

	if data.Content == nil {
		data.Content = []domain.UserResponse{}
	}

	return c.JSON(
		http.StatusOK,
		domain.PaginationResponse[domain.UserResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Get users successfully",
		},
	)
}

// @router /api/v1/user/{id}/update-password [put]
// @tags user
// @summary Update user password by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "User ID"
// @param request body domain.UpdatePasswordRequest true "Request body"
// @success 200 {object} domain.SingleResponse[domain.Empty]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
// @failure 404 {object} domain.SingleResponse[domain.Empty]
// @failure 500 {object} domain.SingleResponse[domain.Empty]
func (handler *UserHandler) UpdatePassword(c echo.Context) (err error) {
	id := c.Param("id")
	var request *domain.UpdatePasswordRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	err = handler.Service.UpdatePassword(ctx, &id, &request.Password)

	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(
				http.StatusNotFound,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusNotFound,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Update password failed: " + err.Error(),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.Empty]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Message: "Password updated successfully",
		},
	)
}
