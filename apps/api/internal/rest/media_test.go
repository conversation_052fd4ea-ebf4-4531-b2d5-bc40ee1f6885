package rest_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
	"api/utils"
)

func TestMediaRest(test *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.MediaService)
		handler     = &rest.MediaHandler{mockService}
	)

	e.Validator = utils.NewCustomValidator()

	reqBody := domain.MediaS3RequestPresignUrl{
		Filename: "test_file.mp4",
	}

	reqBytes, err := json.Marshal(reqBody)
	require.NoError(test, err)

	url := "https://example.com/presigned-url"

	test.Run("Success upload API", func(t *testing.T) {
		mockService.On(
			"UploadS3",
			mock.Anything,
			mock.AnythingOfType("string"),
		).
			Return(&url, nil).
			Once()

		req := httptest.NewRequest(
			http.MethodPost,
			"/media/s3/upload/presign-url",
			bytes.NewReader(reqBytes),
		)

		rec := httptest.NewRecorder()
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		c := e.NewContext(req, rec)

		err := handler.UploadS3(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusCreated, rec.Code)
	})
}
