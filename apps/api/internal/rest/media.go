package rest

import (
	"context"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"

	"api/domain"
	internalMiddleware "api/internal/rest/middleware"
	"api/utils"
)

//go:generate mockery
type MediaService interface {
	UploadS3(
		ctx context.Context,
		filename string,
	) (*string, error)
}

type MediaHandler struct {
	Service MediaService
}

func NewMediaHandler(
	e *echo.Group,
	service MediaService,
) {
	handler := &MediaHandler{service}

	mediaRoutes := e.Group("")
	mediaRoutes.Use(
		internalMiddleware.SetRequestContextWithTimeout(60 * time.Second),
	)

	mediaRoutes.POST("/media/s3/upload/presign-url", handler.UploadS3)
}

// @router /api/v1/media/s3/upload/presign-url [post]
// @tags media
// @summary Upload media file to AWS S3
// @accept json
// @produce json
// @param request body domain.MediaS3RequestPresignUrl true "Info file"
// @success 201 {object} domain.SingleResponse[domain.MediaS3Response]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *MediaHandler) UploadS3(c echo.Context) (err error) {
	var request *domain.MediaS3RequestPresignUrl
	err = c.Bind(&request)

	ctx := c.Request().Context()
	signedUrl, err := handler.Service.UploadS3(ctx, request.Filename)

	if err != nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Generate presigned url failed: " + err.Error(),
			},
		)
	}

	return c.JSON(
		http.StatusCreated,
		domain.SingleResponse[domain.MediaS3Response]{
			Code:    http.StatusCreated,
			Status:  utils.SuccessMsg,
			Data:    domain.MediaS3Response{PresignUrl: signedUrl},
			Message: "Presigned url generated successfully",
		},
	)
}
