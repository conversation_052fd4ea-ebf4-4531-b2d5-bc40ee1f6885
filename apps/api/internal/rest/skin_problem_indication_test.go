package rest_test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
	"api/utils"
)

func TestSkinProblemIndicationRest(t *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.SkinProblemIndicationService)
		handler     = &rest.SkinProblemIndicationHandler{mockService}
	)

	e.Validator = utils.NewCustomValidator()

	reqBody := domain.SkinProblemIndicationRequest{
		Name: "Test create skin problem indication",
	}

	reqBytes, err := json.Marshal(reqBody)
	require.NoError(t, err)

	sharedData := domain.SkinProblemIndication{
		Name: reqBody.Name,
	}

	t.Run("Create API", func(t *testing.T) {
		svc := mockService.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.SkinProblemIndicationRequest"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedData, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/skin-problem-indication",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusCreated, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Error Create service")).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/skin-problem-indication",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})

		t.Run("Failed invalid request", func(t *testing.T) {
			req := httptest.NewRequest(
				http.MethodPost,
				"/skin-problem-indication",
				bytes.NewReader([]byte(`invalid-json`)),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusBadRequest, rec.Code)
		})
	})

	t.Run("GetByID API", func(t *testing.T) {
		svc := mockService.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedData, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/skin-problem-indication/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.GetByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Data not found")).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/skin-problem-indication/%v", utils.DummyID),
				nil,
			)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.GetByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	t.Run("UpdateByID API", func(t *testing.T) {
		svc := mockService.On(
			"UpdateByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
			mock.AnythingOfType("*domain.SkinProblemIndicationRequest"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedData, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/skin-problem-indication/%v", utils.DummyID),
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdateByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Data is not found")).
				Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/skin-problem-indication/%v", utils.DummyID),
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdateByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	t.Run("DeleteByID API", func(t *testing.T) {
		svc := mockService.On(
			"DeleteByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedData, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodDelete,
				fmt.Sprintf("/skin-problem-indication/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.DeleteByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Data is not found")).
				Once()

			req := httptest.NewRequest(
				http.MethodDelete,
				fmt.Sprintf("/skin-problem-indication/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.DeleteByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	t.Run("GetMany API", func(t *testing.T) {
		svc := mockService.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.SkinProblemIndicationFilter"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return([]domain.SkinProblemIndication{sharedData}, 1, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/skin-problem-indication?page=1&page_size=10"),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.
				Return(nil, 0, fmt.Errorf("Error GetMany service")).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/skin-problem-indication?page=1&page_size=10"),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})
	})
}
