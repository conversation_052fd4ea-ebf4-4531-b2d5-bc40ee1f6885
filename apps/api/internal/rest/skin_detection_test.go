package rest_test

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
	"api/utils"
)

func TestSkinDetectionRest(test *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.SkinDetectionService)
		handler     = &rest.SkinDetectionHandler{mockService}
	)

	test.Run("Success detect wrinkles", func(t *testing.T) {
		response := domain.SkinDetectionResponse{
			GeneratedImageURL: "https://example.com/face_aging_wrinkles_detected.png",
		}

		mockService.On(
			"DetectWrinkles",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("*domain.SkinDetectionQueryParams"),
		).
			Return(&response, nil).
			Once()

		req := httptest.NewRequest(
			http.MethodGet,
			fmt.Sprintf("/skin-detection/wrinkles/%v", utils.DummyID),
			nil,
		)

		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("id")
		c.SetParamValues(utils.DummyID)

		err := handler.DetectWrinkles(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)
	})

	test.Run("Failed detect wrinkles not found", func(t *testing.T) {
		mockService.On(
			"DetectWrinkles",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("*domain.SkinDetectionQueryParams"),
		).
			Return(nil, fmt.Errorf("File is not found")).
			Once()

		req := httptest.NewRequest(
			http.MethodGet,
			fmt.Sprintf("/skin-detection/wrinkles/%v", utils.DummyID),
			nil,
		)

		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("id")
		c.SetParamValues(utils.DummyID)

		err := handler.DetectWrinkles(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusNotFound, rec.Code)
	})

	test.Run("Success detect pores", func(t *testing.T) {
		response := domain.SkinDetectionResponse{
			GeneratedImageURL: "https://example.com/face_aging_pores_detected.png",
		}

		mockService.On(
			"DetectPores",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("*domain.SkinDetectionQueryParams"),
		).
			Return(&response, nil).
			Once()

		req := httptest.NewRequest(
			http.MethodGet,
			fmt.Sprintf("/skin-detection/pores/%v", utils.DummyID),
			nil,
		)

		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("id")
		c.SetParamValues(utils.DummyID)

		err := handler.DetectPores(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)
	})

	test.Run("Failed detect pores not found", func(t *testing.T) {
		mockService.On(
			"DetectPores",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("*domain.SkinDetectionQueryParams"),
		).
			Return(nil, fmt.Errorf("File is not found")).
			Once()

		req := httptest.NewRequest(
			http.MethodGet,
			fmt.Sprintf("/skin-detection/pores/%v", utils.DummyID),
			nil,
		)

		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("id")
		c.SetParamValues(utils.DummyID)

		err := handler.DetectPores(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusNotFound, rec.Code)
	})
}
