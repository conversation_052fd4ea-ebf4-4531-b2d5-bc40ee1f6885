package http_test

import (
	"api/domain"
	httpRepo "api/internal/repositories/http"
	"context"
	"os"
	"strings"

	netHttp "net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFaceAgingHttp(t *testing.T) {
	server := httptest.NewServer(netHttp.HandlerFunc(func(w netHttp.ResponseWriter, r *netHttp.Request) {
		w.Header().Set("Content-Type", "application/json")

		if strings.Contains(r.URL.Path, "/face-aging/concerns") {
			// Mock the response for the upload image endpoint
			w.WriteHeader(netHttp.StatusOK)
			w.Write([]byte(`{
                "image_url": [
                    "https://example.com/image.jpg",
                    "https://example.com/image2.jpg"
                ],
                "generated_image": [
                    {
                        "concern": "wrinkles",
                        "generated_image_url": "https://example.com/image.jpg",
                        "selected_area_url": "https://example.com/image.png"
                    },
                    {
                        "concern": "pores",
                        "generated_image_url": "https://example.com/image.jpg",
                        "selected_area_url": "https://example.com/image.png"
                    }
                ]
            }`))
		} else {
			w.WriteHeader(netHttp.StatusNotFound)
		}
	}))
	t.Cleanup(server.Close)

	os.Setenv("ML_API_URL", server.URL)
	http := httpRepo.NewFaceAgingHttp()

	t.Run("Test Face Aging with Specific Concern", func(t *testing.T) {
		// Mock the request data
		data := &domain.FaceAgingConcernMLRequest{
			ImagePath: "path/to/image.jpg",
			MaskPath:  nil,
			Concerns: []domain.FaceAgingConcernDetailMLRequest{
				{
					Concern: domain.FaceAgingConcernWrinkle,
					Areas:   []domain.FaceAgingArea{domain.FaceAgingAreaUpper, domain.FaceAgingAreaMid},
				},
				{
					Concern: domain.FaceAgingConcernPore,
					Areas:   []domain.FaceAgingArea{domain.FaceAgingAreaLower},
				},
			},
		}

		// Call the UploadImage method
		ctx := context.Background()
		result, err := http.FaceAgingWithConcern(ctx, data)
		require.NoError(t, err)
		require.NotNil(t, result)

		assert.Len(t, result.ImageURL, 2)
		assert.Equal(t, "https://example.com/image.jpg", result.ImageURL[0])
		assert.Equal(t, "https://example.com/image2.jpg", result.ImageURL[1])
		assert.Len(t, result.GeneratedImage, 2)
		assert.Equal(t, domain.FaceAgingConcernWrinkle, result.GeneratedImage[0].Concern)
		assert.Equal(t, "https://example.com/image.jpg", result.GeneratedImage[0].GeneratedImageURL)
		assert.Equal(t, "https://example.com/image.png", result.GeneratedImage[0].SelectedAreaImageURL)
		assert.Equal(t, domain.FaceAgingConcernPore, result.GeneratedImage[1].Concern)
		assert.Equal(t, "https://example.com/image.jpg", result.GeneratedImage[1].GeneratedImageURL)
		assert.Equal(t, "https://example.com/image.png", result.GeneratedImage[1].SelectedAreaImageURL)
	})
}
