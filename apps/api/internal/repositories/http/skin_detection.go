package http

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"

	"github.com/rs/zerolog/log"

	"api/domain"
)

type SkinDetectionHttp struct {
	client  *http.Client
	baseURL string
}

func NewSkinDetectionHttp() *SkinDetectionHttp {
	mlApiUrl := os.Getenv("ML_API_URL")
	if mlApiUrl == "" {
		mlApiUrl = "http://localhost:8080"
	}

	return &SkinDetectionHttp{
		client:  &http.Client{},
		baseURL: mlApiUrl,
	}
}

func (h *SkinDetectionHttp) DetectWrinkles(
	ctx context.Context,
	data *domain.SkinDetectionMLRequest,
) (*domain.SkinDetectionMLResponse, error) {
	url := fmt.Sprintf("%s/skin-detection/wrinkles", h.baseURL)

	reqBody, err := json.Marshal(data)

	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal request body")
		return nil, err
	}

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to create request")
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := h.client.Do(req)

	if err != nil {
		log.Error().Err(err).Msg("Failed to send request")
		return nil, err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, err := io.ReadAll(resp.Body)

		if err != nil {
			return nil, fmt.Errorf("Error reading http response: %v", err)
		}

		errResp := string(bodyBytes)
		log.Error().Msgf("Error response body: %s", errResp)

		switch {
		case strings.Contains(errResp, "NoSuchKey"):
			return nil, fmt.Errorf("S3 object %s does not exist", data.ImagePath)
		default:
			return nil, fmt.Errorf("Error ML API: %s", errResp)
		}
	}

	var response domain.SkinDetectionMLResponse
	err = json.NewDecoder(resp.Body).Decode(&response)

	if err != nil {
		log.Error().Err(err).Msg("Failed to decode response body")
		return nil, err
	}

	return &response, nil
}

func (h *SkinDetectionHttp) DetectPores(
	ctx context.Context,
	data *domain.SkinDetectionMLRequest,
) (*domain.SkinDetectionMLResponse, error) {
	url := fmt.Sprintf("%s/skin-detection/pores", h.baseURL)

	reqBody, err := json.Marshal(data)

	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal request body")
		return nil, err
	}

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to create request")
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := h.client.Do(req)

	if err != nil {
		log.Error().Err(err).Msg("Failed to send request")
		return nil, err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, err := io.ReadAll(resp.Body)

		if err != nil {
			return nil, fmt.Errorf("Error reading http response: %v", err)
		}

		errResp := string(bodyBytes)
		log.Error().Msgf("Error response body: %s", errResp)

		switch {
		case strings.Contains(errResp, "NoSuchKey"):
			return nil, fmt.Errorf("S3 object %s does not exist", data.ImagePath)
		default:
			return nil, fmt.Errorf("Error ML API: %s", errResp)
		}
	}

	var response domain.SkinDetectionMLResponse
	err = json.NewDecoder(resp.Body).Decode(&response)

	if err != nil {
		log.Error().Err(err).Msg("Failed to decode response body")
		return nil, err
	}

	return &response, nil
}
