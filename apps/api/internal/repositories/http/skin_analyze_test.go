package http_test

import (
	"api/domain"
	httpRepo "api/internal/repositories/http"
	"context"
	"os"

	netHttp "net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSkinAnalyzeHttp(test *testing.T) {
	server := httptest.NewServer(netHttp.HandlerFunc(func(w netHttp.ResponseWriter, r *netHttp.Request) {
		w.<PERSON>er().Set("Content-Type", "application/json")

		if r.URL.Path == "/skin-analyze/upload" {
			// Mock the response for the upload image endpoint
			w.<PERSON>rite<PERSON>eader(netHttp.StatusOK)
			w.Write([]byte(`{
                "data": {
                    "name": "<PERSON>",
                    "actual_age": 30,
                    "input_date": "2023-10-01",
                    "phone_number": "1234567890",
                    "evaluation_rate": 5,
                    "skin_age": 30,
                    "skin_condition": "Good",
                    "rgb_pore": 75,
                    "rgb_spot": 50,
                    "rgb_wrinkle": 70,
                    "pl_texture": 75,
                    "uv_porphyrin": 80,
                    "uv_pigmentation": 60,
                    "uv_moisture": 70,
                    "sensitive_area": 70,
                    "brown_area": 80,
                    "uv_damage": 90,
                    "suggestion": "Use sunscreen",
                    "path_images": ["path/to/image1.jpg", "path/to/image2.jpg"],
                    "path_pdf": "path/to/report.pdf"
                },
                "message": "success"
            }`))
		} else {
			w.WriteHeader(netHttp.StatusNotFound)
        }
	}))
	test.Cleanup(server.Close)

	os.Setenv("ML_API_URL", server.URL)
	http := httpRepo.NewSkinAnalyzeHttp()

	test.Run("Test UploadImage", func(t *testing.T) {
		// Mock the request data
		data := &domain.SkinAnalyzeUploadRequest{
			Images: []string{"path/to/image1.jpg", "path/to/image2.jpg"},
			Pdf:    "path/to/report.pdf",
		}

		// Call the UploadImage method
		ctx := context.Background()
		result, err := http.UploadImage(ctx, data)
		require.NoError(t, err)
		require.NotNil(t, result)

		assert.Equal(t, "John Doe", result.Name)
	})
}
