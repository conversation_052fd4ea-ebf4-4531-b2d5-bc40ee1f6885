package http_test

import (
	"api/domain"
	httpRepo "api/internal/repositories/http"
	"context"
	"os"
	"strings"

	netHttp "net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestRecommendationHttp(test *testing.T) {
	server := httptest.NewServer(netHttp.HandlerFunc(func(w netHttp.ResponseWriter, r *netHttp.Request) {
		w.<PERSON>er().Set("Content-Type", "application/json")

		if strings.Contains(r.URL.Path, "/recommendation-treatment") {
			// Mock the response for the upload image endpoint
			w.<PERSON>riteHeader(netHttp.StatusOK)
			w.Write([]byte(`{
                "text": "Recommended treatment for the image",
                "video": "https://example.com/video.mp4",
                "data": [
                    {
                        "id": "12345",
                        "type": "treatment",
                        "sku": "sku123",
                        "name": "Treatment Name",
                        "concern_group": "Concern Group",
                        "concern": "Concern",
                        "price": 100,
                        "created_by": "user123",
                        "created_at": 1633072800000,
                        "updated_by": "user456",
                        "updated_at": 1633072800000
                    },
                    {
                        "id": "67890",
                        "type": "treatment",
                        "sku": "sku456",
                        "name": "Another Treatment",
                        "concern_group": "Another Concern Group",
                        "concern": "Another Concern",
                        "price": 200,
                        "created_by": "user789",
                        "created_at": 1633072800000,
                        "updated_by": "user012",
                        "updated_at": 1633072800000
                    }
                ]
            }`))
		} else {
			w.WriteHeader(netHttp.StatusNotFound)
		}
	}))
	test.Cleanup(server.Close)

	os.Setenv("ML_API_URL", server.URL)
	http := httpRepo.NewRecommendationHttp()

	test.Run("Test Recommendation", func(t *testing.T) {
		// Mock the request data
		actualAge := 30
		phoneNumber := "1234567890"
		evaluationRate := 5
		skinAge := 30
		skinCondition := "Good"
		plTexture := 75
		uvPorphyrin := 80
		uvPigmentation := 60
		uvMoisture := 70
		sensitiveArea := 70
		brownArea := 80
		uvDamage := 90
		suggestion := "Use sunscreen"
		skinAnalyze := domain.SkinAnalyze{
			ID:             "12345",
			Name:           "John Doe",
			ActualAge:      &actualAge,
			InputDate:      "2023-10-01",
			PhoneNumber:    &phoneNumber,
			EvaluationRate: &evaluationRate,
			SkinAge:        &skinAge,
			SkinCondition:  &skinCondition,
			RGBPore:        75,
			RGBSpot:        50,
			RGBWrinkle:     70,
			PLTexture:      &plTexture,
			UVPorphyrin:    &uvPorphyrin,
			UVPigmentation: &uvPigmentation,
			UVMoisture:     &uvMoisture,
			SensitiveArea:  &sensitiveArea,
			BrownArea:      &brownArea,
			UVDamage:       &uvDamage,
			Suggestion:     &suggestion,
			PathImages:     []string{"image1.jpg", "image2.jpg"},
			PathPDF:        "/path/to/pdf",
			Timestamp: domain.Timestamp{
				CreatedAt: 1633072800000,
				UpdatedAt: 1633072800000,
			},
		}

		topConcern := []domain.RecommendationConcern{
			{
				Name:  "Concern",
				Label: "Concern Label",
				Score: 70,
			},
			{
				Name:  "Concern 2",
				Label: "Concern Label 2",
				Score: 68,
			},
			{
				Name:  "Concern 3",
				Label: "Concern Label 3",
				Score: 65,
			},
		}

		concernAnswer := domain.ConcernAnswer{
			ConcernKey: "Concern",
			Answer:     "Answer",
			VideoURL:   nil,
		}

		concernGroups := []*domain.ConcernGroup{
			{
				Name:    "Concern Group",
				Concern: "Concern",
			},
			{
				Name:    "Concern Group 2",
				Concern: "Concern 2",
			},
		}

		treatments := &[]domain.TreatmentProductGetMany{
			{
				TreatmentProduct: domain.TreatmentProduct{
					ID:          "12345",
					ItemCode:    "sku123",
					Name:        "Treatment Name",
					Type:        domain.TreatmentType,
					Description: "Treatment Description",
					Price:       100,
					Quantity:    1,
				},
				Category: []domain.TreatmentCategory{},
			},
			{
				TreatmentProduct: domain.TreatmentProduct{
					ID:          "123456",
					ItemCode:    "sku1234",
					Name:        "Treatment Name 2",
					Type:        domain.TreatmentType,
					Description: "Treatment Description 2",
					Price:       100,
					Quantity:    1,
				},
				Category: []domain.TreatmentCategory{},
			},
		}

		data := &domain.RecommendationTreatmentMLRequest{
			SkinAnalyze:   skinAnalyze,
			TopConcern:    topConcern,
			ConcernAnswer: concernAnswer,
			ConcernGroups: concernGroups,
			Treatments:    treatments,
		}

		// Call the UploadImage method
		ctx := context.Background()
		result, err := http.RecommendationTreatment(ctx, data)
		require.NoError(t, err)
		require.NotNil(t, result)

		assert.Len(t, result.Data, 2)
		assert.Equal(t, "Recommended treatment for the image", result.Text)
		assert.Equal(t, "https://example.com/video.mp4", result.Video)
	})
}
