package http

import (
	"api/domain"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"

	"github.com/rs/zerolog/log"
)

type RecommendationHttp struct {
	client  *http.Client
	baseURL string
}

func NewRecommendationHttp() *RecommendationHttp {
	mlApiUrl := os.Getenv("ML_API_URL")
	if mlApiUrl == "" {
		mlApiUrl = "http://localhost:8080"
	}
	return &RecommendationHttp{
		client:  &http.Client{},
		baseURL: mlApiUrl,
	}
}

func (h *RecommendationHttp) RecommendationTreatment(
	ctx context.Context,
	data *domain.RecommendationTreatmentMLRequest,
) (*domain.RecommendationTreatmentMLResponse, error) {
	url := fmt.Sprintf("%s/recommendation-treatment", h.baseURL)

	reqBody, err := json.Marshal(data)
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal request body")
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(reqBody))
	if err != nil {
		log.Error().Err(err).Msg("Failed to create request")
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := h.client.Do(req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to send request")
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// log the error response body
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Error().Msgf("Error response body: %s", string(bodyBytes))
		log.Error().Msgf("Failed to get recommendation, status code: %d", resp.StatusCode)
		return nil, fmt.Errorf("failed to get recommendation, status code: %d", resp.StatusCode)
	}

	var response domain.RecommendationTreatmentMLResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	if err != nil {
		log.Error().Err(err).Msg("Failed to decode response body")
		return nil, err
	}

	return &response, nil
}

func (h *RecommendationHttp) GetRecommendation(
	ctx context.Context,
	data *domain.GetRecommendationMLRequest,
) (*domain.RecommendationResponse, error) {
	url := fmt.Sprintf("%s/recommendation", h.baseURL)

	reqBody, err := json.Marshal(data)
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal request body")
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(reqBody))
	if err != nil {
		log.Error().Err(err).Msg("Failed to create request")
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := h.client.Do(req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to send request")
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Error().Msgf("Error response body: %s", string(bodyBytes))
		log.Error().Msgf("Failed to get recommendation, status code: %d", resp.StatusCode)
		return nil, fmt.Errorf("failed to get recommendation, status code: %d", resp.StatusCode)
	}

	var mlResponse struct {
		Data struct {
			Status string                    `json:"status"`
			Data   domain.RecommendationResponse `json:"data"`
			Error  interface{}              `json:"error"`
		} `json:"data"`
	}
	err = json.NewDecoder(resp.Body).Decode(&mlResponse)
	if err != nil {
		log.Error().Err(err).Msg("Failed to decode response body")
		return nil, err
	}

	return &mlResponse.Data.Data, nil
}
