package http_test

import (
	"context"
	"os"
	"strings"

	netHttp "net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"api/domain"
	httpRepo "api/internal/repositories/http"
)

func TestSkinDetectionHttp(test *testing.T) {
	server := httptest.NewServer(
		netHttp.HandlerFunc(
			func(w netHttp.ResponseWriter, r *netHttp.Request) {
				w.Header().Set("Content-Type", "application/json")

				if strings.Contains(r.URL.Path, "/skin-detection/wrinkles") {
					request := []byte(`
					{
						"image_path": "uwu/face_aging_wrinkles_detected.png"
					}
					`)

					// Mock the response for the upload image endpoint
					w.WriteHeader(netHttp.StatusOK)
					w.Write(request)
				} else if strings.Contains(r.URL.Path, "/skin-detection/pores") {
					request := []byte(`
					{
						"image_path": "uwu/face_aging_pores_detected.png"
					}
					`)

					// Mock the response for the upload image endpoint
					w.<PERSON>rite<PERSON>eader(netHttp.StatusOK)
					w.Write(request)
				}
			},
		),
	)

	test.Cleanup(server.Close)

	os.Setenv("ML_API_URL", server.URL)
	http := httpRepo.NewSkinDetectionHttp()

	test.Run("Success get image path for wrinkle detection", func(t *testing.T) {
		data := &domain.SkinDetectionMLRequest{
			ImagePath: "uwu/face_aging_wrinkles.png",
		}

		ctx := context.Background()
		result, err := http.DetectWrinkles(ctx, data)
		require.NoError(t, err)
		require.NotNil(t, result)

		assert.Equal(t, "uwu/face_aging_wrinkles_detected.png", result.ImagePath)
	})

	test.Run("Success get image path for pore detection", func(t *testing.T) {
		data := &domain.SkinDetectionMLRequest{
			ImagePath: "uwu/face_aging_pores.png",
		}

		ctx := context.Background()
		result, err := http.DetectPores(ctx, data)
		require.NoError(t, err)
		require.NotNil(t, result)

		assert.Equal(t, "uwu/face_aging_pores_detected.png", result.ImagePath)
	})
}
