package aws_test

import (
	"context"
	"testing"

	"api/internal/repositories/aws"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/stretchr/testify/assert"
)

func TestPresigner(t *testing.T) {
	// Initialize the AWS SDK configuration
	cfg, err := config.LoadDefaultConfig(context.TODO(), config.WithRegion("ap-southeast-2"))
	if err != nil {
		t.Fatalf("unable to load SDK config, %v", err)
	}

	// Create a new Presigner instance
	presigner := aws.NewPresigner(cfg)

	// Define test parameters
	bucketName := "my-test-bucket"
	objectKey := "test-object"
	lifetimeSecs := int64(3600) // 1 hour

	// Call the GetObject method
	request, err := presigner.GetObject(context.TODO(), bucketName, objectKey, lifetimeSecs)
	if err != nil {
		t.Fatalf("failed to get presigned request: %v", err)
	}

	// Check if the request is not nil
	if request == nil {
		t.Fatal("expected a non-nil presigned request")
	}

	// Print the presigned URL for verification
	assert.NotNil(t, request)
}
