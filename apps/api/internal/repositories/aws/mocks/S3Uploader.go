// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	mock "github.com/stretchr/testify/mock"
)

// NewS3Uploader creates a new instance of S3Uploader. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewS3Uploader(t interface {
	mock.TestingT
	Cleanup(func())
}) *S3Uploader {
	mock := &S3Uploader{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// S3Uploader is an autogenerated mock type for the S3Uploader type
type S3Uploader struct {
	mock.Mock
}

type S3Uploader_Expecter struct {
	mock *mock.Mock
}

func (_m *S3Uploader) EXPECT() *S3Uploader_Expecter {
	return &S3Uploader_Expecter{mock: &_m.Mock}
}

// Upload provides a mock function for the type S3Uploader
func (_mock *S3Uploader) Upload(ctx context.Context, input *s3.PutObjectInput, opts ...func(*manager.Uploader)) (*manager.UploadOutput, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, input, opts)
	} else {
		tmpRet = _mock.Called(ctx, input)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Upload")
	}

	var r0 *manager.UploadOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *s3.PutObjectInput, ...func(*manager.Uploader)) (*manager.UploadOutput, error)); ok {
		return returnFunc(ctx, input, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *s3.PutObjectInput, ...func(*manager.Uploader)) *manager.UploadOutput); ok {
		r0 = returnFunc(ctx, input, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*manager.UploadOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *s3.PutObjectInput, ...func(*manager.Uploader)) error); ok {
		r1 = returnFunc(ctx, input, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// S3Uploader_Upload_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Upload'
type S3Uploader_Upload_Call struct {
	*mock.Call
}

// Upload is a helper method to define mock.On call
//   - ctx
//   - input
//   - opts
func (_e *S3Uploader_Expecter) Upload(ctx interface{}, input interface{}, opts ...interface{}) *S3Uploader_Upload_Call {
	return &S3Uploader_Upload_Call{Call: _e.mock.On("Upload",
		append([]interface{}{ctx, input}, opts...)...)}
}

func (_c *S3Uploader_Upload_Call) Run(run func(ctx context.Context, input *s3.PutObjectInput, opts ...func(*manager.Uploader))) *S3Uploader_Upload_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]func(*manager.Uploader))
		run(args[0].(context.Context), args[1].(*s3.PutObjectInput), variadicArgs...)
	})
	return _c
}

func (_c *S3Uploader_Upload_Call) Return(uploadOutput *manager.UploadOutput, err error) *S3Uploader_Upload_Call {
	_c.Call.Return(uploadOutput, err)
	return _c
}

func (_c *S3Uploader_Upload_Call) RunAndReturn(run func(ctx context.Context, input *s3.PutObjectInput, opts ...func(*manager.Uploader)) (*manager.UploadOutput, error)) *S3Uploader_Upload_Call {
	_c.Call.Return(run)
	return _c
}
