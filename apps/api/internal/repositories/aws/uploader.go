package aws

import (
	"context"
	"io"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/rs/zerolog/log"
)

//go:generate mockery
type S3Uploader interface {
	Upload(
		ctx context.Context,
		input *s3.PutObjectInput,
		opts ...func(*manager.Uploader),
	) (*manager.UploadOutput, error)
}

//go:generate mockery
type S3Client interface {
	HeadObject(
		ctx context.Context,
		params *s3.HeadObjectInput,
		optFns ...func(*s3.Options),
	) (*s3.HeadObjectOutput, error)
}

type s3Actions struct {
	s3Client   S3Client
	s3Uploader S3Uploader
}

func NewUploader(s3Client S3Client, s3Uploader S3Uploader) s3Actions {
	return s3Actions{s3Client: s3Client, s3Uploader: s3Uploader}
}

func (actor s3Actions) UploadS3Object(
	ctx context.Context,
	bucketName string,
	objectKey string,
	file io.Reader,
) (*manager.UploadOutput, error) {
	bucketInfo := s3.HeadObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(objectKey),
	}

	input := s3.PutObjectInput{
		Bucket: bucketInfo.Bucket,
		Key:    bucketInfo.Key,
		Body:   file,
	}

	output, err := actor.s3Uploader.Upload(
		ctx,
		&input,
	)

	if err != nil {
		log.Error().Err(err).Msgf(
			"Couldn't upload object to %v:%v.",
			bucketName, objectKey,
		)
		return nil, err
	}

	err = s3.
		NewObjectExistsWaiter(actor.s3Client).
		Wait(
			ctx,
			&bucketInfo,
			time.Minute,
		)

	if err != nil {
		log.Error().Err(err).Msgf(
			"Failed attempt to wait for object %s to exist.\n",
			objectKey,
		)
		return nil, err
	}

	return output, nil
}
