package postgres

import (
	"api/domain"
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"
)

type TreatmentRepository struct {
	db *pgxpool.Pool
}

func NewTreatmentRepository(
	db *pgxpool.Pool,
) *TreatmentRepository {
	return &TreatmentRepository{db}
}

func (repo *TreatmentRepository) GetAllTreatment(
	ctx context.Context,
) ([]*domain.Treatment, error) {
	query := `
    SELECT
        *
    FROM treatments
    `

	rows, err := repo.db.Query(ctx, query)
	if err != nil {
		log.Error().Err(err).Msg("Query error")
		return nil, err
	}
	defer rows.Close()

	treatments, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByName[domain.Treatment])
	if err != nil {
		log.Error().Err(err).Msg("Error collecting rows")
		return nil, err
	}

	return treatments, nil
}
