package postgres_test

import (
	"context"
	"strings"
	"testing"

	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"
)

func TestSkinProblemIndicationRepository(t *testing.T) {
	dbPool := utils.GetTestDBPool(t)
	t.Cleanup(func() {
		dbPool.Close()
	})

	ctx := context.Background()
	repo := postgres.NewSkinProblemIndicationRepository(dbPool)

	cleanupByIDQuery := `
	delete
	from skin_problem_indications
	where id = any (@id)
	`

	// Copy the value, not the pointer.
	var (
		sharedRequest = domain.SkinProblemIndicationRequest{
			Name: "Test create skin problem indication",
		}

		sharedFilterData = domain.SkinProblemIndicationFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
		}
	)

	t.Run("Success CRUD", func(t *testing.T) {
		request := sharedRequest

		// Create new data for test.
		data, err := repo.Create(ctx, &request)
		require.NoError(t, err, "Create data should not have an error.")
		require.NotEmpty(t, data.ID, "ID should not be empty")

		t.Cleanup(func() {
			args := pgx.NamedArgs{"id": []string{data.ID}}

			err := utils.CleanupTestDummyData(
				ctx,
				dbPool,
				cleanupByIDQuery,
				args,
			)

			if err != nil {
				t.Fatalf("Error cleanup: %v", err)
			}
		})

		t.Run("Get data by id", func(t *testing.T) {
			result, err := repo.GetByID(ctx, &data.ID)
			assert.NoError(t, err, "Get data by id should not have an error.")
			assert.NotNil(t, result, "Get data by id result should not be nil.")
		})

		t.Run("Get many data", func(t *testing.T) {
			filterData := sharedFilterData

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(
				t,
				totalData,
				1,
				"Get many total data should have been equal or more than one",
			)
		})

		t.Run("Get many data filter name", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.Name = "test create skin problem indication"

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.Equal(t, totalData, 1)
		})

		t.Run("Update data by id", func(t *testing.T) {
			updateRequest := sharedRequest
			updateRequest.Name = "Update test skin problem indication data"

			updatedData, err := repo.UpdateByID(ctx, &data.ID, &updateRequest)
			assert.NoError(t, err, "Update data by id should not have an error.")

			// Check the name regardless of capitalization.
			insensitiveUpdatedName := strings.EqualFold(updateRequest.Name, updatedData.Name)
			assert.True(t, insensitiveUpdatedName)
		})

		t.Run("Delete data by id", func(t *testing.T) {
			result, err := repo.DeleteByID(ctx, &data.ID)
			assert.NoError(t, err, "Delete data by id should not have an error.")
			assert.Equal(t, result.ID, data.ID)
		})
	})

	t.Run("Duplicate name", func(t *testing.T) {
		var (
			ids           []string
			firstRequest  = sharedRequest
			secondRequest = sharedRequest
		)

		secondRequest.Name = "Test Duplicate"

		// Create first firstData.
		firstData, err := repo.Create(ctx, &firstRequest)
		require.NoError(t, err, "Create data should not have an error.")
		require.NotEmpty(t, firstData.ID, "ID should not be empty")

		ids = append(ids, firstData.ID)

		// Create second data.
		secondData, err := repo.Create(ctx, &secondRequest)
		require.NoError(t, err, "Create data should not have an error.")
		require.NotEmpty(t, firstData.ID, "ID should not be empty")

		ids = append(ids, secondData.ID)

		t.Cleanup(func() {
			args := pgx.NamedArgs{"id": ids}

			err := utils.CleanupTestDummyData(
				ctx,
				dbPool,
				cleanupByIDQuery,
				args,
			)

			if err != nil {
				t.Fatalf("Error cleanup: %v", err)
			}
		})

		t.Run("Create duplicate data", func(t *testing.T) {
			duplicateData, err := repo.Create(ctx, &firstRequest)
			assert.Error(t, err)
			assert.Nil(t, duplicateData)
		})

		t.Run("Update data by id", func(t *testing.T) {
			updateRequest := firstRequest
			updateRequest.Name = secondRequest.Name

			updatedData, err := repo.UpdateByID(ctx, &firstData.ID, &updateRequest)
			assert.Error(t, err)
			assert.Nil(t, updatedData)
		})
	})

	t.Run("Failed CRUD", func(t *testing.T) {
		t.Parallel()

		t.Run("Not found id when update data", func(t *testing.T) {
			t.Parallel()

			newRequest := sharedRequest
			newRequest.Name = "Failed update"

			data, err := repo.UpdateByID(ctx, &utils.DummyID, &newRequest)
			assert.NoError(t, err)
			assert.Nil(t, data)
		})

		t.Run("Invalid id when update data", func(t *testing.T) {
			t.Parallel()

			request := sharedRequest
			updatedData := request

			data, err := repo.UpdateByID(ctx, &utils.DummyID, &updatedData)
			assert.NoError(t, err)
			assert.Nil(t, data)
		})
	})
}
