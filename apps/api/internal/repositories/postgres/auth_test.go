package postgres_test

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"golang.org/x/crypto/bcrypt"

	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"
)

func TestAuthRepository(test *testing.T) {
	dbPool := utils.GetTestDBPool(test)
	test.Cleanup(func() {
		dbPool.Close()
	})

	ctx := context.Background()
	repo := postgres.NewAuthRepository(dbPool)

	cleanupByIDQuery := `
	delete
	from users
	where id = @id
	`

	var (
		password = "test-password"

		generatedPassword, err = bcrypt.GenerateFromPassword(
			[]byte(password),
			bcrypt.DefaultCost,
		)

		encryptPassword = string(generatedPassword[:])
	)

	require.NoError(test, err, "Generate password should not have an error.")

	// Copy the value, not the pointer.
	var (
		sharedData = domain.User{
			Name:        "Test user auth",
			Email:       "<EMAIL>",
			Role:        domain.Admin,
			PhoneNumber: "08123456789",
			Password:    &encryptPassword,
		}

		sharedRequest = domain.LoginRequest{
			Email:    sharedData.Email,
			Password: password,
		}
	)

	test.Run("Success Auth", func(t *testing.T) {
		data := sharedData
		request := sharedRequest

		// Create new user for test.
		createUserQuery := `
		insert into users
		(
			name,
			email,
			role,
			phone_number,
			password
		)
		values (
			@name,
			@email,
			@role,
			@phoneNumber,
			@password
		)
		returning id
		`

		createUserArgs := pgx.NamedArgs{
			"name":        data.Name,
			"email":       data.Email,
			"role":        data.Role,
			"phoneNumber": data.PhoneNumber,
			"password":    data.Password,
		}

		err := dbPool.QueryRow(ctx, createUserQuery, createUserArgs).Scan(&data.ID)

		require.NoError(t, err, "Should have no error when create test user.")

		t.Cleanup(func() {
			args := pgx.NamedArgs{"id": data.ID}
			err := utils.CleanupTestDummyData(ctx, dbPool, cleanupByIDQuery, args)

			if err != nil {
				t.Fatalf("Error cleanup: %v", err)
			}
		})

		t.Run("Verify email", func(t *testing.T) {
			result, err := repo.VerifyEmail(ctx, &request)
			assert.NoError(t, err)
			assert.NotNil(t, result)
		})
	})

	test.Run("Failed auth", func(t *testing.T) {
		t.Parallel()

		t.Run("Email not found", func(t *testing.T) {
			t.Parallel()

			request := sharedRequest

			result, err := repo.VerifyEmail(ctx, &request)
			assert.NoError(t, err)
			assert.Nil(t, result)
		})
	})
}
