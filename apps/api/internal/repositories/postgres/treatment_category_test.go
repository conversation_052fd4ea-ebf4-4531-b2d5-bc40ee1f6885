package postgres_test

import (
	"context"
	"strings"
	"testing"

	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"
)

func TestTreatmentCategoryRepository(test *testing.T) {
	dbPool := utils.GetTestDBPool(test)
	test.Cleanup(func() {
		dbPool.Close()
	})

	ctx := context.Background()
	repo := postgres.NewTreatmentCategoryRepository(dbPool)

	cleanupByIDQuery := `
	delete
	from treatment_categories
	where id = @id
	`

	// Copy the value, not the pointer.
	var (
		sharedData = domain.TreatmentCategory{
			Name: "Test create treatment category",
		}

		sharedFilterData = domain.TreatmentCategoryFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
		}
	)

	test.Run("Success CRUD", func(t *testing.T) {
		data := sharedData

		t.Cleanup(func() {
			args := pgx.NamedArgs{"id": data.ID}

			err := utils.CleanupTestDummyData(
				ctx,
				dbPool,
				cleanupByIDQuery,
				args,
			)

			if err != nil {
				t.Fatalf("Error cleanup: %v", err)
			}
		})

		// Create new data for test.
		err := repo.Create(ctx, &data)
		require.NoError(t, err, "Create data should not have an error.")
		require.NotEmpty(t, data.ID, "ID should not be empty")

		t.Run("Create duplicate data", func(t *testing.T) {
			err := repo.Create(ctx, &data)
			assert.NoError(t, err, "Create data should not have an error.")
			assert.NotEmpty(t, data.ID, "ID should not be empty")
		})

		t.Run("Get data by id", func(t *testing.T) {
			result, err := repo.GetByID(ctx, &data.ID)
			assert.NoError(t, err, "Get data by id should not have an error.")
			assert.NotNil(t, result, "Get data by id result should not be nil.")
		})

		t.Run("Get many data", func(t *testing.T) {
			filterData := sharedFilterData

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(
				t,
				totalData,
				1,
				"Get many total data should have been equal or more than one",
			)
		})

		t.Run("Update data by id", func(t *testing.T) {
			updatedData := data
			updatedData.Name = "Update test survey data"

			err := repo.UpdateByID(ctx, &data, &updatedData)
			assert.NoError(t, err, "Update data by id should not have an error.")

			// Check the name regardless of capitalization.
			insensitiveUpdatedName := strings.EqualFold(data.Name, updatedData.Name)
			assert.True(t, insensitiveUpdatedName)
		})

		t.Run("Delete data by id", func(t *testing.T) {
			result, err := repo.DeleteByID(ctx, &data.ID)
			assert.NoError(t, err, "Delete data by id should not have an error.")
			assert.Equal(t, result.ID, data.ID)
		})
	})

	test.Run("Failed CRUD", func(t *testing.T) {
		t.Parallel()

		t.Run("Not found id when update data", func(t *testing.T) {
			t.Parallel()

			data := sharedData
			data.ID = utils.DummyID

			updatedData := sharedData
			updatedData.ID = utils.DummyID

			err := repo.UpdateByID(ctx, &data, &updatedData)
			assert.ErrorIs(
				t,
				err,
				pgx.ErrNoRows,
				"Update data by id should have an error.",
			)
		})

		t.Run("Invalid id when update data", func(t *testing.T) {
			t.Parallel()

			data := sharedData
			updatedData := sharedData

			err := repo.UpdateByID(ctx, &data, &updatedData)
			assert.Error(t, err, "Update data by id should have an error.")
		})
	})
}
