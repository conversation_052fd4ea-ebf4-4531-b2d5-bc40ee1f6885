package postgres_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"
)

func TestUserRepository(test *testing.T) {
	dbPool := utils.GetTestDBPool(test)
	test.Cleanup(func() {
		dbPool.Close()
	})

	repo := postgres.NewUserRepository(dbPool)
	ctx := context.Background()

	createdID := ""

	test.Run("TestCreateUser", func(t *testing.T) {
		password := "test-password"
		email := fmt.Sprintf("<EMAIL>", time.Now().UnixNano())
		user := &domain.User{
			Name:        "Test User",
			Email:       email,
			Role:        domain.Admin,
			PhoneNumber: "08123456789",
			Password:    &password,
		}

		user, err := repo.Create(ctx, user)
		require.NoError(t, err, "Failed to create user")
		require.NotNil(t, user, "User should not be nil")

		assert.Equal(t, "Test User", user.Name, "Name should match")
		assert.Equal(t, email, user.Email, "Email should match")
		assert.NotEmpty(t, user.ID, "ID should not be empty")
		createdID = user.ID
	})

	test.Run("TestGetUserByID", func(t *testing.T) {
		user, err := repo.GetByID(ctx, &createdID)
		require.NoError(t, err, "Failed to get user by ID")
		require.NotNil(t, user, "User should not be nil")
		assert.Equal(t, createdID, user.ID, "ID should match")
	})

	test.Run("TestUpdateUserByID", func(t *testing.T) {
		user, err := repo.GetByID(ctx, &createdID)
		require.NoError(t, err, "Failed to get user by ID")
		require.NotNil(t, user, "User should not be nil")

		user.Name = "Updated Name"
		updatedUser, err := repo.UpdateByID(ctx, user, user)
		require.NoError(t, err, "Failed to update user")
		require.NotNil(t, updatedUser, "Updated user should not be nil")
		assert.Equal(t, "Updated Name", updatedUser.Name, "Name should be updated")
	})

	test.Run("TestGetManyUsers", func(t *testing.T) {
		filter := domain.UserFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
		}
		users, count, err := repo.GetMany(ctx, &filter)
		require.NoError(t, err, "Failed to get users")
		require.NotEmpty(t, users, "Users should not be empty")
		assert.Greater(t, count, 0, "Count should be greater than 0")
		assert.Greater(t, len(users), 0, "Users should be greater than 0")
		assert.LessOrEqual(t, len(users), filter.PageSize, "Users should not exceed page size")
	})

	test.Run("TestGetManyUsers filter", func(t *testing.T) {
		// Create a new user for password update test
		password := "test-password"
		email := fmt.Sprintf("<EMAIL>", time.Now().UnixNano())
		user := &domain.User{
			Name:        "Password Test User Get Many",
			Email:       email,
			Role:        domain.Branch,
			PhoneNumber: "08123456799",
			Password:    &password,
		}

		cleanupByIDQuery := `
		DELETE
		FROM users
		WHERE id = @id
		`

		user, err := repo.Create(ctx, user)
		require.NoError(t, err, "Failed to create user for password test")

		t.Cleanup(func() {
			args := pgx.NamedArgs{"id": user.ID}

			err := utils.CleanupTestDummyData(
				ctx,
				dbPool,
				cleanupByIDQuery,
				args,
			)

			require.NoErrorf(test, err, "Error cleanup: %v", err)
		})

		t.Run("Filter name", func(t *testing.T) {
			filter := domain.UserFilter{
				Pagination: domain.Pagination{
					Page:     1,
					PageSize: 10,
				},
				Name: &user.Name,
			}
			users, count, err := repo.GetMany(ctx, &filter)
			require.NoError(t, err)
			require.NotEmpty(t, users)
			assert.Equal(t, count, 1)
		})

		t.Run("Filter roles", func(t *testing.T) {
			filter := domain.UserFilter{
				Pagination: domain.Pagination{
					Page:     1,
					PageSize: 10,
				},
				Roles: []string{"branch"},
			}
			users, count, err := repo.GetMany(ctx, &filter)
			require.NoError(t, err)
			require.NotEmpty(t, users)
			assert.GreaterOrEqual(t, count, 1)
		})
	})

	test.Run("TestDeleteUserByID", func(t *testing.T) {
		deletedUser, err := repo.DeleteByID(ctx, &createdID)
		require.NoError(t, err, "Failed to delete user")
		require.NotNil(t, deletedUser, "Deleted user should not be nil")
		assert.Equal(t, createdID, deletedUser.ID, "ID should match")

		// Verify user is deleted
		_, err = repo.GetByID(ctx, &createdID)
		assert.Error(t, err, "GetByID should return error after deletion")
		assert.Contains(t, err.Error(), "not found", "Error should indicate user not found")
	})

	test.Run("TestUpdatePassword", func(t *testing.T) {
		// Create a new user for password update test
		password := "test-password"
		email := fmt.Sprintf("<EMAIL>", time.Now().UnixNano())
		user := &domain.User{
			Name:        "Password Test User",
			Email:       email,
			Role:        domain.Admin,
			PhoneNumber: "08123456789",
			Password:    &password,
		}

		user, err := repo.Create(ctx, user)
		require.NoError(t, err, "Failed to create user for password test")

		// Update password
		newPassword := "new-password"
		err = repo.UpdatePassword(ctx, &user.ID, &newPassword)
		require.NoError(t, err, "Failed to update password")

		// Verify password update
		updatedUser, err := repo.GetByID(ctx, &user.ID)
		require.NoError(t, err, "Failed to get updated user")
		require.NotNil(t, updatedUser, "Updated user should not be nil")
		assert.NotEqual(t, *user.Password, *updatedUser.Password, "Password should be updated")

		// Cleanup
		_, err = repo.DeleteByID(ctx, &user.ID)
		require.NoError(t, err, "Failed to cleanup test user")
	})

	test.Run("Test error when create user duplicate email", func(t *testing.T) {
		password := "test-password"
		email := fmt.Sprintf("<EMAIL>", time.Now().UnixNano())
		user := &domain.User{
			Name:        "Test User",
			Email:       email,
			Role:        "Anu",
			PhoneNumber: "08123456789",
			Password:    &password,
		}

		user, err := repo.Create(ctx, user)
		require.NoError(t, err, "Failed to create user")
		require.NotNil(t, user, "User should not be nil")

		t.Cleanup(func() {
			err := utils.CleanupTestDummyData(
				ctx,
				dbPool,
				`delete from users where id = @id`,
				pgx.NamedArgs{"id": user.ID},
			)
			require.NoErrorf(test, err, "Error cleanup user: %v", err)
		})

		newUser, err := repo.Create(ctx, user)
		require.ErrorContains(t, err, "already exist")
		require.Nil(t, newUser, "User should be nil")
	})

}
