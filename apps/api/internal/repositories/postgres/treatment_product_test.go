package postgres_test

import (
	"context"
	"testing"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"
)

func TestTreatmentProductRepository(test *testing.T) {
	dbPool := utils.GetTestDBPool(test)
	test.Cleanup(func() {
		dbPool.Close()
	})

	ctx := context.Background()
	repo := postgres.NewTreatmentProductRepository(dbPool)
	problemIndicationRepo := postgres.NewSkinProblemIndicationRepository(dbPool)
	categoryRepo := postgres.NewTreatmentCategoryRepository(dbPool)
	intervalRepo := postgres.NewTreatmentIntervalRepository(dbPool)
	concernRepo := postgres.NewSkinProblemRepository(dbPool)
	surveyRepo := postgres.NewSurveyRepository(dbPool)

	cleanupSkinProblemIndicationByIDQuery := `
	delete
	from skin_problem_indications
	where id = @id
	`

	cleanupCategoryByIDQuery := `
	delete
	from treatment_categories
	where id = @id
	`

	cleanupIntervalByIDQuery := `
	delete
	from treatment_intervals
	where id = @id
	`

	cleanupConcernByIDQuery := `
	delete
	from skin_problems
	where id = any (@ids)
	`

	cleanupSurveyByIDQuery := `
	delete
	from survey_questions
	where id = any (@ids)
	`

	cleanupByIDQuery := `
	delete
	from treatment_products
	where id = @id
	`

	problemIndicationRequest := domain.SkinProblemIndicationRequest{
		Name: "test treatment product indication",
	}

	problemIndicationData, err := problemIndicationRepo.Create(
		ctx,
		&problemIndicationRequest,
	)

	require.NoError(
		test,
		err,
		"Create skin problem indication should not have an error",
	)

	test.Cleanup(func() {
		indicationArgs := pgx.NamedArgs{"id": problemIndicationData.ID}

		err := utils.CleanupTestDummyData(
			ctx,
			dbPool,
			cleanupSkinProblemIndicationByIDQuery,
			indicationArgs,
		)

		require.NoErrorf(
			test,
			err,
			"Error cleanup skin problem indication: %v",
			err,
		)
	})

	// Copy the value, not the pointer.
	var (
		categoryData = domain.TreatmentCategory{Name: "Test treatment product category"}
		intervalData = domain.TreatmentInterval{Days: 69420}

		problemIndicationIDs = []string{problemIndicationData.ID}

		concernData = []domain.SkinProblemRequest{
			{Name: "test acne", SkinProblemIndicationIDs: problemIndicationIDs},
			{Name: "test pore", SkinProblemIndicationIDs: problemIndicationIDs},
			{Name: "test wrinkle", SkinProblemIndicationIDs: problemIndicationIDs},
		}

		surveyData = []domain.Survey{
			{
				Description: "Test survey one",
				Question:    "Apakah hidup anda tenang?",
				Answers: []domain.SurveyAnswer{
					{Title: "Ya"},
					{Title: "Tidak"},
				},
			},
			{
				Description: "Test survey one",
				Question:    "Apakah anda kekurangan masalah?",
				Answers: []domain.SurveyAnswer{
					{Title: "Ya"},
					{Title: "Tidak"},
				},
			},
		}
	)

	err = categoryRepo.Create(ctx, &categoryData)
	require.NoError(test, err, "Create category should not have an error")

	err = intervalRepo.Create(ctx, &intervalData)
	require.NoError(test, err, "Create interval should not have an error")

	var concernIDs []string
	for _, data := range concernData {
		newData, err := concernRepo.Create(ctx, &data)
		require.NoError(test, err, "Create concern should not have an error")
		concernIDs = append(concernIDs, newData.ID)
	}

	var surveyIDs []string
	for _, data := range surveyData {
		newData, err := surveyRepo.Create(ctx, &data)
		require.NoError(test, err, "Create survey should not have an error")
		surveyIDs = append(surveyIDs, newData.ID)
	}

	test.Cleanup(func() {
		categoryArgs := pgx.NamedArgs{"id": categoryData.ID}
		intervalArgs := pgx.NamedArgs{"id": intervalData.ID}
		concernArgs := pgx.NamedArgs{"ids": concernIDs}
		surveyArgs := pgx.NamedArgs{"ids": surveyIDs}

		err := utils.CleanupTestDummyData(
			ctx,
			dbPool,
			cleanupCategoryByIDQuery,
			categoryArgs,
		)
		require.NoErrorf(test, err, "Error cleanup category: %v", err)

		err = utils.CleanupTestDummyData(
			ctx,
			dbPool,
			cleanupIntervalByIDQuery,
			intervalArgs,
		)
		require.NoErrorf(test, err, "Error cleanup interval: %v", err)

		err = utils.CleanupTestDummyData(
			ctx,
			dbPool,
			cleanupConcernByIDQuery,
			concernArgs,
		)
		require.NoErrorf(test, err, "Error cleanup concerns: %v", err)

		err = utils.CleanupTestDummyData(
			ctx,
			dbPool,
			cleanupSurveyByIDQuery,
			surveyArgs,
		)
		require.NoErrorf(test, err, "Error cleanup survey: %v", err)
	})

	testItemCode := "UWU69420"
	testNotes := "Test notes from doctor"
	mediaUrl := "https://example.com/s3-object"
	thumbnailUrl := "https://example.com/s3-object"
	durationTopRecommendation := time.Now().UnixNano()

	// Copy the value, not the pointer.
	sharedRequest := domain.TreatmentProductRequest{
		ItemCode:                  testItemCode,
		Name:                      "Test Treatment Product",
		Type:                      domain.TreatmentType,
		Description:               "Dummy data test",
		Price:                     69420,
		CategoryIDs:               []string{categoryData.ID},
		IntervalID:                &intervalData.ID,
		ConcernIDs:                concernIDs,
		Notes:                     &testNotes,
		MediaUrl:                  &mediaUrl,
		ThumbnailUrl:              &thumbnailUrl,
		Quantity:                  69,
		IsTopRecommendation:       true,
		DurationTopRecommendation: &durationTopRecommendation,
		SurveyQuestions: []domain.TreatmentProductSurveyQuestionInput{
			{
				SurveyQuestionID: surveyIDs[0],
				SelectedAnswer:   1,
			},
			{
				SurveyQuestionID: surveyIDs[1],
				SelectedAnswer:   0,
			},
		},
	}

	var surveyQuestions domain.TreatmentProductSurveyQuestionJSON
	for i := 0; i < len(sharedRequest.SurveyQuestions); i++ {
		surveyQuestions = append(
			surveyQuestions,
			domain.TreatmentProductSurveyQuestion{
				ID:             surveyIDs[i],
				Question:       surveyData[i].Question,
				Answers:        surveyData[i].Answers,
				SelectedAnswer: sharedRequest.SurveyQuestions[i].SelectedAnswer,
				QuestionOrder:  i,
			},
		)
	}

	var (
		sharedData = domain.TreatmentProduct{
			ItemCode:     sharedRequest.ItemCode,
			Name:         sharedRequest.Name,
			Type:         sharedRequest.Type,
			Description:  sharedRequest.Description,
			Price:        sharedRequest.Price,
			IntervalID:   &intervalData.ID,
			Notes:        sharedRequest.Notes,
			MediaUrl:     sharedRequest.MediaUrl,
			ThumbnailUrl: sharedRequest.ThumbnailUrl,
		}

		sharedFilterData = domain.TreatmentProductFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
			SortColumn: "created_at",
			SortOrder:  "asc",
		}
	)

	test.Run("Get supplementary data", func(t *testing.T) {
		request := sharedRequest
		data, err := repo.GetSupplementaryData(ctx, &request)
		assert.NoError(t, err)
		assert.NotNil(t, data)
	})

	test.Run("Success CRUD", func(t *testing.T) {
		var (
			request = sharedRequest
			data    = sharedData
		)

		t.Cleanup(func() {
			treatmentProductArgs := pgx.NamedArgs{"id": data.ID}

			err = utils.CleanupTestDummyData(
				ctx,
				dbPool,
				cleanupByIDQuery,
				treatmentProductArgs,
			)

			require.NoErrorf(test, err, "Error cleanup treatment product: %v", err)
		})

		// Create new data for test.
		newData, err := repo.Create(
			ctx,
			&data,
			concernIDs,
			request.CategoryIDs,
			request.SurveyQuestions,
		)

		data = *newData

		require.NoError(t, err, "Create data should not have an error.")
		require.NotEmpty(t, data.ID, "ID should not be empty")

		t.Run("Get data by id", func(t *testing.T) {
			result, err := repo.GetByID(ctx, &data.ID)
			assert.NoError(t, err, "Get data by id should not have an error.")
			assert.NotNil(t, result, "Get data by id result should not be nil.")
		})

		t.Run("Get many data", func(t *testing.T) {
			filterData := sharedFilterData

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(
				t,
				totalData,
				1,
				"Get many total data should have been equal or more than one",
			)
		})

		t.Run("Get many data search by name", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.Name = "test"

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(
				t,
				totalData,
				1,
				"Get many total data should have been equal or more than one",
			)
		})

		t.Run("Get many data filter by type", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.Types = []string{string(data.Type)}

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(
				t,
				totalData,
				1,
				"Get many total data should have been equal or more than one",
			)
		})

		t.Run("Get many data filter by range price", func(t *testing.T) {
			var (
				minPrice = int64(0)
				maxPrice = sharedData.Price
			)

			filterData := sharedFilterData
			filterData.MinPrice = &minPrice
			filterData.MaxPrice = &maxPrice

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(
				t,
				totalData,
				1,
				"Get many total data should have been equal or more than one",
			)
		})

		t.Run("Get many data filter by multiple category ids", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.CategoryIDs = []string{categoryData.ID}

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.Equal(t, totalData, 1)
		})

		t.Run("Update data by id", func(t *testing.T) {
			var (
				existingData = domain.TreatmentProductResponse{
					ID: data.ID,
					Timestamp: domain.Timestamp{
						UpdatedAt: data.UpdatedAt,
					},
				}

				updatedData = data
			)

			updatedData.Description = "Updated dummy data test"

			result, err := repo.UpdateByID(
				ctx,
				&existingData,
				&updatedData,
				concernIDs,
				request.CategoryIDs,
				request.SurveyQuestions,
			)

			assert.NoError(t, err, "Update data by id should not have an error.")
			assert.Equal(t, updatedData.Description, result.Description)
			assert.NotEqual(t, updatedData.UpdatedAt, result.UpdatedAt)
		})

		t.Run("Delete data by id", func(t *testing.T) {
			result, err := repo.DeleteByID(ctx, &data.ID)
			assert.NoError(t, err, "Delete data by id should not have an error.")
			assert.Equal(t, result.ID, data.ID)
		})
	})

	test.Run("Success create and update with optional field", func(t *testing.T) {
		data := sharedData

		// Create new data for test.
		newData, err := repo.Create(
			ctx,
			&data,
			nil,
			nil,
			nil,
		)

		require.NoError(t, err, "Create data should not have an error.")
		require.NotEmpty(t, newData.ID, "ID should not be empty")

		t.Cleanup(func() {
			treatmentProductArgs := pgx.NamedArgs{"id": newData.ID}

			err = utils.CleanupTestDummyData(
				ctx,
				dbPool,
				cleanupByIDQuery,
				treatmentProductArgs,
			)

			require.NoErrorf(test, err, "Error cleanup treatment product: %v", err)
		})

		t.Run("Get data by id", func(t *testing.T) {
			result, err := repo.GetByID(ctx, &newData.ID)
			assert.NoError(t, err, "Get data by id should not have an error.")
			assert.NotNil(t, result, "Get data by id result should not be nil.")
		})

		t.Run("Update data by id", func(t *testing.T) {
			var (
				existingData = domain.TreatmentProductResponse{
					ID: newData.ID,
					Timestamp: domain.Timestamp{
						UpdatedAt: data.UpdatedAt,
					},
				}

				updatedData = data
			)

			updatedData.Description = "Updated dummy data test"

			result, err := repo.UpdateByID(
				ctx,
				&existingData,
				&updatedData,
				nil,
				nil,
				nil,
			)

			assert.NoError(t, err, "Update data by id should not have an error.")
			assert.Equal(t, updatedData.Description, result.Description)
			assert.NotEqual(t, updatedData.UpdatedAt, result.UpdatedAt)
		})
	})

	test.Run("Failed CRUD", func(t *testing.T) {
		t.Parallel()

		t.Run("Category id not found", func(t *testing.T) {
			t.Parallel()

			request := sharedRequest
			request.CategoryIDs = []string{utils.DummyID}

			data, err := repo.GetSupplementaryData(ctx, &request)
			assert.ErrorContains(t, err, "Category ID")
			assert.Nil(t, data)
		})

		t.Run("Interval id not found", func(t *testing.T) {
			t.Parallel()

			request := sharedRequest
			request.IntervalID = &utils.DummyID

			data, err := repo.GetSupplementaryData(ctx, &request)
			assert.ErrorContains(t, err, "Interval ID")
			assert.Nil(t, data)
		})

		t.Run("Concern ids not found", func(t *testing.T) {
			t.Parallel()

			request := sharedRequest
			request.ConcernIDs = []string{utils.DummyID, utils.DummyID}

			data, err := repo.GetSupplementaryData(ctx, &request)
			assert.ErrorContains(t, err, "Concern IDs")
			assert.Nil(t, data)
		})

		t.Run("Survey ids not found", func(t *testing.T) {
			t.Parallel()

			request := sharedRequest
			request.SurveyQuestions = []domain.TreatmentProductSurveyQuestionInput{
				{SurveyQuestionID: utils.DummyID},
				{SurveyQuestionID: utils.DummyID},
			}

			data, err := repo.GetSupplementaryData(ctx, &request)
			assert.ErrorContains(t, err, "Survey IDs")
			assert.Nil(t, data)
		})

		t.Run("Survey selected answer index out of bounds", func(t *testing.T) {
			t.Parallel()

			request := sharedRequest
			request.SurveyQuestions = []domain.TreatmentProductSurveyQuestionInput{
				{
					SurveyQuestionID: surveyIDs[0],
					SelectedAnswer:   2,
				},
			}

			data, err := repo.GetSupplementaryData(ctx, &request)
			assert.ErrorContains(t, err, "Survey selected answer")
			assert.Nil(t, data)
		})

		t.Run("Create with invalid concern id", func(t *testing.T) {
			t.Parallel()

			var (
				data       = sharedData
				invalidIDs = []string{"1"}
			)

			_, err := repo.Create(
				ctx,
				&data,
				invalidIDs,
				nil,
				nil,
			)

			assert.Error(t, err)
		})

		t.Run("Get many data search by name not found", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.Name = "uwu69420"

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.Empty(t, results, "Get many data result should be empty.")
			assert.Equal(
				t,
				0,
				totalData,
				"Get many total data should have been equal to 0",
			)
		})

		t.Run("Get many data filter by type not found", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.Types = []string{"uwu"}

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.Empty(t, results, "Get many data result should be empty.")
			assert.Equal(
				t,
				0,
				totalData,
				"Get many total data should have been equal or more than one",
			)
		})
	})
}
