package postgres

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"

	"api/domain"
	"api/utils"
)

type userRepository struct {
	db *pgxpool.Pool
}

func NewUserRepository(db *pgxpool.Pool) *userRepository {
	return &userRepository{db}
}

func (repo *userRepository) Create(ctx context.Context, user *domain.User) (*domain.User, error) {
	query := `
	INSERT INTO users (
		name,
		email,
		phone_number,
		password,
		role,
		address,
		gender,
		birth_date,
		created_at,
		updated_at
	) VALUES (
		@name,
		@email,
		@phone_number,
		@password,
		@role,
		@address,
		@gender,
		@birth_date,
		EXTRACT(epoch FROM now()) * 1000,
		EXTRACT(epoch FROM now()) * 1000
	) RETURNING *`

	args := pgx.StrictNamedArgs{
		"name":         user.Name,
		"email":        user.Email,
		"phone_number": user.PhoneNumber,
		"password":     user.Password,
		"role":         user.Role,
		"address":      user.Address,
		"gender":       user.Gender,
		"birth_date":   user.BirthDate,
	}

	rows, err := repo.db.Query(ctx, query, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create user")
		return nil, err
	}
	defer rows.Close()

	result, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[domain.User])
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect user")
		customErr := utils.CustomPostgresErr(err)
		return nil, customErr
	}

	return result, nil
}

func (repo *userRepository) GetByID(ctx context.Context, id *string) (*domain.User, error) {
	query := `SELECT * FROM users WHERE id = @id`

	args := pgx.StrictNamedArgs{
		"id": id,
	}

	rows, err := repo.db.Query(ctx, query, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get user by ID")
		return nil, err
	}
	defer rows.Close()

	result, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[domain.User])
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("Data with id %s not found", *id)
		}
		log.Error().Err(err).Msg("Failed to collect user")
		return nil, err
	}

	return result, nil
}

func (repo *userRepository) UpdateByID(ctx context.Context, existingData *domain.User, newData *domain.User) (*domain.User, error) {
	query := `
	UPDATE users
	SET
		name = @name,
		email = @email,
		phone_number = @phone_number,
		password = @password,
		role = @role,
		address = @address,
		gender = @gender,
		birth_date = @birth_date,
		updated_at = EXTRACT(epoch FROM now()) * 1000
	WHERE id = @id
	RETURNING *`

	args := pgx.StrictNamedArgs{
		"name":         newData.Name,
		"email":        newData.Email,
		"phone_number": newData.PhoneNumber,
		"password":     newData.Password,
		"role":         newData.Role,
		"address":      newData.Address,
		"gender":       newData.Gender,
		"birth_date":   newData.BirthDate,
		"id":           newData.ID,
	}

	rows, err := repo.db.Query(ctx, query, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to update user")
		return nil, err
	}
	defer rows.Close()

	result, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[domain.User])
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("Data with id %s not found", newData.ID)
		}
		log.Error().Err(err).Msg("Failed to collect updated user")
		return nil, err
	}

	return result, nil
}

func (repo *userRepository) DeleteByID(ctx context.Context, id *string) (*domain.User, error) {
	query := `DELETE FROM users WHERE id = @id RETURNING *`

	args := pgx.StrictNamedArgs{
		"id": id,
	}

	rows, err := repo.db.Query(ctx, query, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to delete user")
		return nil, err
	}
	defer rows.Close()

	result, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[domain.User])
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("Data with id %s not found", *id)
		}
		log.Error().Err(err).Msg("Failed to collect deleted user")
		return nil, err
	}

	return result, nil
}

func (repo *userRepository) GetMany(
	ctx context.Context,
	filter *domain.UserFilter,
) ([]*domain.User, int, error) {
	var (
		query          = `SELECT * FROM users`
		countQuery     = `SELECT COUNT(*) FROM users`
		composedFilter []string
		args           = pgx.StrictNamedArgs{}
	)

	// Add name filter
	if filter.Name != nil && *filter.Name != "" {
		composedFilter = append(composedFilter, "name ILIKE @name")
		args["name"] = "%" + *filter.Name + "%"
	}

	if len(filter.Roles) > 0 {
		composedFilter = append(composedFilter, "role = any (@roles)")
		args["roles"] = filter.Roles
	}

	whereClause := utils.GetComposedWhereClause(composedFilter)
	if whereClause != nil {
		query += fmt.Sprintf("\n%s", *whereClause)
		countQuery += fmt.Sprintf("\n%s", *whereClause)
	}

	// Get total count
	var totalData int
	err := repo.db.QueryRow(
		ctx,
		countQuery,
		args,
	).Scan(&totalData)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get total count of users")
		return nil, 0, err
	}

	// Add pagination
	paginationClause := filter.Pagination.GetPaginationQuery()
	if paginationClause != nil {
		query += fmt.Sprintf("%s", *paginationClause)
	}

	// Get data
	rows, err := repo.db.Query(ctx, query, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get users")
		return nil, 0, err
	}
	defer rows.Close()

	results, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByName[domain.User])
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect users")
		return nil, 0, err
	}

	return results, totalData, nil
}

func (repo *userRepository) UpdatePassword(ctx context.Context, id *string, password *string) error {
	query := `
	UPDATE users
	SET
		password = @password,
		updated_at = EXTRACT(epoch FROM now()) * 1000
	WHERE id = @id
	`

	args := pgx.NamedArgs{
		"password": password,
		"id":       id,
	}

	result, err := repo.db.Exec(ctx, query, args)
	if err != nil {
		return err
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("user with id %s not found", *id)
	}

	return nil
}
