package postgres

import (
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"

	"api/domain"
)

type concernAnswerRepository struct {
	db *pgxpool.Pool
}

func NewConcernAnswerRepository(
	db *pgxpool.Pool,
) *concernAnswerRepository {
	return &concernAnswerRepository{db}
}

func (repo *concernAnswerRepository) GetConcernAnswerByKey(
	ctx context.Context,
	key string,
) (*domain.ConcernAnswer, error) {
	query := `
	SELECT
    *
	FROM concern_answers
	WHERE concern_key = $1
	`

	rows, err := repo.db.Query(
		ctx,
		query,
		&key,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			log.Error().Err(err).Msg("No rows found")
			return nil, nil
		}
		log.Error().Err(err).Msg("Query error")
		return nil, err
	}
	defer rows.Close()

	concernAnswer, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[domain.ConcernAnswer])
	if err != nil {
		log.Error().Err(err).Msg("Error collecting row")
		return nil, err
	}

	return concernAnswer, nil
}
