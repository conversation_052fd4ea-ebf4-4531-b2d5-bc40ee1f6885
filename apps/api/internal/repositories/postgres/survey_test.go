package postgres_test

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"
)

func TestSurveyRepository(t *testing.T) {
	dbPool := utils.GetTestDBPool(t)
	t.Cleanup(func() {
		dbPool.Close()
	})

	ctx := context.Background()
	repo := postgres.NewSurveyRepository(dbPool)

	cleanupByIDQuery := `
	delete
	from survey_questions
	where id = @id
	`

	// Copy the value, not the pointer.
	var (
		sharedData = domain.Survey{
			Description: "Test create survey data",
			Question:    "Apakah test nya berhasil?",
			Answers: []domain.SurveyAnswer{
				{
					Title: "Ya",
				},
				{
					Title: "Tidak",
				},
			},
			Type:     domain.SingleFull,
			IsStatic: false,
			Category: domain.Informational,
		}

		sharedDataNested = domain.SurveyRequestNested{
			Description: sharedData.Description,
			Question:    sharedData.Question,
			Answers:     sharedData.Answers,
			Type:        sharedData.Type,
			Category:    sharedData.Category,
			ChildQuestions: []domain.SurveyRequestChildQuestion{
				{
					ParentQuestionAnswer: 0,
					Description:          "Test child question",
					Question:             "Apakah test child question nya berhasil?",
					Answers:              sharedData.Answers,
					Type:                 domain.SingleFull,
					QuestionOrder:        0,
					Category:             domain.Informational,
				},
			},
		}

		sharedFilterData = domain.SurveyFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
		}

		sharedFilterDataNested = domain.SurveyFilterNested{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
		}
	)

	t.Run("Success CRUD", func(t *testing.T) {
		data := sharedData

		t.Cleanup(func() {
			args := pgx.NamedArgs{"id": data.ID}
			err := utils.CleanupTestDummyData(ctx, dbPool, cleanupByIDQuery, args)

			if err != nil {
				t.Fatalf("Error cleanup: %v", err)
			}
		})

		// Create new data for test.
		newData, err := repo.Create(ctx, &data)
		require.NoError(t, err, "Create data should not have an error.")
		require.NotEmpty(t, newData.ID, "ID should not be empty")

		data = *newData

		t.Run("Get data by id", func(t *testing.T) {
			result, err := repo.GetByID(ctx, &data.ID)
			assert.NoError(t, err, "Get data by id should not have an error.")
			assert.NotNil(t, result, "Get data by id result should not be nil.")
		})

		t.Run("Get many data", func(t *testing.T) {
			filterData := sharedFilterData

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(
				t,
				totalData,
				1,
				"Get many total data should have been equal or more than one",
			)
		})

		t.Run("Get many data filter question", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.Question = &sharedData.Question

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.Equal(t, totalData, 1)
		})

		t.Run("Get many data filter type", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.Type = []string{"single_full"}

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(t, totalData, 1)
		})

		t.Run("Get many data filter category", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.Category = []string{"informational"}

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(t, totalData, 1)
		})

		t.Run("Get many data filter sort group", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.SortColumn = "group"
			filterData.SortOrder = "asc"

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(t, totalData, 1)
			assert.Nil(t, results[0].ParentQuestionID)
		})

		t.Run("Get many data filter sort mobile", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.SortColumn = "mobile"
			filterData.SortOrder = "asc"

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(t, totalData, 1)
			assert.Equal(t, results[0].ID, "1f04ea29-b556-4793-80ca-f0314f49917d")
		})

		t.Run("Update data by id", func(t *testing.T) {
			newData := data
			newData.Description = "Update test survey data"

			updatedData, err := repo.UpdateByID(ctx, &data, &newData)
			assert.NoError(t, err, "Update data by id should not have an error.")
			assert.Equal(t, newData.Description, updatedData.Description)
		})

		t.Run("Delete data by id", func(t *testing.T) {
			result, err := repo.DeleteByID(ctx, &data.ID)
			assert.NoError(t, err, "Delete data by id should not have an error.")
			assert.Equal(t, result.ID, data.ID)
		})
	})

	t.Run("Success CRUD nested data", func(t *testing.T) {
		data := sharedDataNested

		// Create new data for test.
		createdData, err := repo.CreateNested(ctx, &data)
		require.NoError(t, err, "Create data should not have an error.")
		require.NotEmpty(t, createdData.ID, "ID should not be empty")

		t.Cleanup(func() {
			args := pgx.NamedArgs{"id": createdData.ID}
			err := utils.CleanupTestDummyData(ctx, dbPool, cleanupByIDQuery, args)

			if err != nil {
				t.Fatalf("Error cleanup %s: %v", t.Name(), err)
			}
		})

		t.Run("Get data by id", func(t *testing.T) {
			result, err := repo.GetByIDNested(ctx, &createdData.ID)
			assert.NoError(t, err, "Get data by id should not have an error.")
			assert.IsType(
				t,
				&domain.SurveyResponseNested{},
				result,
			)
		})

		t.Run("Get many data", func(t *testing.T) {
			filterData := sharedFilterDataNested

			results, totalData, err := repo.GetManyNested(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(
				t,
				totalData,
				1,
				"Get many total data should have been equal or more than one",
			)
		})

		t.Run("Get many data filter question", func(t *testing.T) {
			filterData := sharedFilterDataNested
			filterData.Question = &sharedData.Question

			results, totalData, err := repo.GetManyNested(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.Equal(t, totalData, 1)
		})

		t.Run("Get many data filter type", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.Type = []string{"single_full"}

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(t, totalData, 1)
		})

		t.Run("Get many data filter category", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.Category = []string{"informational"}

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(t, totalData, 1)
		})

		t.Run("Get many data filter sort mobile", func(t *testing.T) {
			filterData := sharedFilterData
			filterData.SortColumn = "mobile"
			filterData.SortOrder = "asc"

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(t, totalData, 1)
			assert.Equal(t, results[0].ID, "1f04ea29-b556-4793-80ca-f0314f49917d")
		})

		t.Run("Update data by id", func(t *testing.T) {
			newData := data
			newData.Description = "Update test survey data"

			updatedData, err := repo.UpdateByIDNested(ctx, &createdData.ID, &newData)
			assert.NoError(t, err, "Update data by id should not have an error.")
			assert.Equal(t, newData.Description, updatedData.Description)
		})
	})

	t.Run("Success add survey to treatment-product", func(t *testing.T) {
		var (
			prevTreatmentProductSurveyCount int
			newTreatmentProductSurveyCount  int

			treatmentProductSurveyCountQuery = `
			SELECT count(*) FROM treatment_products_surveys
			`
		)

		data := sharedData
		data.Category = domain.Contraindication

		err := dbPool.QueryRow(
			ctx,
			`SELECT count(*) FROM treatment_products_surveys`,
		).Scan(&prevTreatmentProductSurveyCount)

		require.NoError(t, err)

		// Create new data for test.
		createdData, err := repo.Create(ctx, &data)
		require.NoError(t, err, "Create data should not have an error.")
		require.NotEmpty(t, createdData.ID, "ID should not be empty")

		t.Cleanup(func() {
			args := pgx.NamedArgs{"id": createdData.ID}
			err := utils.CleanupTestDummyData(ctx, dbPool, cleanupByIDQuery, args)

			if err != nil {
				t.Fatalf("Error cleanup: %v", err)
			}
		})

		err = dbPool.QueryRow(
			ctx,
			treatmentProductSurveyCountQuery,
		).Scan(&newTreatmentProductSurveyCount)

		require.NoError(t, err)
		require.Greater(
			t,
			newTreatmentProductSurveyCount,
			prevTreatmentProductSurveyCount,
		)

		t.Run("Update with category contraindication", func(t *testing.T) {
			newData := data
			newData.Description = "Update test survey data"

			updatedData, err := repo.UpdateByID(ctx, createdData, &newData)
			assert.NoError(t, err, "Update data by id should not have an error.")
			assert.Equal(t, newData.Description, updatedData.Description)

			err = dbPool.QueryRow(
				ctx,
				treatmentProductSurveyCountQuery,
			).Scan(&newTreatmentProductSurveyCount)

			require.NoError(t, err)
			require.Greater(
				t,
				newTreatmentProductSurveyCount,
				prevTreatmentProductSurveyCount,
			)
		})

		t.Run("Update with category informational", func(t *testing.T) {
			newData := sharedData
			newData.Description = "Update test survey data"

			updatedData, err := repo.UpdateByID(ctx, createdData, &newData)
			assert.NoError(t, err, "Update data by id should not have an error.")
			assert.Equal(t, newData.Description, updatedData.Description)

			err = dbPool.QueryRow(
				ctx,
				treatmentProductSurveyCountQuery,
			).Scan(&newTreatmentProductSurveyCount)

			require.NoError(t, err)
			require.Equal(
				t,
				newTreatmentProductSurveyCount,
				prevTreatmentProductSurveyCount,
			)
		})
	})

	t.Run("Success add survey nested to treatment-product", func(t *testing.T) {
		var (
			prevTreatmentProductSurveyCount int
			newTreatmentProductSurveyCount  int

			treatmentProductSurveyCountQuery = `
			SELECT count(*) FROM treatment_products_surveys
			`
		)

		data := sharedDataNested
		data.Category = domain.Contraindication
		data.ChildQuestions[0].Category = domain.Contraindication

		err := dbPool.QueryRow(
			ctx,
			`SELECT count(*) FROM treatment_products_surveys`,
		).Scan(&prevTreatmentProductSurveyCount)

		require.NoError(t, err)

		// Create new data for test.
		createdData, err := repo.CreateNested(ctx, &data)
		require.NoError(t, err, "Create data should not have an error.")
		require.NotEmpty(t, createdData.ID, "ID should not be empty")

		t.Cleanup(func() {
			args := pgx.NamedArgs{"id": createdData.ID}
			err := utils.CleanupTestDummyData(ctx, dbPool, cleanupByIDQuery, args)

			if err != nil {
				t.Fatalf("Error cleanup: %v", err)
			}
		})

		err = dbPool.QueryRow(
			ctx,
			treatmentProductSurveyCountQuery,
		).Scan(&newTreatmentProductSurveyCount)

		require.NoError(t, err)
		require.Greater(
			t,
			newTreatmentProductSurveyCount,
			prevTreatmentProductSurveyCount,
		)

		t.Run("Update with category contraindication", func(t *testing.T) {
			newData := data
			newData.Description = "Update test survey data"

			updatedData, err := repo.UpdateByIDNested(ctx, &createdData.ID, &newData)
			assert.NoError(t, err, "Update data by id should not have an error.")
			assert.Equal(t, newData.Description, updatedData.Description)

			err = dbPool.QueryRow(
				ctx,
				treatmentProductSurveyCountQuery,
			).Scan(&newTreatmentProductSurveyCount)

			require.NoError(t, err)
			require.Greater(
				t,
				newTreatmentProductSurveyCount,
				prevTreatmentProductSurveyCount,
			)
		})

		t.Run("Update with category informational", func(t *testing.T) {
			newData := sharedDataNested
			newData.Description = "Update test survey data"

			updatedData, err := repo.UpdateByIDNested(ctx, &createdData.ID, &newData)
			assert.NoError(t, err, "Update data by id should not have an error.")
			assert.Equal(t, newData.Description, updatedData.Description)

			err = dbPool.QueryRow(
				ctx,
				treatmentProductSurveyCountQuery,
			).Scan(&newTreatmentProductSurveyCount)

			require.NoError(t, err)
			require.Equal(
				t,
				newTreatmentProductSurveyCount,
				prevTreatmentProductSurveyCount,
			)
		})
	})

	t.Run("Failed CRUD", func(t *testing.T) {
		t.Parallel()

		t.Run("Not found parent_question_id when create data", func(t *testing.T) {
			t.Parallel()

			data := sharedData
			data.ParentQuestionID = &utils.DummyID

			newData, err := repo.Create(ctx, &data)
			assert.Error(t, err, "Create data should have an error.")
			assert.Nil(t, newData, "New data should have been nil.")
		})

		t.Run("Invalid parent_question_id when create data", func(t *testing.T) {
			t.Parallel()

			data := sharedData
			id := "123"

			data.ParentQuestionID = &id

			newData, err := repo.Create(ctx, &data)
			assert.Error(t, err, "Create data should have an error.")
			assert.Nil(t, newData, "New data should have been nil.")
		})

		t.Run("Not found id when update data", func(t *testing.T) {
			t.Parallel()

			data := sharedData
			data.ID = utils.DummyID

			updatedData := sharedData
			updatedData.ID = utils.DummyID

			_, err := repo.UpdateByID(ctx, &data, &updatedData)
			assert.ErrorIs(t, err, pgx.ErrNoRows)
		})
	})
}
