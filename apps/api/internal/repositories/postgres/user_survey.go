package postgres

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"api/domain"
	"api/utils"
)

type userSurveyRepository struct {
	db *pgxpool.Pool
}

func NewUserSurveyRepository(db *pgxpool.Pool) *userSurveyRepository {
	return &userSurveyRepository{db}
}

func (repo *userSurveyRepository) Create(
	ctx context.Context,
	request *domain.UserSurveyRequest,
) (*domain.UserSurvey, error) {
	args := pgx.StrictNamedArgs{
		"userID":        request.UserID,
		"skinAnalyzeID": request.SkinAnalyzeID,
		"results":       request.Results,
	}

	query := `
	INSERT INTO user_surveys
	(
		user_id,
		skin_analyze_id,
		results
	)
	VALUES (
		@userID,
		@skinAnalyzeID,
		@results
	)
	RETURNING *
	`

	rows, err := repo.db.Query(
		ctx,
		query,
		args,
	)

	defer rows.Close()

	if err != nil {
		return nil, err
	}

	data, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.UserSurvey],
	)

	if err != nil {
		return nil, err
	}

	return data, nil
}

func (repo *userSurveyRepository) GetByID(
	ctx context.Context,
	id *string,
) (*domain.UserSurvey, error) {
	args := pgx.StrictNamedArgs{"id": *id}

	query := `
	SELECT *
	FROM user_surveys
	WHERE id = @id
	`

	rows, err := repo.db.Query(
		ctx,
		query,
		args,
	)

	defer rows.Close()

	if err != nil {
		return nil, err
	}

	data, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.UserSurvey],
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}

		return nil, err
	}

	return data, nil
}

func (repo *userSurveyRepository) GetMany(
	ctx context.Context,
	filter *domain.UserSurveyFilter,
) ([]domain.UserSurvey, int, error) {
	var (
		composedFilter []string
		filterArgs     = make(pgx.StrictNamedArgs)
	)

	baseQuery := `SELECT * FROM user_surveys`
	countQuery := `SELECT count(*) FROM user_surveys`

	if filter.UserID != "" {
		composedFilter = append(composedFilter, `user_surveys.user_id = @userID`)
		filterArgs["userID"] = filter.UserID
	}

	if filter.SkinAnalyzeID != "" {
		composedFilter = append(
			composedFilter,
			`user_surveys.skin_analyze_id = @skinAnalyzeID`,
		)
		filterArgs["skinAnalyzeID"] = filter.SkinAnalyzeID
	}

	whereClause := utils.GetComposedWhereClause(composedFilter)
	if whereClause != nil {
		baseQuery += fmt.Sprintf("\n%s", *whereClause)
		countQuery += fmt.Sprintf("\n%s", *whereClause)
	}

	paginationClause := filter.Pagination.GetPaginationQuery()
	if paginationClause != nil {
		baseQuery += fmt.Sprintf("%s", *paginationClause)
	}

	var totalData int

	err := repo.db.QueryRow(ctx, countQuery, filterArgs).Scan(&totalData)

	if err != nil {
		return nil, 0, err
	}

	rows, err := repo.db.Query(ctx, baseQuery, filterArgs)
	defer rows.Close()

	if err != nil {
		return nil, 0, err
	}

	results, err := pgx.CollectRows(
		rows,
		pgx.RowToStructByName[domain.UserSurvey],
	)

	if err != nil {
		return nil, 0, err
	}

	return results, totalData, nil
}
