package postgres

import (
	"api/domain"
	"api/utils"
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"
)

type machineSyncLogRepository struct {
	db *pgxpool.Pool
}

func NewMachineSyncLogRepository(db *pgxpool.Pool) *machineSyncLogRepository {
	return &machineSyncLogRepository{db}
}

func (repo *machineSyncLogRepository) CreateMachineSyncLog(
	ctx context.Context,
	ms *domain.MachineSyncLog,
) (*domain.MachineSyncLog, error) {
	query := `
    INSERT INTO machine_sync_logs (
        data
    ) VALUES (
        @data
    ) RETURNING *`
	args := pgx.StrictNamedArgs{
		"data": ms.Data,
	}

	rows, err := repo.db.Query(ctx, query, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create machine sync log")
		return nil, err
	}
	defer rows.Close()

	result, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.MachineSyncLog],
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect machine sync log")
		customErr := utils.CustomPostgresErr(err)
		return nil, customErr
	}

	return result, nil
}

func (repo *machineSyncLogRepository) GetManyMachineSyncLog(
	ctx context.Context,
	filter *domain.MachineSyncLogFilter,
) ([]*domain.MachineSyncLog, *domain.MachineSyncLogStatCount, int, error) {
	var (
		query          = `SELECT * FROM machine_sync_logs`
		statisticQuery = `SELECT
            COUNT(CASE WHEN data->>'status' = 'success' THEN 1 END) as success_count,
            COUNT(CASE WHEN data->>'status' = 'error' THEN 1 END) as error_count
        FROM machine_sync_logs
        WHERE TO_TIMESTAMP(created_at / 1000) >= DATE_TRUNC('day', NOW())
          AND TO_TIMESTAMP(created_at / 1000) < (DATE_TRUNC('day', NOW()) + INTERVAL '1 day')`
		countQuery  = `SELECT COUNT(*) FROM machine_sync_logs`
		whereClause = ""
		args        = pgx.StrictNamedArgs{}
	)

	// Get total count
	var totalData int
	err := repo.db.QueryRow(
		ctx,
		countQuery+whereClause,
		args,
	).Scan(&totalData)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get total count of machine sync logs")
		return nil, nil, 0, err
	}

	// Get Stat Data
	statRows, err := repo.db.Query(ctx, statisticQuery, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get machine sync stats")
		return nil, nil, 0, err
	}
	defer statRows.Close()

	stats, err := pgx.CollectExactlyOneRow(statRows, pgx.RowToAddrOfStructByName[domain.MachineSyncLogStatCount])
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect machine sync logs")
		return nil, nil, 0, err
	}

	// Add pagination
	if filter.Page != 0 && filter.PageSize != 0 {
		offset := (filter.Page - 1) * filter.PageSize
		whereClause += " ORDER BY created_at DESC LIMIT @limit OFFSET @offset"
		args["limit"] = filter.PageSize
		args["offset"] = offset
	}

	// Get data
	rows, err := repo.db.Query(ctx, query+whereClause, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get machine sync logs")
		return nil, nil, 0, err
	}
	defer rows.Close()

	results, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByName[domain.MachineSyncLog])
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect machine sync logs")
		return nil, nil, 0, err
	}

	return results, stats, totalData, nil
}
