package postgres_test

import (
	"context"
	"testing"

	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestConcernAnswerRepository(test *testing.T) {
	dbPool := utils.GetTestDBPool(test)
	test.Cleanup(func() {
		dbPool.Close()
	})

	ctx := context.Background()
	repo := postgres.NewConcernAnswerRepository(dbPool)

	test.Run("SuccessGetConcernAnswerByKey", func(t *testing.T) {
		concernKey := "dry_skin"
		concernAnswer := domain.ConcernAnswer{
			ConcernKey: concernKey,
			Answer:     "Use moisturizer",
		}

		_, err := dbPool.Exec(ctx, `
        INSERT INTO concern_answers (concern_key, answer)
        VALUES ($1, $2)
        `, concernKey, concernAnswer.Answer)
		require.NoError(t, err)

		t.Cleanup(func() {
			_, err = dbPool.Exec(ctx, `
            DELETE FROM concern_answers
            WHERE concern_key = $1
            `, concernKey)
			require.NoError(t, err)
		})

		result, err := repo.GetConcernAnswerByKey(ctx, concernKey)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, concernKey, result.ConcernKey)
		assert.Equal(t, concernAnswer.Answer, result.Answer)
		assert.Nil(t, result.VideoURL)
		assert.NotEmpty(t, result.Timestamp)
	})
}
