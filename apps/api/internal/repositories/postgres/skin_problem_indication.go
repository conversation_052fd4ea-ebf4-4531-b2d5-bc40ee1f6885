package postgres

import (
	"context"
	"fmt"
	"strings"
	"text/template"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"api/domain"
	"api/utils"
)

type skinProblemIndicationRepository struct {
	db *pgxpool.Pool
}

func NewSkinProblemIndicationRepository(
	db *pgxpool.Pool,
) *skinProblemIndicationRepository {
	return &skinProblemIndicationRepository{db}
}

func (repo *skinProblemIndicationRepository) Create(
	ctx context.Context,
	request *domain.SkinProblemIndicationRequest,
) (*domain.SkinProblemIndication, error) {
	var (
		name = cases.
			Title(language.English).
			String(request.Name)

		args = pgx.StrictNamedArgs{
			"name": name,
		}
	)

	insertQuery := `
	INSERT INTO skin_problem_indications (name)
	VALUES (@name)
	RETURNING *
	`

	newRows, err := repo.db.Query(
		ctx,
		insertQuery,
		args,
	)

	defer newRows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to insert skin problem indication")
		return nil, utils.CustomPostgresErr(err)
	}

	newData, err := pgx.CollectExactlyOneRow(
		newRows,
		pgx.RowToAddrOfStructByName[domain.SkinProblemIndication],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get insert skin problem indication result")
		return nil, utils.CustomPostgresErr(err)
	}

	return newData, nil
}

func (repo *skinProblemIndicationRepository) GetByID(
	ctx context.Context,
	id *string,
) (*domain.SkinProblemIndication, error) {
	args := pgx.StrictNamedArgs{"id": &id}

	query := `
	SELECT *
	FROM skin_problem_indications
	WHERE id = @id
	`

	rows, err := repo.db.Query(
		ctx,
		query,
		args,
	)

	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to get skin problem indication by id")
		return nil, err
	}

	data, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.SkinProblemIndication],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get skin problem indication by id result")
		return nil, utils.CustomPostgresErr(err)
	}

	return data, nil
}

func (repo *skinProblemIndicationRepository) UpdateByID(
	ctx context.Context,
	id *string,
	request *domain.SkinProblemIndicationRequest,
) (*domain.SkinProblemIndication, error) {
	var (
		name = cases.
			Title(language.English).
			String(request.Name)

		updateArgs = pgx.StrictNamedArgs{
			"id":   *id,
			"name": name,
		}
	)

	updateQuery := `
	UPDATE skin_problem_indications
	SET
	name = @name,
	updated_at = extract(epoch FROM now()) * 1000
	WHERE id = @id
	RETURNING *
	`

	rows, err := repo.db.Query(
		ctx,
		updateQuery,
		updateArgs,
	)

	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to update skin problem indication")
		return nil, err
	}

	updatedData, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.SkinProblemIndication],
	)

	if err != nil {
		log.Error().Err(err).Msg(
			"Failed to get treatment skin problem indication result",
		)
		return nil, utils.CustomPostgresErr(err)
	}

	return updatedData, nil
}

func (repo *skinProblemIndicationRepository) DeleteByID(
	ctx context.Context,
	id *string,
) (*domain.SkinProblemIndication, error) {
	args := pgx.StrictNamedArgs{"id": &id}

	query := `
	DELETE
	FROM skin_problem_indications
	WHERE id = @id
	RETURNING *
	`

	rows, err := repo.db.Query(
		ctx,
		query,
		args,
	)

	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to run delete skin problem indication query")
		return nil, err
	}

	data, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.SkinProblemIndication],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get delete skin problem indication result")
		return nil, utils.CustomPostgresErr(err)
	}

	return data, nil
}

func (repo *skinProblemIndicationRepository) GetMany(
	ctx context.Context,
	filter *domain.SkinProblemIndicationFilter,
) ([]domain.SkinProblemIndication, int, error) {
	var (
		baseQueryBuilder  = new(strings.Builder)
		countQueryBuilder = new(strings.Builder)
		conditionalTmpl   = make(map[string]string)
		composedFilter    []string
		filterArgs        = make(pgx.StrictNamedArgs)
	)

	baseQueryTmpl := `
	SELECT *
	FROM skin_problem_indications
	{{if .Where}}{{.Where}}{{end -}}
	`

	countQueryTmpl := `
	SELECT count(*)
	FROM skin_problem_indications
	{{if .Where}}{{.Where}}{{end -}}
	`

	if filter.Name != "" {
		composedFilter = append(composedFilter, `skin_problem_indications.name ilike @name`)
		filterArgs["name"] = fmt.Sprintf("%%%s%%", filter.Name)
	}

	whereClause := utils.GetComposedWhereClause(composedFilter)
	if whereClause != nil {
		conditionalTmpl["Where"] = *whereClause
	}

	baseTmpl := template.Must(template.New("baseQuery").Parse(baseQueryTmpl))
	if err := baseTmpl.Execute(baseQueryBuilder, conditionalTmpl); err != nil {
		return nil, 0, fmt.Errorf("Error build base query: %v", err)
	}

	countTmpl := template.Must(template.New("countQuery").Parse(countQueryTmpl))
	if err := countTmpl.Execute(countQueryBuilder, conditionalTmpl); err != nil {
		return nil, 0, fmt.Errorf("Error build count query: %v", err)
	}

	baseQuery := baseQueryBuilder.String()
	countQuery := countQueryBuilder.String()

	paginationClause := filter.Pagination.GetPaginationQuery()
	if paginationClause != nil {
		baseQuery += fmt.Sprintf("\n%s", *paginationClause)
	}

	var totalData int

	err := repo.db.QueryRow(ctx, countQuery, filterArgs).Scan(&totalData)

	if err != nil {
		return nil, 0, err
	}

	rows, err := repo.db.Query(ctx, baseQuery, filterArgs)
	defer rows.Close()

	if err != nil {
		return nil, 0, err
	}

	results, err := pgx.CollectRows(
		rows,
		pgx.RowToStructByName[domain.SkinProblemIndication],
	)

	return results, totalData, nil
}
