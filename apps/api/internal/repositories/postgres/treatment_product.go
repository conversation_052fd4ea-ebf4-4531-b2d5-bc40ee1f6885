package postgres

import (
	"context"
	"fmt"
	"strings"
	"text/template"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"

	"api/domain"
	"api/utils"
)

type treatmentProductRepository struct {
	db *pgxpool.Pool
}

func NewTreatmentProductRepository(db *pgxpool.Pool) *treatmentProductRepository {
	return &treatmentProductRepository{db}
}

func insertTreatmentProductIndication(
	ctx context.Context,
	tx pgx.Tx,
	treatmentProductID string,
	concernIDs []string,
) (err error) {
	var indicationRows []domain.TreatmentProductIndication

	for i, id := range concernIDs {
		indicationRows = append(
			indicationRows,
			domain.TreatmentProductIndication{
				TreatmentProductID:    treatmentProductID,
				TreatmentIndicationID: id,
				IndicationOrder:       i,
			},
		)
	}

	indicationRowSrc := pgx.CopyFromSlice(
		len(indicationRows),
		func(i int) ([]any, error) {
			return []any{
				indicationRows[i].TreatmentProductID,
				indicationRows[i].TreatmentIndicationID,
				indicationRows[i].IndicationOrder,
			}, nil
		},
	)

	_, err = tx.CopyFrom(
		ctx,
		pgx.Identifier{"treatment_products_indications"},
		[]string{
			"treatment_product_id",
			"treatment_indication_id",
			"indication_order",
		},
		indicationRowSrc,
	)

	return err
}

func insertTreatmentProductCategory(
	ctx context.Context,
	tx pgx.Tx,
	treatmentProductID string,
	categoryIDs []string,
) (err error) {
	var categoryRows []domain.TreatmentProductCategory

	for i, id := range categoryIDs {
		categoryRows = append(
			categoryRows,
			domain.TreatmentProductCategory{
				TreatmentProductID:  treatmentProductID,
				TreatmentCategoryID: id,
				CategoryOrder:       i,
			},
		)
	}

	categoryRowSrc := pgx.CopyFromSlice(
		len(categoryRows),
		func(i int) ([]any, error) {
			return []any{
				categoryRows[i].TreatmentProductID,
				categoryRows[i].TreatmentCategoryID,
				categoryRows[i].CategoryOrder,
			}, nil
		},
	)

	_, err = tx.CopyFrom(
		ctx,
		pgx.Identifier{"treatment_products_categories"},
		[]string{
			"treatment_product_id",
			"treatment_category_id",
			"category_order",
		},
		categoryRowSrc,
	)

	return err
}

func insertTreatmentProductSurvey(
	ctx context.Context,
	tx pgx.Tx,
	treatmentProductID string,
	input []domain.TreatmentProductSurveyQuestionInput,
) (err error) {
	var surveyRows []domain.TreatmentProductSurvey

	for i := 0; i < len(input); i++ {
		surveyRows = append(
			surveyRows,
			domain.TreatmentProductSurvey{
				TreatmentProductID: treatmentProductID,
				SurveyID:           input[i].SurveyQuestionID,
				SelectedAnswer:     input[i].SelectedAnswer,
			},
		)
	}

	surveyRowSrc := pgx.CopyFromSlice(
		len(surveyRows),
		func(i int) ([]any, error) {
			return []any{
				surveyRows[i].TreatmentProductID,
				surveyRows[i].SurveyID,
				surveyRows[i].SelectedAnswer,
			}, nil
		},
	)

	_, err = tx.CopyFrom(
		ctx,
		pgx.Identifier{"treatment_products_surveys"},
		[]string{
			"treatment_product_id",
			"survey_id",
			"selected_answer",
		},
		surveyRowSrc,
	)

	return err
}

func getCategory(
	ctx context.Context,
	repo *treatmentProductRepository,
	ids []string,
) (data []domain.TreatmentCategory, err error) {
	categoryArgs := pgx.StrictNamedArgs{"categoryIDs": ids}

	categoryQuery := `
	SELECT
	id,
	name,
	created_at,
	created_by,
	updated_at,
	updated_by
	FROM treatment_categories
	WHERE id = any (@categoryIDs)
	ORDER BY
	array_position(@categoryIDs, treatment_categories.id)
	`

	if len(ids) > 0 {
		rows, err := repo.db.Query(ctx, categoryQuery, categoryArgs)
		defer rows.Close()

		if err != nil {
			log.Error().Err(err).Msg("Failed to query treatment category")
			return nil, err
		}

		categoryData, err := pgx.CollectRows(
			rows,
			pgx.RowToStructByName[domain.TreatmentCategory],
		)

		if err != nil {
			log.Error().Err(err).Msg("Failed to get treatment category")
			return nil, err
		}

		data = categoryData
	}

	if len(ids) != len(data) {
		var foundIDs []string
		for _, item := range data {
			foundIDs = append(foundIDs, item.ID)
		}

		notFoundIDs := utils.DiffIDs(ids, foundIDs)

		return nil, fmt.Errorf(
			"Category IDs not found: %v",
			strings.Join(notFoundIDs, ", "),
		)
	}

	return data, nil
}

func getInterval(
	ctx context.Context,
	repo *treatmentProductRepository,
	id *string,
) (data *domain.TreatmentInterval, err error) {
	intervalArgs := pgx.StrictNamedArgs{"intervalID": id}

	intervalQuery := `
	SELECT
	id,
	days,
	created_at,
	created_by,
	updated_at,
	updated_by
	FROM treatment_intervals
	WHERE id = @intervalID
	`

	if id != nil {
		rows, err := repo.db.Query(ctx, intervalQuery, intervalArgs)
		defer rows.Close()

		if err != nil {
			log.Error().Err(err).Msg("Failed to query treatment interval")
			return nil, err
		}

		intervalData, err := pgx.CollectExactlyOneRow(
			rows,
			pgx.RowToAddrOfStructByName[domain.TreatmentInterval],
		)

		if err != nil {
			log.Error().Err(err).Msg("Failed to get treatment interval")
			if err == pgx.ErrNoRows {
				return nil, fmt.Errorf("Interval ID is not found")
			}
			return nil, err
		}

		data = intervalData
	}

	return data, nil
}

func getIndication(
	ctx context.Context,
	repo *treatmentProductRepository,
	concernIDs []string,
) (concernData []domain.SkinProblem, err error) {
	concernArgs := pgx.StrictNamedArgs{"indicationIDs": concernIDs}

	concernQuery := `
	SELECT
	id,
	name,
	created_at,
	created_by,
	updated_at,
	updated_by
	FROM skin_problems
	WHERE id = any (@indicationIDs)
	ORDER BY
	array_position(@indicationIDs, skin_problems.id)
	`

	if len(concernIDs) > 0 {
		concernRows, err := repo.db.Query(ctx, concernQuery, concernArgs)

		if err != nil {
			log.Error().Err(err).Msg("Failed to query concern")
			return nil, err
		}

		concernData, err = pgx.CollectRows(
			concernRows,
			pgx.RowToStructByName[domain.SkinProblem],
		)

		if err != nil {
			log.Error().Err(err).Msg("Failed to get concern")
			return nil, err
		}
	}

	if len(concernIDs) != len(concernData) {
		var foundIDs []string
		for _, data := range concernData {
			foundIDs = append(foundIDs, data.ID)
		}

		notFoundIDs := utils.DiffIDs(concernIDs, foundIDs)

		return nil, fmt.Errorf(
			"Concern IDs not found: %v",
			strings.Join(notFoundIDs, ", "),
		)
	}

	return concernData, nil
}

func getSurvey(
	ctx context.Context,
	repo *treatmentProductRepository,
	input []domain.TreatmentProductSurveyQuestionInput,
) (data []domain.TreatmentProductSurveyQuestion, err error) {
	var (
		surveyIDs             []string
		surveySelectedAnswers []int
	)

	for _, data := range input {
		surveyIDs = append(surveyIDs, data.SurveyQuestionID)
		surveySelectedAnswers = append(surveySelectedAnswers, data.SelectedAnswer)
	}

	surveyQuestionArgs := pgx.StrictNamedArgs{"surveyIDs": surveyIDs}

	surveyQuestionQuery := `
	SELECT
	id,
	question,
	answers,
	question_order
	FROM survey_questions
	WHERE id = any (@surveyIDs)
	ORDER BY
	array_position(@surveyIDs, survey_questions.id)
	`

	surveyRows, err := repo.db.Query(ctx, surveyQuestionQuery, surveyQuestionArgs)

	data, err = pgx.CollectRows(
		surveyRows,
		func(row pgx.CollectableRow) (domain.TreatmentProductSurveyQuestion, error) {
			var data domain.TreatmentProductSurveyQuestion

			err = surveyRows.Scan(
				&data.ID,
				&data.Question,
				&data.Answers,
				&data.QuestionOrder,
			)

			return data, err
		},
	)

	if err != nil {
		return nil, err
	}

	if len(surveyIDs) != len(data) {
		var foundIDs []string
		for _, item := range data {
			foundIDs = append(foundIDs, item.ID)
		}

		notFoundIDs := utils.DiffIDs(surveyIDs, foundIDs)

		return nil, fmt.Errorf(
			"Survey IDs not found: %v",
			strings.Join(notFoundIDs, ", "),
		)
	}

	for i := 0; i < len(data); i++ {
		if surveySelectedAnswers[i] >= len(data[i].Answers) {
			return nil, fmt.Errorf(
				"Survey selected answer index out of bounds",
			)
		}
	}

	return data, nil
}

func (repo *treatmentProductRepository) GetSupplementaryData(
	ctx context.Context,
	request *domain.TreatmentProductRequest,
) (
	data *domain.TreatmentProductSupplementaryData,
	err error,
) {
	categoryData, err := getCategory(ctx, repo, request.CategoryIDs)

	if err != nil {
		return nil, err
	}

	intervalData, err := getInterval(ctx, repo, request.IntervalID)

	if err != nil {
		return nil, err
	}

	concernData, err := getIndication(
		ctx,
		repo,
		request.ConcernIDs,
	)

	if err != nil {
		return nil, err
	}

	surveyData, err := getSurvey(
		ctx,
		repo,
		request.SurveyQuestions,
	)

	if err != nil {
		return nil, err
	}

	data = &domain.TreatmentProductSupplementaryData{
		Category:        categoryData,
		Interval:        intervalData,
		Concern:         concernData,
		SurveyQuestions: surveyData,
	}

	return data, nil
}

func (repo *treatmentProductRepository) Create(
	ctx context.Context,
	data *domain.TreatmentProduct,
	concernIDs, categoryIDs []string,
	surveyInput []domain.TreatmentProductSurveyQuestionInput,
) (*domain.TreatmentProduct, error) {
	var (
		treatmentProductArgs = pgx.StrictNamedArgs{
			"itemCode":                  data.ItemCode,
			"name":                      data.Name,
			"type":                      data.Type,
			"description":               data.Description,
			"intervalID":                data.IntervalID,
			"price":                     data.Price,
			"mediaUrl":                  data.MediaUrl,
			"thumbnailUrl":              data.ThumbnailUrl,
			"notes":                     data.Notes,
			"quantity":                  data.Quantity,
			"isTopRecommendation":       data.IsTopRecommendation,
			"durationTopRecommendation": data.DurationTopRecommendation,
		}
	)

	insertTreatmentProductQuery := `
	INSERT INTO treatment_products
	(
		item_code,
		name,
		type,
		description,
		interval_id,
		price,
		media_url,
		thumbnail_url,
		notes,
		quantity,
		is_top_recommendation,
		duration_top_recommendation
	)
	VALUES (
		@itemCode,
		@name,
		@type,
		@description,
		@intervalID,
		@price,
		@mediaUrl,
		@thumbnailUrl,
		@notes,
		@quantity,
		@isTopRecommendation,
		@durationTopRecommendation
	)
	RETURNING *
	`

	tx, err := repo.db.Begin(ctx)

	if err != nil {
		log.Error().Err(err).Msg("Error starting db transaction")
		return nil, fmt.Errorf("Error starting db transaction")
	}

	rows, err := tx.Query(
		ctx,
		insertTreatmentProductQuery,
		treatmentProductArgs,
	)

	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to insert treatment product")
		tx.Rollback(ctx)
		return nil, err
	}

	data, err = pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.TreatmentProduct],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get treatment product result")
		tx.Rollback(ctx)
		return nil, utils.CustomPostgresErr(err)
	}

	err = insertTreatmentProductCategory(
		ctx,
		tx,
		data.ID,
		categoryIDs,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to insert treatment product category")
		tx.Rollback(ctx)
		return nil, err
	}

	err = insertTreatmentProductIndication(
		ctx,
		tx,
		data.ID,
		concernIDs,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to insert treatment product indication")
		tx.Rollback(ctx)
		return nil, err
	}

	err = insertTreatmentProductSurvey(
		ctx,
		tx,
		data.ID,
		surveyInput,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to insert treatment product survey")
		tx.Rollback(ctx)
		return nil, err
	}

	if err := tx.Commit(ctx); err != nil {
		log.Error().Err(err).Msg("Failed to commit create treatment product")
		return nil, err
	}

	return data, nil
}

func (repo *treatmentProductRepository) GetByID(
	ctx context.Context,
	id *string,
) (*domain.TreatmentProductResponse, error) {
	args := pgx.StrictNamedArgs{"id": &id}

	query := `
	SELECT
	tp.id,
	tp.item_code,
	tp.name,
	tp.type,
	tp.description,
	tp.price,
	tp.media_url,
	tp.thumbnail_url,
	tp.is_top_recommendation,
	tp.duration_top_recommendation,
	tp.notes,
	tp.quantity,
	tp.created_at,
	tp.created_by,
	tp.updated_at,
	tp.updated_by,
	tc.category,
	to_jsonb(tint.*) as interval,
	jsonb_agg(tind.* order by tpi_m2m.indication_order asc) filter (
		where tpi_m2m.id is not null
	) as concern,
	sq.survey_data as survey_questions
	FROM treatment_products as tp
	LEFT JOIN treatment_products_indications as tpi_m2m on tp.id = tpi_m2m.treatment_product_id
	LEFT JOIN skin_problems as tind on tpi_m2m.treatment_indication_id = tind.id
	LEFT JOIN lateral (
		SELECT
		tpc.treatment_product_id,
		jsonb_agg(tc.* order by tpc.category_order asc) as category
		FROM treatment_products_categories as tpc
		LEFT JOIN treatment_categories as tc on tpc.treatment_category_id = tc.id
		WHERE tpc.treatment_product_id = tp.id
		GROUP BY tpc.treatment_product_id
	) as tc on true
	LEFT JOIN lateral (
		SELECT
		jsonb_agg(
			jsonb_build_object(
				'id', survey_questions.id,
				'question', survey_questions.question,
				'answers', survey_questions.answers,
				'selected_answer', tps.selected_answer,
				'question_order', survey_questions.question_order
			)
			order by survey_questions.question_order asc
		) as survey_data
		FROM treatment_products_surveys as tps
		INNER JOIN survey_questions on tps.survey_id = survey_questions.id
		WHERE tps.treatment_product_id = tp.id
		GROUP BY tps.treatment_product_id
	) as sq on true
	LEFT JOIN treatment_intervals as tint on tp.interval_id = tint.id
	WHERE tp.id = @id
	GROUP BY tp.id, tc.category, tint.id, sq.survey_data
	`

	rows, err := repo.db.Query(
		ctx,
		query,
		args,
	)

	defer rows.Close()

	if err != nil {
		return nil, err
	}

	data, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.TreatmentProductResponse],
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}

		return nil, err
	}

	return data, nil
}

func (repo *treatmentProductRepository) UpdateByID(
	ctx context.Context,
	existingData *domain.TreatmentProductResponse,
	newData *domain.TreatmentProduct,
	concernIDs, categoryIDs []string,
	surveyInput []domain.TreatmentProductSurveyQuestionInput,
) (*domain.TreatmentProduct, error) {
	var (
		deleteTreatmentProductRelationArgs = pgx.StrictNamedArgs{
			"treatmentProductID": existingData.ID,
		}

		updateTreatmentProductArgs = pgx.StrictNamedArgs{
			"id":                        existingData.ID,
			"itemCode":                  newData.ItemCode,
			"name":                      newData.Name,
			"type":                      newData.Type,
			"description":               newData.Description,
			"intervalID":                newData.IntervalID,
			"price":                     newData.Price,
			"mediaUrl":                  newData.MediaUrl,
			"thumbnailUrl":              newData.ThumbnailUrl,
			"notes":                     newData.Notes,
			"quantity":                  newData.Quantity,
			"isTopRecommendation":       newData.IsTopRecommendation,
			"durationTopRecommendation": newData.DurationTopRecommendation,
		}
	)

	deleteTreatmentProductIndicationQuery := `
	DELETE
	FROM treatment_products_indications
	WHERE treatment_product_id = @treatmentProductID
	`

	deleteTreatmentProductCategoryQuery := `
	DELETE
	FROM treatment_products_categories
	WHERE treatment_product_id = @treatmentProductID
	`

	deleteTreatmentProductSurveyQuery := `
	DELETE
	FROM treatment_products_surveys
	WHERE treatment_product_id = @treatmentProductID
	`

	updateTreatmentProductQuery := `
	UPDATE treatment_products
	SET
	item_code = @itemCode,
	name = @name,
	type = @type,
	description = @description,
	interval_id = @intervalID,
	price = @price,
	media_url = @mediaUrl,
	thumbnail_url = @thumbnailUrl,
	notes = @notes,
	quantity = @quantity,
	is_top_recommendation = @isTopRecommendation,
	duration_top_recommendation = @durationTopRecommendation,
	updated_at = extract(epoch FROM now()) * 1000
	WHERE id = @id
	RETURNING *
	`

	tx, err := repo.db.Begin(ctx)

	if err != nil {
		log.Error().Err(err).Msg("Error starting db transaction")
		return nil, fmt.Errorf("Error starting db transaction")
	}

	_, err = tx.Exec(
		ctx,
		deleteTreatmentProductIndicationQuery,
		deleteTreatmentProductRelationArgs,
	)

	if err != nil {
		log.Error().Err(err).Msg(
			"Failed delete existing treatment product indication",
		)
		tx.Rollback(ctx)
		return nil, err
	}

	_, err = tx.Exec(
		ctx,
		deleteTreatmentProductCategoryQuery,
		deleteTreatmentProductRelationArgs,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed delete existing treatment product category")
		tx.Rollback(ctx)
		return nil, err
	}

	_, err = tx.Exec(
		ctx,
		deleteTreatmentProductSurveyQuery,
		deleteTreatmentProductRelationArgs,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed delete existing treatment product survey")
		tx.Rollback(ctx)
		return nil, err
	}

	rows, err := tx.Query(
		ctx,
		updateTreatmentProductQuery,
		updateTreatmentProductArgs,
	)

	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to update treatment product")
		tx.Rollback(ctx)
		return nil, err
	}

	updatedData, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.TreatmentProduct],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get treatment product result")
		tx.Rollback(ctx)
		return nil, utils.CustomPostgresErr(err)
	}

	err = insertTreatmentProductCategory(
		ctx,
		tx,
		updatedData.ID,
		categoryIDs,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to insert treatment product category")
		tx.Rollback(ctx)
		return nil, err
	}

	err = insertTreatmentProductIndication(
		ctx,
		tx,
		existingData.ID,
		concernIDs,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to insert treatment product indication")
		tx.Rollback(ctx)
		return nil, err
	}

	err = insertTreatmentProductSurvey(
		ctx,
		tx,
		existingData.ID,
		surveyInput,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to insert treatment product survey")
		tx.Rollback(ctx)
		return nil, err
	}

	if err := tx.Commit(ctx); err != nil {
		log.Error().Err(err).Msg("Failed to commit update treatment product")
		return nil, err
	}

	return updatedData, nil
}

func (repo *treatmentProductRepository) DeleteByID(
	ctx context.Context,
	id *string,
) (*domain.TreatmentProduct, error) {
	args := pgx.StrictNamedArgs{"id": &id}

	query := `
	DELETE
	FROM treatment_products
	WHERE id = @id
	RETURNING *
	`

	rows, err := repo.db.Query(ctx, query, args)
	defer rows.Close()

	if err != nil {
		return nil, err
	}

	data, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.TreatmentProduct],
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return data, nil
}

func (repo *treatmentProductRepository) GetMany(
	ctx context.Context,
	filter *domain.TreatmentProductFilter,
) ([]domain.TreatmentProductGetMany, int, error) {
	var (
		baseQueryBuilder  = new(strings.Builder)
		countQueryBuilder = new(strings.Builder)
		conditionalTmpl   = make(map[string]string)
		composedFilter    []string
		filterArgs        = make(pgx.StrictNamedArgs)
	)

	sharedQuery := `
	SELECT
	treatment_products.*,
	tc.category,
	sq.survey_data as survey_questions,
	to_jsonb(tint.*) as interval,
	jsonb_agg(tind.* order by tpi_m2m.indication_order) filter (where tind.id is not null) as concern
	FROM treatment_products
	LEFT JOIN treatment_products_indications as tpi_m2m on treatment_products.id = tpi_m2m.treatment_product_id
	LEFT JOIN lateral (
		SELECT
		sp.*,
		jsonb_agg(spi.* order by spg.problem_order asc) filter (where spi.id is not null) as concern_indications
		FROM skin_problems as sp
		LEFT JOIN skin_problem_groups as spg on sp.id = spg.skin_problem_id
		LEFT JOIN skin_problem_indications as spi on spg.skin_problem_indication_id = spi.id
		WHERE tpi_m2m.treatment_indication_id = sp.id
		GROUP BY sp.id
	) as tind on true
	LEFT JOIN lateral (
		SELECT
		tpc.treatment_product_id,
		jsonb_agg(tc.* order by tpc.category_order asc) as category
		FROM treatment_products_categories as tpc
		LEFT JOIN treatment_categories as tc on tpc.treatment_category_id = tc.id
		WHERE tpc.treatment_product_id = treatment_products.id
		GROUP BY tpc.treatment_product_id
	) as tc on true
	LEFT JOIN lateral (
		SELECT
		jsonb_agg(
			jsonb_build_object(
				'id', survey_questions.id,
				'question', survey_questions.question,
				'answers', survey_questions.answers,
				'selected_answer', tps.selected_answer,
				'question_order', survey_questions.question_order
			)
			order by survey_questions.question_order asc
		) as survey_data
		FROM treatment_products_surveys as tps
		INNER JOIN survey_questions on tps.survey_id = survey_questions.id
		WHERE tps.treatment_product_id = treatment_products.id
		GROUP BY tps.treatment_product_id
	) as sq on true
	LEFT JOIN treatment_intervals as tint on treatment_products.interval_id = tint.id
	{{if .Where}}{{.Where}}{{end -}}
	GROUP BY treatment_products.id, tc.category, tint.id, sq.survey_data
	`

	// Because treatment_products can have multiple categories, and we need to
	// get all treatment_products that have the specified category ids, that's why
	// we aggregate all categories in treatment_products and filter category ids
	// from the aggregate results.
	baseQueryTmpl := fmt.Sprintf(`
	SELECT *
	FROM (
		%s
		{{if .OrderBy}}{{.OrderBy}}{{end -}}
	) as results
	{{if .WhereCategory}}{{.WhereCategory}}{{end -}}
	`, sharedQuery)

	countQueryTmpl := fmt.Sprintf(`
	SELECT count(*)
	FROM (
		%s
	) as results
	{{if .WhereCategory}}{{.WhereCategory}}{{end -}}
	`, sharedQuery)

	if filter.Name != "" {
		composedFilter = append(composedFilter, `treatment_products.name ilike @name`)
		filterArgs["name"] = fmt.Sprintf("%%%s%%", filter.Name)
	}

	if filter.Types != nil {
		composedFilter = append(
			composedFilter,
			`treatment_products.type = any (@types)`,
		)
		filterArgs["types"] = filter.Types
	}

	if filter.MinPrice != nil && filter.MaxPrice != nil {
		composedFilter = append(
			composedFilter,
			`(treatment_products.price >= @minPrice and treatment_products.price <= @maxPrice)`,
		)
		filterArgs["minPrice"] = *filter.MinPrice
		filterArgs["maxPrice"] = *filter.MaxPrice
	}

	if filter.IsTopRecommendation != nil {
		composedFilter = append(
			composedFilter,
			`treatment_products.is_top_recommendation = @isTopRecommendation`,
		)
		filterArgs["isTopRecommendation"] = filter.IsTopRecommendation
	}

	if len(filter.CategoryIDs) > 0 {
		conditionalTmpl["WhereCategory"] = "WHERE jsonb_path_query_array(results.category, '$[*].id') ?| @categoryIDs"
		filterArgs["categoryIDs"] = filter.CategoryIDs
	}

	whereClause := utils.GetComposedWhereClause(composedFilter)
	if whereClause != nil {
		conditionalTmpl["Where"] = *whereClause
	}

	if filter.SortColumn != "" && filter.SortOrder != "" {
		var column string

		// Check domain file on oneof struct tag.
		switch filter.SortColumn {
		case "top_recommendation_name":
			column = `
			(
				treatment_products.is_top_recommendation = true
				AND treatment_products.duration_top_recommendation >= (
					extract(epoch from now()) * 1000
				)
			) desc,
			treatment_products.name`
		default:
			column = fmt.Sprintf("treatment_products.%s", filter.SortColumn)
		}

		conditionalTmpl["OrderBy"] = fmt.Sprintf(
			"ORDER BY %s %s\n",
			column,
			filter.SortOrder,
		)
	}

	baseTmpl := template.Must(template.New("baseQuery").Parse(baseQueryTmpl))
	if err := baseTmpl.Execute(baseQueryBuilder, conditionalTmpl); err != nil {
		return nil, 0, fmt.Errorf("Error build base query: %v", err)
	}

	countTmpl := template.Must(template.New("countQuery").Parse(countQueryTmpl))
	if err := countTmpl.Execute(countQueryBuilder, conditionalTmpl); err != nil {
		return nil, 0, fmt.Errorf("Error build count query: %v", err)
	}

	baseQuery := baseQueryBuilder.String()
	countQuery := countQueryBuilder.String()

	paginationClause := filter.Pagination.GetPaginationQuery()
	if paginationClause != nil {
		baseQuery += fmt.Sprintf("\n%s", *paginationClause)
	}

	var totalData int

	err := repo.db.QueryRow(ctx, countQuery, filterArgs).Scan(&totalData)

	if err != nil {
		return nil, 0, err
	}

	rows, err := repo.db.Query(ctx, baseQuery, filterArgs)
	defer rows.Close()

	if err != nil {
		return nil, 0, err
	}

	results, err := pgx.CollectRows(
		rows,
		pgx.RowToStructByName[domain.TreatmentProductGetMany],
	)

	if err != nil {
		return nil, 0, err
	}

	return results, totalData, nil
}
