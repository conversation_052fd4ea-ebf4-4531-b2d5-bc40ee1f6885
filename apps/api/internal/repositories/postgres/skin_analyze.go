package postgres

import (
	"context"
	"fmt"
	"strings"
	"text/template"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"

	"api/domain"
	"api/utils"
)

type SkinAnalyzeRepository struct {
	Conn *pgxpool.Pool
}

func NewSkinAnalyzeRepository(conn *pgxpool.Pool) *SkinAnalyzeRepository {
	return &SkinAnalyzeRepository{
		Conn: conn,
	}
}

func (r *SkinAnalyzeRepository) CreateSkinAnalyze(
	ctx context.Context,
	skinAnalyze *domain.SkinAnalyze,
) (*domain.SkinAnalyze, error) {
	query := `INSERT INTO
        skin_analyzes
    (
        name,
        actual_age,
        input_date,
        phone_number,
        evaluation_rate,
        skin_age,
        skin_condition,
        rgb_pore,
        rgb_spot,
        rgb_wrinkle,
        pl_texture,
        uv_porphyrin,
        uv_pigmentation,
        uv_moisture,
        sensitive_area,
        brown_area,
        uv_damage,
        suggestion,
        path_images,
        path_pdf
    )
    VALUES
    (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20
    ) RETURNING *`
	rows, err := r.Conn.Query(ctx, query,
		skinAnalyze.Name,
		skinAnalyze.ActualAge,
		skinAnalyze.InputDate,
		skinAnalyze.PhoneNumber,
		skinAnalyze.EvaluationRate,
		skinAnalyze.SkinAge,
		skinAnalyze.SkinCondition,
		skinAnalyze.RGBPore,
		skinAnalyze.RGBSpot,
		skinAnalyze.RGBWrinkle,
		skinAnalyze.PLTexture,
		skinAnalyze.UVPorphyrin,
		skinAnalyze.UVPigmentation,
		skinAnalyze.UVMoisture,
		skinAnalyze.SensitiveArea,
		skinAnalyze.BrownArea,
		skinAnalyze.UVDamage,
		skinAnalyze.Suggestion,
		skinAnalyze.PathImages,
		skinAnalyze.PathPDF,
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create skin analyze")
		return nil, err
	}
	defer rows.Close()

	skinAnalyze, err = pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[domain.SkinAnalyze])
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect skin analyze")
		return nil, err
	}

	return skinAnalyze, nil
}

func (r *SkinAnalyzeRepository) GetSkinAnalyzeByID(
	ctx context.Context,
	id string,
) (*domain.SkinAnalyze, error) {
	query := `SELECT * FROM skin_analyzes WHERE id = $1`
	rows, err := r.Conn.Query(ctx, query, id)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get skin analyze by ID")
		return nil, err
	}
	defer rows.Close()

	skinAnalyze, err := pgx.CollectOneRow(rows, pgx.RowToAddrOfStructByName[domain.SkinAnalyze])
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect skin analyze")
		return nil, err
	}
	return skinAnalyze, nil
}

func (r *SkinAnalyzeRepository) UpdateSkinAnalyzeByID(
	ctx context.Context,
	id string,
	skinAnalyze *domain.SkinAnalyze,
) (*domain.SkinAnalyze, error) {
	args := pgx.StrictNamedArgs{
		"id":              id,
		"name":            skinAnalyze.Name,
		"actual_age":      skinAnalyze.ActualAge,
		"input_date":      skinAnalyze.InputDate,
		"phone_number":    skinAnalyze.PhoneNumber,
		"evaluation_rate": skinAnalyze.EvaluationRate,
		"skin_age":        skinAnalyze.SkinAge,
		"skin_condition":  skinAnalyze.SkinCondition,
		"rgb_pore":        skinAnalyze.RGBPore,
		"rgb_spot":        skinAnalyze.RGBSpot,
		"rgb_wrinkle":     skinAnalyze.RGBWrinkle,
		"pl_texture":      skinAnalyze.PLTexture,
		"uv_porphyrin":    skinAnalyze.UVPorphyrin,
		"uv_pigmentation": skinAnalyze.UVPigmentation,
		"uv_moisture":     skinAnalyze.UVMoisture,
		"sensitive_area":  skinAnalyze.SensitiveArea,
		"brown_area":      skinAnalyze.BrownArea,
		"uv_damage":       skinAnalyze.UVDamage,
		"suggestion":      skinAnalyze.Suggestion,
		"path_images":     skinAnalyze.PathImages,
		"path_pdf":        skinAnalyze.PathPDF,
	}
	query := `UPDATE skin_analyzes SET
        name = @name,
        actual_age = @actual_age,
        input_date = @input_date,
        phone_number = @phone_number,
        evaluation_rate = @evaluation_rate,
        skin_age = @skin_age,
        skin_condition = @skin_condition,
        rgb_pore = @rgb_pore,
        rgb_spot = @rgb_spot,
        rgb_wrinkle = @rgb_wrinkle,
        pl_texture = @pl_texture,
        uv_porphyrin = @uv_porphyrin,
        uv_pigmentation = @uv_pigmentation,
        uv_moisture = @uv_moisture,
        sensitive_area = @sensitive_area,
        brown_area = @brown_area,
        uv_damage = @uv_damage,
        suggestion = @suggestion,
        path_images = @path_images,
        path_pdf = @path_pdf,
        updated_at = EXTRACT(epoch FROM now()) * 1000::numeric
    WHERE id = @id RETURNING *`
	rows, err := r.Conn.Query(ctx, query, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to update skin analyze by ID")
		return nil, err
	}
	defer rows.Close()

	updatedSkinAnalyze, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[domain.SkinAnalyze])
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect updated skin analyze")
		return nil, err
	}

	return updatedSkinAnalyze, nil
}

func (r *SkinAnalyzeRepository) GetManySkinAnalyzes(
	ctx context.Context,
	filter *domain.SkinAnalyzeFilter,
) ([]*domain.SkinAnalyze, int, error) {
	var (
		baseQueryBuilder  = new(strings.Builder)
		countQueryBuilder = new(strings.Builder)
		conditionalTmpl   = make(map[string]string)
		composedFilter    []string
		args              = pgx.StrictNamedArgs{}
	)

	baseQueryTmpl := `
    SELECT
    *
    FROM skin_analyzes
    {{if .Where}}{{.Where}}{{end -}}
    {{if .OrderBy}}{{.OrderBy}}{{end -}}
    `

	countQueryTmpl := `
    SELECT
    COUNT(*)
    FROM skin_analyzes
    {{if .Where}}{{.Where}}{{end -}}`

	if filter.Name != "" {
		composedFilter = append(composedFilter, `skin_analyzes.name ilike @name`)
		args["name"] = fmt.Sprintf("%%%s%%", filter.Name)
	}

	whereClause := utils.GetComposedWhereClause(composedFilter)
	if whereClause != nil {
		conditionalTmpl["Where"] = *whereClause
	}

	if filter.SortColumn != "" && filter.SortOrder != "" {
		var column string

		// Check domain file on oneof struct tag.
		switch filter.SortColumn {
		default:
			column = fmt.Sprintf("skin_analyzes.%s", filter.SortColumn)
		}

		conditionalTmpl["OrderBy"] = fmt.Sprintf(
			"ORDER BY %s %s\n",
			column,
			filter.SortOrder,
		)
	}

	baseTmpl := template.Must(template.New("baseQuery").Parse(baseQueryTmpl))
	if err := baseTmpl.Execute(baseQueryBuilder, conditionalTmpl); err != nil {
		return nil, 0, fmt.Errorf("error build base query: %v", err)
	}

	countTmpl := template.Must(template.New("countQuery").Parse(countQueryTmpl))
	if err := countTmpl.Execute(countQueryBuilder, conditionalTmpl); err != nil {
		return nil, 0, fmt.Errorf("error build count query: %v", err)
	}

	baseQuery := baseQueryBuilder.String()
	countQuery := countQueryBuilder.String()

	paginationClause := filter.GetPaginationQuery()
	if paginationClause != nil {
		baseQuery += " " + *paginationClause
	}

	var totalCount int
	err := r.Conn.QueryRow(ctx, countQuery, args).Scan(&totalCount)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get total count of skin analyzes")
		return nil, 0, err
	}

	rows, err := r.Conn.Query(ctx, baseQuery, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get all skin analyzes")
		return nil, 0, err
	}
	defer rows.Close()

	skinAnalyzes, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByName[domain.SkinAnalyze])
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect skin analyzes")
		return nil, 0, err
	}

	return skinAnalyzes, totalCount, nil
}
