package postgres

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"api/domain"
	"api/utils"
)

type parameterSkinEvaluationRepository struct {
	db *pgxpool.Pool
}

func NewParameterSkinEvaluationRepository(
	db *pgxpool.Pool,
) *parameterSkinEvaluationRepository {
	return &parameterSkinEvaluationRepository{db}
}

func (repo *parameterSkinEvaluationRepository) GetByID(
	ctx context.Context,
	id *string,
) (*domain.ParameterSkinEvaluation, error) {
	args := pgx.StrictNamedArgs{"id": &id}

	query := `
	SELECT *
	FROM parameter_skin_evaluations
	WHERE id = @id
	`

	rows, err := repo.db.Query(
		ctx,
		query,
		args,
	)

	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg(
			"Failed to run parameter skin evaluation get by id query",
		)
		return nil, err
	}

	data, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.ParameterSkinEvaluation],
	)

	if err != nil {
		log.Error().Err(err).Msg(
			"Failed to get by id parameter skin evaluation result",
		)
		return nil, utils.CustomPostgresErr(err)
	}

	return data, nil
}

func (repo *parameterSkinEvaluationRepository) UpdateByID(
	ctx context.Context,
	id *string,
	request *domain.ParameterSkinEvaluationRequest,
) (*domain.ParameterSkinEvaluation, error) {
	name := cases.
		Title(language.English).
		String(request.Name)

	args := pgx.StrictNamedArgs{
		"id":         &id,
		"name":       name,
		"lowerPoint": request.LowerPoint,
		"upperPoint": request.UpperPoint,
	}

	updateQuery := `
	UPDATE parameter_skin_evaluations
	SET
	name = @name,
	lower_point = @lowerPoint,
	upper_point = @upperPoint,
	updated_at = extract(epoch FROM now()) * 1000
	WHERE id = @id
	RETURNING *
	`

	rows, err := repo.db.Query(ctx, updateQuery, args)
	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to update parameter skin evaluation")
		return nil, err
	}

	updatedData, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.ParameterSkinEvaluation],
	)

	if err != nil {
		log.Error().Err(err).Msg(
			"Failed to get updated parameter skin evaluation result",
		)
		return nil, utils.CustomPostgresErr(err)
	}

	return updatedData, nil
}

func (repo *parameterSkinEvaluationRepository) GetMany(
	ctx context.Context,
	filter *domain.ParameterSkinEvaluationFilter,
) ([]domain.ParameterSkinEvaluation, int, error) {
	baseQuery := `
	SELECT *
	FROM parameter_skin_evaluations
	ORDER BY parameter_order
	`

	countQuery := `SELECT count(*) FROM parameter_skin_evaluations`

	paginationClause := filter.Pagination.GetPaginationQuery()
	if paginationClause != nil {
		baseQuery += fmt.Sprintf("%s", *paginationClause)
	}

	var totalData int

	err := repo.db.QueryRow(ctx, countQuery).Scan(&totalData)

	if err != nil {
		log.Error().Err(err).Msg(
			"Failed to get many total parameter skin evaluation",
		)
		return nil, 0, err
	}

	rows, err := repo.db.Query(ctx, baseQuery)
	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg(
			"Failed to run get many parameter skin evaluation query",
		)
		return nil, 0, err
	}

	results, err := pgx.CollectRows(
		rows,
		pgx.RowToStructByName[domain.ParameterSkinEvaluation],
	)

	if err != nil {
		log.Error().Err(err).Msg(
			"Failed to get many parameter skin evaluation results",
		)
		return nil, 0, err
	}

	return results, totalData, nil
}
