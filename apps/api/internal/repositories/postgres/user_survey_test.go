package postgres_test

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"
)

func TestUserSurveyRepository(test *testing.T) {
	dbPool := utils.GetTestDBPool(test)
	test.Cleanup(func() {
		dbPool.Close()
	})

	ctx := context.Background()
	repo := postgres.NewUserSurveyRepository(dbPool)

	cleanupUserByIDQuery := `
	delete
	from users
	where id = @id
	`

	cleanupSkinAnalyzeByIDQuery := `
	delete
	from skin_analyzes
	where id = @id
	`

	cleanupUserSurveyByIDQuery := `
	delete
	from user_surveys
	where id = @id
	`

	userData := domain.User{
		Name:        "Test dummy user",
		Email:       "<EMAIL>",
		PhoneNumber: "0891234768",
		Role:        domain.Client,
	}

	userArgs := pgx.NamedArgs{
		"userName":        userData.Name,
		"userEmail":       userData.Email,
		"userPhoneNumber": userData.PhoneNumber,
		"userRole":        userData.Role,
	}

	err := dbPool.QueryRow(
		ctx,
		`
		INSERT INTO users
		(name, email, phone_number, role)
		VALUES
		(@userName, @userEmail, @userPhoneNumber, @userRole)
		Returning id
		`,
		userArgs,
	).Scan(&userData.ID)

	require.NoError(test, err, "Create dummy user should not have an error")

	test.Cleanup(func() {
		err := utils.CleanupTestDummyData(
			ctx,
			dbPool,
			cleanupUserByIDQuery,
			pgx.NamedArgs{"id": userData.ID},
		)
		require.NoErrorf(test, err, "Error cleanup user: %v", err)
	})

	// Remove this if user client implemented.
	var (
		skinAnalyzeData = &domain.SkinAnalyze{
			Name:       "John Doe",
			InputDate:  "2023-10-01",
			RGBPore:    75,
			RGBSpot:    50,
			RGBWrinkle: 70,
			PathImages: []string{"image1.jpg", "image2.jpg"},
			PathPDF:    "/path/to/pdf",
		}
	)

	skinAnalyzeArgs := pgx.NamedArgs{
		"name":       skinAnalyzeData.Name,
		"inputDate":  skinAnalyzeData.InputDate,
		"rgbPore":    skinAnalyzeData.RGBPore,
		"rgbSpot":    skinAnalyzeData.RGBSpot,
		"rgbWrinkle": skinAnalyzeData.RGBWrinkle,
		"pathImages": skinAnalyzeData.PathImages,
		"pathPDF":    skinAnalyzeData.PathPDF,
	}

	err = dbPool.QueryRow(
		ctx,
		`
		INSERT INTO skin_analyzes
		(name, input_date, rgb_pore, rgb_spot, rgb_wrinkle, path_images, path_pdf)
		VALUES
		(
			@name,
			@inputDate,
			@rgbPore,
			@rgbSpot,
			@rgbWrinkle,
			@pathImages,
			@pathPDF
		)
		Returning id
		`,
		skinAnalyzeArgs,
	).Scan(&skinAnalyzeData.ID)

	require.NoError(test, err, "Create dummy user should not have an error")

	test.Cleanup(func() {
		err := utils.CleanupTestDummyData(
			ctx,
			dbPool,
			cleanupSkinAnalyzeByIDQuery,
			pgx.NamedArgs{"id": skinAnalyzeData.ID},
		)
		require.NoErrorf(test, err, "Error cleanup skin analyze: %v", err)
	})

	// Copy the value, not the pointer.
	var (
		sharedRequest = domain.UserSurveyRequest{
			UserID:        &userData.ID,
			SkinAnalyzeID: &skinAnalyzeData.ID,
			Results: domain.UserSurveyResultJSON{
				{
					Question: "Masalah Kulit apa yang ingin diselesaikan?",
					Answers:  []string{"Wrinkle", "Dark spot", "Acne"},
				},
			},
		}

		sharedFilterData = domain.UserSurveyFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
			UserID: userData.ID,
		}
	)

	test.Run("Success CRUD", func(t *testing.T) {
		request := sharedRequest

		// Create new data for test.
		data, err := repo.Create(ctx, &request)
		require.NoError(t, err, "Create data should not have an error.")
		require.NotEmpty(t, data.ID, "ID should not be empty")

		t.Cleanup(func() {
			args := pgx.NamedArgs{"id": data.ID}
			err := utils.CleanupTestDummyData(
				ctx,
				dbPool,
				cleanupUserSurveyByIDQuery,
				args,
			)

			if err != nil {
				t.Fatalf("Error cleanup: %v", err)
			}
		})

		t.Run("Get data by id", func(t *testing.T) {
			result, err := repo.GetByID(ctx, &data.ID)
			assert.NoError(t, err, "Get data by id should not have an error.")
			assert.NotNil(t, result, "Get data by id result should not be nil.")
		})

		t.Run("Get many data", func(t *testing.T) {
			filterData := sharedFilterData

			results, totalData, err := repo.GetMany(ctx, &filterData)
			assert.NoError(t, err, "Get many data should not have an error.")
			assert.NotNil(t, results, "Get many data result should not be nil.")
			assert.GreaterOrEqual(
				t,
				totalData,
				1,
				"Get many total data should have been equal or more than one",
			)
		})
	})

	test.Run("Failed CRUD", func(t *testing.T) {
		t.Parallel()

		t.Run("Not found user id when create data", func(t *testing.T) {
			t.Parallel()

			request := sharedRequest
			request.UserID = &utils.DummyID

			data, err := repo.Create(ctx, &request)
			assert.Error(t, err, "Create data should have an error.")
			assert.Nil(t, data, "New data should have been nil.")
		})
	})
}
