package postgres_test

import (
	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"
	"context"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestTreatmentRepository(test *testing.T) {
	dbPool := utils.GetTestDBPool(test)
	test.Cleanup(func() {
		dbPool.Close()
	})

	repo := postgres.NewTreatmentRepository(dbPool)
	ctx := context.Background()

	test.Run("Success GetAllTreatment", func(t *testing.T) {
		// Mock data
		concernGroup := domain.Treatment{
			Name:         "Dry Skin",
			Type:         "treatment",
			SKU:          "sku123",
			ConcernGroup: "Concern Group",
			Concern:      "Dryness",
			Price:        100,
		}

		row := dbPool.QueryRow(ctx, `
        INSERT INTO treatments (name, type, sku, concern_group, concern, price)
        VALUES ($1, $2, $3, $4, $5, $6) RETURNING id
        `,
			concernGroup.Name,
			concernGroup.Type,
			concernGroup.SKU,
			concernGroup.ConcernGroup,
			concernGroup.Concern,
			concernGroup.Price,
		)

		var id string
		err := row.Scan(&id)
		require.NoError(t, err)

		t.Cleanup(func() {
			_, err = dbPool.Exec(ctx, `
            DELETE FROM treatments
            WHERE id = $1
            `, id)
			require.NoError(t, err)
		})

		// Call the method
		result, err := repo.GetAllTreatment(ctx)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotEmpty(t, result)
		require.Greater(t, len(result), 0)
	})
}
