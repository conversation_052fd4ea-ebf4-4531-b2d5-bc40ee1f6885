package postgres

import (
	"api/domain"
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"
)

type ConcernGroupRepository struct {
	db *pgxpool.Pool
}

func NewConcernGroupRepository(
	db *pgxpool.Pool,
) *ConcernGroupRepository {
	return &ConcernGroupRepository{db}
}

func (repo *ConcernGroupRepository) GetAllConcernGroup(
	ctx context.Context,
) ([]*domain.ConcernGroup, error) {
	query := `
    SELECT
        *
    FROM concern_groups
    `

	rows, err := repo.db.Query(ctx, query)
	if err != nil {
		log.Error().Err(err).Msg("Query error")
		return nil, err
	}
	defer rows.Close()

	concernGroups, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByName[domain.ConcernGroup])
	if err != nil {
		log.Error().Err(err).Msg("Error collecting rows")
		return nil, err
	}

	return concernGroups, nil
}
