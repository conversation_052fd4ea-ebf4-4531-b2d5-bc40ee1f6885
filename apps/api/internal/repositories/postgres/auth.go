package postgres

import (
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"api/domain"
)

type authRepository struct {
	db *pgxpool.Pool
}

func NewAuthRepository(db *pgxpool.Pool) *authRepository {
	return &authRepository{db}
}

func (repo *authRepository) VerifyEmail(
	ctx context.Context,
	request *domain.LoginRequest,
) (*domain.User, error) {
	query := `
	SELECT
	users.id,
	users.password,
	users.role
	FROM users
	WHERE email = @email
	`

	args := pgx.NamedArgs{"email": request.Email}

	var data domain.User

	err := repo.db.QueryRow(ctx, query, args).
		Scan(
			&data.ID,
			&data.Password,
			&data.Role,
		)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}

		return nil, err
	}

	return &data, nil
}
