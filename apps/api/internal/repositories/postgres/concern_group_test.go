package postgres_test

import (
	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"
	"context"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestConcernGroupRepository(test *testing.T) {
	dbPool := utils.GetTestDBPool(test)
	test.Cleanup(func() {
		dbPool.Close()
	})

	repo := postgres.NewConcernGroupRepository(dbPool)
	ctx := context.Background()

	test.Run("Success GetAllConcernGroup", func(t *testing.T) {
		// Mock data
		concernGroup := domain.ConcernGroup{
			Name:    "Dry Skin",
			Concern: "Dryness",
		}

		row := dbPool.QueryRow(ctx, `
        INSERT INTO concern_groups (name, concern)
        VALUES ($1, $2) RETURNING id
        `, concernGroup.Name, concernGroup.Concern)

		var id string
		err := row.Scan(&id)
		require.NoError(t, err)

		t.Cleanup(func() {
			_, err = dbPool.Exec(ctx, `
            DELETE FROM concern_groups
            WHERE id = $1
            `, id)
			require.NoError(t, err)
		})

		// Call the method
		result, err := repo.GetAllConcernGroup(ctx)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotEmpty(t, result)
		require.Greater(t, len(result), 0)
	})
}
