package postgres_test

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"
)

func TestParameterSkinEvaluationRepository(t *testing.T) {
	dbPool := utils.GetTestDBPool(t)
	t.Cleanup(func() {
		dbPool.Close()
	})

	ctx := context.Background()
	repo := postgres.NewParameterSkinEvaluationRepository(dbPool)

	// Copy the value, not the pointer.
	var (
		sharedRequest = domain.ParameterSkinEvaluationRequest{
			Name:       "Test Parameter",
			LowerPoint: 0,
			UpperPoint: 100,
		}

		sharedFilter = domain.ParameterSkinEvaluationFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
		}
	)

	args := pgx.NamedArgs{
		"name":           sharedRequest.Name,
		"lowerPoint":     sharedRequest.LowerPoint,
		"upperPoint":     sharedRequest.UpperPoint,
		"parameterOrder": 4,
	}

	insertQuery := `
	INSERT INTO parameter_skin_evaluations
	(name, lower_point, upper_point, parameter_order)
	VALUES
	(@name, @lowerPoint, @upperPoint, @parameterOrder)
	RETURNING id
	`

	cleanupByIDQuery := `
	DELETE
	FROM parameter_skin_evaluations
	WHERE id = @id
	`

	var id string
	err := dbPool.QueryRow(ctx, insertQuery, args).Scan(&id)
	require.NoError(t, err)

	args["id"] = id

	t.Cleanup(func() {
		err := utils.CleanupTestDummyData(
			ctx,
			dbPool,
			cleanupByIDQuery,
			args,
		)

		require.NoErrorf(t, err, "Error cleanup: %v", err)
	})

	t.Run("Success GetByID", func(t *testing.T) {
		data, err := repo.GetByID(ctx, &id)
		assert.NoError(t, err)
		assert.NotNil(t, data)
	})

	t.Run("Failed GetByID not found", func(t *testing.T) {
		data, err := repo.GetByID(ctx, &utils.DummyID)
		assert.NoError(t, err)
		assert.Nil(t, data)
	})

	t.Run("Success UpdateByID", func(t *testing.T) {
		request := sharedRequest
		request.Name = "Test Updated Parameter"

		data, err := repo.UpdateByID(ctx, &id, &request)
		assert.NoError(t, err)
		assert.NotNil(t, data)
	})

	t.Run("Failed UpdateByID not found", func(t *testing.T) {
		request := sharedRequest

		data, err := repo.UpdateByID(ctx, &utils.DummyID, &request)
		assert.NoError(t, err)
		assert.Nil(t, data)
	})

	t.Run("Success GetMany", func(t *testing.T) {
		filter := sharedFilter

		data, totalData, err := repo.GetMany(ctx, &filter)
		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.GreaterOrEqual(t, totalData, 1)
	})
}
