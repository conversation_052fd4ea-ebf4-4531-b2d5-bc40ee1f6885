#!/bin/bash

ENV_FILE="./apps/api/.env"

# Load environment variables
set -o allexport
source $ENV_FILE
set +o allexport

# Check migrate command availability based on OS
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    if ! command -v migrate &>/dev/null; then
        echo "Error: 'migrate' command not found on macOS"
        echo "Install with: brew install golang-migrate"
        exit 1
    fi
elif [[ "$OSTYPE" == "linux"* ]]; then
    # Linux
    if ! command -v migrate &>/dev/null; then
        echo "Error: 'migrate' command not found on Linux"
        echo "Install with: curl -L https://github.com/golang-migrate/migrate/releases/download/v4.16.2/migrate.linux-amd64.tar.gz | tar xvz"
        echo "Then move binary: sudo mv migrate /usr/local/bin/"
        exit 1
    fi
else
    # Other OS
    if ! command -v migrate &>/dev/null; then
        echo "Error: 'migrate' command not found"
        echo "Visit: https://github.com/golang-migrate/migrate/tree/master/cmd/migrate"
        exit 1
    fi
fi

if [ -z "$DATABASE_URL" ]; then
    echo "Error: DATABASE_URL not found in file $ENV_FILE"
    exit 1
fi

case "$1" in
  'create')
    migrate create -ext sql -dir ./apps/api/migration -seq $2
    ;;
  'reset')
    migrate -database "${DATABASE_URL}" -path ./apps/api/migration down -all && \
    migrate -database "${DATABASE_URL}" -path ./apps/api/migration up
    ;;
  *)
    migrate -database "${DATABASE_URL}" -path ./apps/api/migration "$@"
    ;;
esac
