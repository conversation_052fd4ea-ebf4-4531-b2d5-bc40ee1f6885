CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    name VARCHAR NOT NULL,
    email VARCHAR NOT NULL UNIQUE,
    phone_number VARCHAR NOT NULL UNIQUE,
    password VARCHAR NULL,
    role VARCHAR NOT NULL,
    address VARCHAR NULL,
    gender VARCHAR NULL,
    birth_date BIGINT NULL,
    created_by UUID NULL,
    created_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    updated_by UUID NULL,
    updated_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Primary lookup indexes
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_name ON users(name);

-- Timestamp indexes
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_users_updated_at ON users(updated_at);

-- Composite indexes for common queries
