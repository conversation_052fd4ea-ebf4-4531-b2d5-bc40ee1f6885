CREATE TABLE IF NOT EXISTS treatment_products_surveys (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    treatment_product_id UUID NOT NULL REFERENCES treatment_products(id) ON DELETE CASCADE,
    survey_id UUID NOT NULL REFERENCES survey_questions(id) ON DELETE CASCADE,
    selected_answer int NOT NULL DEFAULT 0
);

ALTER TABLE IF EXISTS treatment_products
DROP COLUMN IF EXISTS survey_questions;
