-- Migration
CREATE TABLE IF NOT EXISTS skin_analyzes (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    name VARCHAR NOT NULL,
    birth_date VARCHAR NULL,
    input_date VARCHAR NOT NULL,
    phone_number VARCHAR NULL,
    evaluation_rate INT NULL,
    skin_age INT NULL,
    skin_condition VARCHAR NULL,
    rgb_pore INT NOT NULL,
    rgb_spot INT NOT NULL,
    rgb_wrinkle INT NOT NULL,
    pl_texture INT NULL,
    uv_porphyrin INT NULL,
    uv_pigmentation INT NULL,
    uv_moisture INT NULL,
    sensitive_area INT NULL,
    brown_area INT NULL,
    uv_damage INT NULL,
    suggestion TEXT NULL,
    path_images VARCHAR[] NOT NULL,
    path_pdf VARCHAR NOT NULL,
    created_by UUID NULL,
    created_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    updated_by UUID NULL,
    updated_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Primary lookup indexes

-- Timestamp indexes
CREATE INDEX IF NOT EXISTS idx_skin_analyzes_created_at ON skin_analyzes(created_at);
CREATE INDEX IF NOT EXISTS idx_skin_analyzes_updated_at ON skin_analyzes(updated_at);

-- Composite indexes for common queries
