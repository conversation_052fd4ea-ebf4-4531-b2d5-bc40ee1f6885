UPDATE treatment_products_indications
SET indication_order = 5
WHERE (indication_number = 2 and indication_order = 2);

UPDATE treatment_products_indications
SET indication_order = 4
WHERE (indication_number = 2 and indication_order = 1);

UPDATE treatment_products_indications
SET indication_order = 3
WHERE (indication_number = 2 and indication_order = 0);

ALTER TABLE IF EXISTS treatment_products_indications
DROP COLUMN IF EXISTS indication_number;
