CREATE TABLE IF NOT EXISTS parameter_skin_evaluations (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    name VARCHAR NOT NULL UNIQUE,
    lower_point INT NOT NULL,
    upper_point INT NOT NULL,
    parameter_order int NOT NULL,
    created_by UUID NULL REFERENCES users(id),
    created_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    updated_by UUID NULL REFERENCES users(id),
    updated_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric)
);
