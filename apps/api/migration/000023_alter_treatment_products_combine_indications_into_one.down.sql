ALTER TABLE IF EXISTS treatment_products_indications
ADD COLUMN IF NOT EXISTS indication_number INT NULL;

UPDATE treatment_products_indications
SET
indication_number = 1
WHERE indication_order = 2;

UPDATE treatment_products_indications
SET
indication_number = 1
WHERE indication_order = 1;

UPDATE treatment_products_indications
SET
indication_number = 1
WHERE indication_order = 0;

UPDATE treatment_products_indications
SET
indication_number = 2,
indication_order = 2
WHERE indication_order = 5;

UPDATE treatment_products_indications
SET
indication_number = 2,
indication_order = 1
WHERE indication_order = 4;

UPDATE treatment_products_indications
SET
indication_number = 2,
indication_order = 0
WHERE indication_order = 3;

ALTER TABLE IF EXISTS treatment_products_indications
ALTER COLUMN indication_number SET NOT NULL;
