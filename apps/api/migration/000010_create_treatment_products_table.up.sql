CREATE TABLE IF NOT EXISTS treatment_products (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    name VARCHAR NOT NULL,
    type VARCHAR NOT NULL,
    category_id UUID NULL REFERENCES treatment_categories(id),
    remarks VARCHAR NOT NULL,
    description VARCHAR NOT NULL,
    interval_id UUID NULL REFERENCES treatment_intervals(id),
    price BIGINT NOT NULL,
    survey_questions JSONB NOT NULL,
    created_by UUID NULL REFERENCES users(id),
    created_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    updated_by UUID NULL REFERENCES users(id),
    updated_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric)
);

CREATE TABLE IF NOT EXISTS treatment_products_indications (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    treatment_product_id UUID NOT NULL REFERENCES treatment_products(id) ON DELETE CASCADE,
    treatment_indication_id UUID NOT NULL REFERENCES treatment_indications(id),
    indication_number INT NOT NULL,
    indication_order INT NOT NULL
);
