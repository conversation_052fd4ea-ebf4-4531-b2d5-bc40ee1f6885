-- Migration
CREATE TABLE IF NOT EXISTS concern_groups (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    name VARCHAR NOT NULL,
    concern VARCHAR NOT NULL,
    created_by <PERSON><PERSON><PERSON> NULL,
    created_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    updated_by UUID NULL,
    updated_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Primary lookup indexes

-- Timestamp indexes
CREATE INDEX IF NOT EXISTS idx_concern_groups_created_at ON concern_groups(created_at);
CREATE INDEX IF NOT EXISTS idx_concern_groups_updated_at ON concern_groups(updated_at);

-- Composite indexes for common queries
