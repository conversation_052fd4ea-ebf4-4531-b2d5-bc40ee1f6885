CREATE TABLE IF NOT EXISTS user_surveys (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    results JSONB NOT NULL,
    created_by UUID NULL REFERENCES users(id),
    created_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    updated_by UUID NULL REFERENCES users(id),
    updated_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric)
);
