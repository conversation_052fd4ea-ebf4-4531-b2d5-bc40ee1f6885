-- Delete existing duplicate question.
DELETE
FROM survey_questions
WHERE id = '2e6066d1-d76e-4cdc-ac5d-55964b7d8741';

ALTER TABLE IF EXISTS survey_questions
ADD COLUMN IF NOT EXISTS category VARCHAR NULL;

-- Add category value in existing dummy data.
do $$ begin
  perform column_name
  from information_schema.columns
  where table_name = 'survey_questions'
  and column_name = 'category';

  if found then
    update survey_questions
    set category = 'informational';
  end if;
end $$;

ALTER TABLE IF EXISTS survey_questions
ALTER COLUMN category SET NOT NULL;
