-- Migration
CREATE TABLE IF NOT EXISTS survey_questions (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    parent_question_id UUID NULL,
    parent_question_answer INT NULL,
    description VARCHAR NOT NULL,
    question VARCHAR NOT NULL,
    answers JSONB NOT NULL,
    is_multiple BOOLEAN NOT NULL DEFAULT FALSE,
    type VARCHAR NOT NULL,
    question_order INT NOT NULL,
    created_by UUID NULL,
    created_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    updated_by UUID NULL,
    updated_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    FOREIGN KEY (parent_question_id) REFERENCES survey_questions(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Primary lookup indexes

-- Timestamp indexes
CREATE INDEX IF NOT EXISTS idx_survey_questions_created_at ON survey_questions(created_at);
CREATE INDEX IF NOT EXISTS idx_survey_questions_updated_at ON survey_questions(updated_at);

-- Composite indexes for common queries
