-- Migration
CREATE TABLE IF NOT EXISTS treatments (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    name VARCHAR NOT NULL,
    type VARCHAR NOT NULL,
    sku VARCHAR NOT NULL,
    concern_group VARCHAR NOT NULL,
    concern VARCHAR NOT NULL,
    price BIGINT NOT NULL,
    created_by UUID NULL,
    created_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    updated_by UUID NULL,
    updated_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Primary lookup indexes

-- Timestamp indexes
CREATE INDEX IF NOT EXISTS idx_treatments_created_at ON treatments(created_at);
CREATE INDEX IF NOT EXISTS idx_treatments_updated_at ON treatments(updated_at);

-- Composite indexes for common queries
