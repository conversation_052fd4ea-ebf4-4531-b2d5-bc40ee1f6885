-- Migration
CREATE TABLE IF NOT EXISTS treatment_intervals (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    days INT NULL UNIQUE,
    created_by UUID NULL,
    created_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    updated_by UUID NULL,
    updated_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Primary lookup indexes

-- Timestamp indexes
CREATE INDEX IF NOT EXISTS idx_treatment_intervals_created_at ON treatment_intervals(created_at);
CREATE INDEX IF NOT EXISTS idx_treatment_intervals_updated_at ON treatment_intervals(updated_at);

-- Composite indexes for common queries
