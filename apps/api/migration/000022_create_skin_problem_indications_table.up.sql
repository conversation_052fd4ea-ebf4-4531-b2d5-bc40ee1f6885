ALTER TABLE IF EXISTS treatment_categories
ALTER COLUMN name SET NOT NULL;

ALTER TABLE IF EXISTS skin_problems
ALTER COLUMN name SET NOT NULL;

ALTER TABLE IF EXISTS treatment_intervals
ALTER COLUMN days SET NOT NULL;

CREATE TABLE IF NOT EXISTS skin_problem_indications (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    name VARCHAR NOT NULL UNIQUE,
    created_by UUID NULL REFERENCES users(id),
    created_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    updated_by UUID NULL REFERENCES users(id),
    updated_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric)
);

CREATE TABLE IF NOT EXISTS skin_problem_groups (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    skin_problem_id UUID NOT NULL REFERENCES skin_problems(id) ON DELETE CASCADE,
    skin_problem_indication_id UUID NOT NULL REFERENCES skin_problem_indications(id),
    problem_order INT NOT NULL
);
