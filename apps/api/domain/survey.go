package domain

type SurveyType string

const (
	SingleFull     SurveyType = "single_full"
	MultipleFull   SurveyType = "multiple_full"
	HorizontalBar  SurveyType = "horizontal_bar"
	Text           SurveyType = "text"
	SurveyDropdown SurveyType = "dropdown"
)

type SurveyCategory string

const (
	Contraindication SurveyCategory = "contraindication"
	Informational    SurveyCategory = "informational"
)

type SurveyAnswer struct {
	Title       string  `json:"title"`
	Description *string `json:"description"`
	ImageUrl    *string `json:"image_url"`
}

type SurveyAnswerJSON []SurveyAnswer

type Survey struct {
	ID                   string           `json:"id"`
	ParentQuestionID     *string          `json:"parent_question_id"`
	ParentQuestionAnswer *int             `json:"parent_question_answer"`
	Description          string           `json:"description"`
	Question             string           `json:"question"`
	Answers              SurveyAnswerJSON `json:"answers"`
	IsMultiple           bool             `json:"is_multiple"`
	Type                 SurveyType       `json:"type"`
	QuestionOrder        int              `json:"question_order"`
	IsStatic             bool             `json:"is_static"`
	Category             SurveyCategory   `json:"category"`
	Timestamp
}

type SurveyRequest struct {
	ParentQuestionID     *string          `json:"parent_question_id" validate:"required_with=ParentQuestionAnswer,omitempty" example:"3fa85f64-5717-4562-b3fc-2c963f66afa6"`
	ParentQuestionAnswer *int             `json:"parent_question_answer" validate:"required_with=ParentQuestionID,omitempty,gte=0"`
	Description          string           `json:"description" validate:"required"`
	Question             string           `json:"question" validate:"required"`
	Answers              SurveyAnswerJSON `json:"answers" validate:"required"`
	IsMultiple           bool             `json:"is_multiple"`
	Type                 SurveyType       `json:"type" validate:"required"`
	QuestionOrder        int              `json:"question_order" validate:"gte=0"`
	Category             SurveyCategory   `json:"category" validate:"required,oneof=informational contraindication"`
}

type SurveyResponse struct {
	ID                   string           `json:"id" example:"3fa85f64-5717-4562-b3fc-2c963f66afa6"`
	ParentQuestionID     *string          `json:"parent_question_id"`
	ParentQuestionAnswer *int             `json:"parent_question_answer"`
	Description          string           `json:"description"`
	Question             string           `json:"question"`
	Answers              SurveyAnswerJSON `json:"answers"`
	IsMultiple           bool             `json:"is_multiple"`
	Type                 SurveyType       `json:"type"`
	QuestionOrder        int              `json:"question_order"`
	IsStatic             bool             `json:"is_static"`
	Category             SurveyCategory   `json:"category"`
	Timestamp
}

type SurveyRequestChildQuestion struct {
	ParentQuestionAnswer int              `json:"parent_question_answer" validate:"required,gte=0"`
	Description          string           `json:"description" validate:"required"`
	Question             string           `json:"question" validate:"required"`
	Answers              SurveyAnswerJSON `json:"answers" validate:"required"`
	IsMultiple           bool             `json:"is_multiple"`
	Type                 SurveyType       `json:"type" validate:"required"`
	QuestionOrder        int              `json:"question_order" validate:"gte=0"`
	Category             SurveyCategory   `json:"category" validate:"required,oneof=informational contraindication"`
}

type SurveyRequestNested struct {
	Description    string                       `json:"description" validate:"required"`
	Question       string                       `json:"question" validate:"required"`
	Answers        SurveyAnswerJSON             `json:"answers" validate:"required"`
	IsMultiple     bool                         `json:"is_multiple"`
	Type           SurveyType                   `json:"type" validate:"required"`
	QuestionOrder  int                          `json:"question_order" validate:"gte=0"`
	Category       SurveyCategory               `json:"category" validate:"required,oneof=informational contraindication"`
	ChildQuestions []SurveyRequestChildQuestion `json:"child_questions"`
}

type SurveyResponseNested struct {
	SurveyResponse
	ChildQuestions []SurveyResponse `json:"child_questions"`
}

type SurveyFilter struct {
	Pagination
	IsStatic   *bool    `json:"is_static" query:"is_static"`
	Question   *string  `json:"question" query:"question"`
	Type       []string `json:"type" query:"type" validate:"omitempty,dive,oneof=single_full multiple_full horizontal_bar text dropdown" example:"single_full"`
	Category   []string `json:"category" query:"category" validate:"omitempty,dive,oneof=contraindication informational" example:"contraindication"`
	Groups     []string `json:"groups" query:"groups" validate:"omitempty,dive,oneof=primary secondary" example:"primary"`
	SortColumn string   `json:"sort_column" query:"sort_column" validate:"required_with=SortOrder,omitempty,oneof=id question category group mobile"`
	SortOrder  string   `json:"sort_order" query:"sort_order" validate:"required_with=SortColumn,omitempty,oneof=asc desc"`
}

type SurveyFilterNested struct {
	Pagination
	IsStatic   *bool    `json:"is_static" query:"is_static"`
	Question   *string  `json:"question" query:"question"`
	Type       []string `json:"type" query:"type" validate:"omitempty,dive,oneof=single_full multiple_full horizontal_bar text dropdown"`
	Category   []string `json:"category" query:"category" validate:"omitempty,dive,oneof=contraindication informational"`
	SortColumn string   `json:"sort_column" query:"sort_column" validate:"required_with=SortOrder,omitempty,oneof=id question category mobile"`
	SortOrder  string   `json:"sort_order" query:"sort_order" validate:"required_with=SortColumn,omitempty,oneof=asc desc"`
}

func (input *SurveyRequest) ToRepo() Survey {
	return Survey{
		ParentQuestionID:     input.ParentQuestionID,
		ParentQuestionAnswer: input.ParentQuestionAnswer,
		Description:          input.Description,
		Question:             input.Question,
		Answers:              input.Answers,
		IsMultiple:           input.IsMultiple,
		Type:                 input.Type,
		QuestionOrder:        input.QuestionOrder,
		Category:             input.Category,
	}
}

func (data *Survey) ToResponse() SurveyResponse {
	return SurveyResponse{
		ID:                   data.ID,
		ParentQuestionID:     data.ParentQuestionID,
		ParentQuestionAnswer: data.ParentQuestionAnswer,
		Description:          data.Description,
		Question:             data.Question,
		Answers:              data.Answers,
		IsMultiple:           data.IsMultiple,
		Type:                 data.Type,
		QuestionOrder:        data.QuestionOrder,
		IsStatic:             data.IsStatic,
		Category:             data.Category,
		Timestamp:            data.Timestamp,
	}
}
