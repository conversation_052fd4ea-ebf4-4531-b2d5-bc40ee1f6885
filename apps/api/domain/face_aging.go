package domain

type FaceAgingConcern string

const (
	FaceAgingConcernPore         FaceAgingConcern = "pores"
	FaceAgingConcernAcne         FaceAgingConcern = "acne"
	FaceAgingConcernScar         FaceAgingConcern = "scar"
	FaceAgingConcernPigmentation FaceAgingConcern = "pigment"
	FaceAgingConcernWrinkle      FaceAgingConcern = "wrinkles"
	FaceAgingConcernSensitive    FaceAgingConcern = "sensitive"
	FaceAgingConcernBeautify     FaceAgingConcern = "beautify"
)

type FaceAgingArea string

const (
	FaceAgingAreaUpper FaceAgingArea = "upper"
	FaceAgingAreaMid   FaceAgingArea = "mid"
	FaceAgingAreaLower FaceAgingArea = "lower"
)

type FaceAgingConcernDetailMLRequest struct {
	Concern FaceAgingConcern `json:"concern"`
	Areas   []FaceAgingArea  `json:"areas"`
}

type FaceAgingConcernRequest struct {
	Concerns   []FaceAgingConcernDetailMLRequest `json:"concerns"`
	IsBeautify bool                              `json:"is_beautify"`
}

type FaceAgingGeneratedMLResponse struct {
	Concern              FaceAgingConcern `json:"concern"`
	GeneratedImageURL    string           `json:"generated_image_url"`
	SelectedAreaImageURL string           `json:"selected_area_url"`
}

type FaceAgingConcernResponse struct {
	GeneratedImages []FaceAgingGeneratedMLResponse `json:"generated_images"`
}

type FaceAgingConcernMLRequest struct {
	ImagePath string                            `json:"image_path"`
	MaskPath  *string                           `json:"mask_path"`
	Concerns  []FaceAgingConcernDetailMLRequest `json:"concerns"`
}

type FaceAgingConcernMLResponse struct {
	ImageURL       []string                       `json:"image_url"`
	GeneratedImage []FaceAgingGeneratedMLResponse `json:"generated_image"`
}
