package domain

type UserSurveyResult struct {
	Question string   `json:"question"`
	Answers  []string `json:"answers"`
}

type UserSurveyResultJSON []UserSurveyResult

type UserSurvey struct {
	ID            string               `json:"id"`
	UserID        *string              `json:"user_id"`
	SkinAnalyzeID *string              `json:"skin_analyze_id"`
	Results       UserSurveyResultJSON `json:"results"`
	Timestamp
}

type UserSurveyRequest struct {
	UserID        *string              `json:"user_id" validate:"omitempty,uuid4" example:"3fa85f64-5717-4562-b3fc-2c963f66afa6"`
	SkinAnalyzeID *string              `json:"skin_analyze_id" validate:"omitempty,uuid4" example:"3fa85f64-5717-4562-b3fc-2c963f66afa6"`
	Results       UserSurveyResultJSON `json:"results" validate:"required"`
}

type UserSurveyFilter struct {
	Pagination
	UserID        string `json:"user_id" query:"user_id" validate:"omitempty,uuid4" example:"3fa85f64-5717-4562-b3fc-2c963f66afa6"`
	SkinAnalyzeID string `json:"skin_analyze_id" query:"skin_analyze_id" validate:"omitempty,uuid4" example:"3fa85f64-5717-4562-b3fc-2c963f66afa6"`
}
