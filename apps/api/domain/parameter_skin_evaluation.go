package domain

type ParameterSkinEvaluation struct {
	ID             string `json:"id"`
	Name           string `json:"name"`
	LowerPoint     int    `json:"lower_point"`
	UpperPoint     int    `json:"upper_point"`
	ParameterOrder int    `json:"parameter_order"`
	Timestamp
}

type ParameterSkinEvaluationRequest struct {
	Name       string `json:"name" validate:"required"`
	LowerPoint int    `json:"lower_point" validate:"gte=0"`
	UpperPoint int    `json:"upper_point" validate:"gte=0,gtfield=LowerPoint"`
}

type ParameterSkinEvaluationFilter struct {
	Pagination
}
