package domain

type TreatmentProductType string

const (
	TreatmentType TreatmentProductType = "treatment"
	ProductType   TreatmentProductType = "product"
)

type TreatmentProductSurveyQuestion struct {
	ID             string           `json:"id"`
	Question       string           `json:"question"`
	Answers        SurveyAnswerJSON `json:"answers"`
	SelectedAnswer int              `json:"selected_answer"`
	QuestionOrder  int              `json:"question_order"`
}

type TreatmentProductSurveyQuestionJSON []TreatmentProductSurveyQuestion

type TreatmentProduct struct {
	ID                        string               `json:"id"`
	ItemCode                  string               `json:"item_code"`
	Name                      string               `json:"name"`
	Type                      TreatmentProductType `json:"type"`
	Description               string               `json:"description"`
	IntervalID                *string              `json:"interval_id"`
	Price                     int64                `json:"price"`
	MediaUrl                  *string              `json:"media_url"`
	ThumbnailUrl              *string              `json:"thumbnail_url"`
	Notes                     *string              `json:"notes"`
	Quantity                  int                  `json:"quantity"`
	IsTopRecommendation       bool                 `json:"is_top_recommendation"`
	DurationTopRecommendation *int64               `json:"duration_top_recommendation"`
	Timestamp
}

// Representation of table many-to-many between treatment_products and
// skin_problems.
type TreatmentProductIndication struct {
	ID                    string `json:"id"`
	TreatmentProductID    string `json:"treatment_product_id"`
	TreatmentIndicationID string `json:"treatment_indication_id"`
	IndicationOrder       int    `json:"indication_order"`
}

// Representation of table many-to-many between treatment_products and
// treatment_categories.
type TreatmentProductCategory struct {
	ID                  string `json:"id"`
	TreatmentProductID  string `json:"treatment_product_id"`
	TreatmentCategoryID string `json:"treatment_category_id"`
	CategoryOrder       int    `json:"category_order"`
}

// Representation of table many-to-many between treatment_products and
// survey_questions.
type TreatmentProductSurvey struct {
	ID                 string `json:"id"`
	TreatmentProductID string `json:"treatment_product_id"`
	SurveyID           string `json:"survey_id"`
	SelectedAnswer     int    `json:"selected_answer"`
}

type TreatmentProductSurveyQuestionInput struct {
	SurveyQuestionID string `json:"survey_question_id"`
	SelectedAnswer   int    `json:"selected_answer"`
}

type TreatmentProductSupplementaryData struct {
	Category        []TreatmentCategory                `json:"category"`
	Interval        *TreatmentInterval                 `json:"interval"`
	Concern         []SkinProblem                      `json:"concern"`
	SurveyQuestions TreatmentProductSurveyQuestionJSON `json:"survey_questions"`
}

type TreatmentProductRequest struct {
	ItemCode                  string                                `json:"item_code" validate:"required" example:"SPDT69420"`
	Name                      string                                `json:"name" validate:"required"`
	Type                      TreatmentProductType                  `json:"type" validate:"required"`
	CategoryIDs               []string                              `json:"category_ids" validate:"omitempty,max=3,unique"`
	ConcernIDs                []string                              `json:"concern_ids" validate:"omitempty,max=6,unique"`
	Description               string                                `json:"description" validate:"required"`
	IntervalID                *string                               `json:"interval_id"`
	Price                     int64                                 `json:"price" validate:"gte=0"`
	MediaUrl                  *string                               `json:"media_url" validate:"omitempty" example:"media/path/to/file"`     // AWS S3 object key
	ThumbnailUrl              *string                               `json:"thumbnail_url" validate:"omitempty" example:"media/path/to/file"` // AWS S3 object key
	Notes                     *string                               `json:"notes"`
	Quantity                  int                                   `json:"quantity" validate:"gte=0"`
	IsTopRecommendation       bool                                  `json:"is_top_recommendation" validate:"required_with=DurationTopRecommendation,omitempty"`
	DurationTopRecommendation *int64                                `json:"duration_top_recommendation" validate:"required_with=IsTopRecommendation,omitempty" example:"1747776517509"`
	SurveyQuestions           []TreatmentProductSurveyQuestionInput `json:"survey_questions"`
}

type TreatmentProductResponse struct {
	ID                        string               `json:"id"`
	ItemCode                  string               `json:"item_code"`
	Name                      string               `json:"name"`
	Type                      TreatmentProductType `json:"type"`
	Description               string               `json:"description"`
	Price                     int64                `json:"price"`
	MediaUrl                  *string              `json:"media_url"`
	ThumbnailUrl              *string              `json:"thumbnail_url"`
	Notes                     *string              `json:"notes"`
	Quantity                  int                  `json:"quantity"`
	IsTopRecommendation       bool                 `json:"is_top_recommendation"`
	DurationTopRecommendation *int64               `json:"duration_top_recommendation"`
	Timestamp
	TreatmentProductSupplementaryData
}

type TreatmentProductGetManyConcern struct {
	SkinProblem
	ConcernIndications []SkinProblemIndication `json:"concern_indications"`
}

type TreatmentProductGetMany struct {
	TreatmentProduct
	Category        []TreatmentCategory                `json:"category"`
	Interval        *TreatmentInterval                 `json:"interval"`
	Concern         []TreatmentProductGetManyConcern   `json:"concern"`
	SurveyQuestions TreatmentProductSurveyQuestionJSON `json:"survey_questions"`
}

type TreatmentProductFilter struct {
	Pagination
	Name                string   `json:"name" query:"name"`
	Types               []string `json:"types" query:"types" validate:"omitempty,dive,oneof=treatment product" example:"treatment"`
	MinPrice            *int64   `json:"min_price" query:"min_price" validate:"required_with=MaxPrice,omitempty,gte=0,ltefield=MaxPrice"`
	MaxPrice            *int64   `json:"max_price" query:"max_price" validate:"required_with,omitempty,gte=0"`
	IsTopRecommendation *bool    `json:"is_top_recommendation" query:"is_top_recommendation"`
	CategoryIDs         []string `json:"category_ids" query:"category_ids" validate:"omitempty,dive,uuid4"`
	SortColumn          string   `json:"sort_column" query:"sort_column" validate:"required_with=SortOrder,omitempty,oneof=id name type price created_at updated_at top_recommendation_name"`
	SortOrder           string   `json:"sort_order" query:"sort_order" validate:"required_with=SortColumn,omitempty,oneof=asc desc"`
}
