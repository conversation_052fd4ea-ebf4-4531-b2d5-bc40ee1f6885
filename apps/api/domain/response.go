package domain

type SingleResponse[D any] struct {
	Code    int    `json:"code"`
	Status  string `json:"status"`
	Data    D      `json:"data"`
	Message string `json:"message"`
}

type PaginationData[D any] struct {
	Content    []D `json:"content"`
	Page       int `json:"page"`
	PageSize   int `json:"page_size"`
	TotalData  int `json:"total_data"`
	TotalPages int `json:"total_pages"`
}

type PaginationResponse[D any] struct {
	Code    int               `json:"code"`
	Status  string            `json:"status"`
	Data    PaginationData[D] `json:"data"`
	Message string            `json:"message"`
}

type PaginationStatsData[D any, E any] struct {
	Content    []D `json:"content"`
	Stats      E   `json:"stats"`
	Page       int `json:"page"`
	PageSize   int `json:"page_size"`
	TotalData  int `json:"total_data"`
	TotalPages int `json:"total_pages"`
}

type PaginationStatsResponse[D any, E any] struct {
	Code    int                       `json:"code"`
	Status  string                    `json:"status"`
	Data    PaginationStatsData[D, E] `json:"data"`
	Message string                    `json:"message"`
}

type Empty struct{}
