package domain

type SkinProblemType string

const (
	SkinProblemSpecial SkinProblemType = "special"
	SkinProblemGeneral SkinProblemType = "general"
)

type SkinProblem struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Timestamp
}

type SkinProblemGroup struct {
	ID                      string `json:"id"`
	SkinProblemID           string `json:"skin_problem_id"`
	SkinProblemIndicationID string `json:"skin_problem_indication_id"`
	ProblemOrder            int    `json:"problem_order"`
}

type SkinProblemRequest struct {
	Name                     string   `json:"name" validate:"required" example:"Acne"`
	SkinProblemIndicationIDs []string `json:"skin_problem_indication_ids" validate:"required,unique,max=4,dive,uuid4"`
}

type SkinProblemResponse struct {
	ID                     string                  `json:"id" example:"3fa85f64-5717-4562-b3fc-2c963f66afa6"`
	Name                   string                  `json:"name" example:"Acne"`
	SkinProblemIndications []SkinProblemIndication `json:"skin_problem_indications"`
	Timestamp
}

type SkinProblemFilter struct {
	Pagination
	Name string           `json:"name" query:"name"`
	Type *SkinProblemType `json:"type" query:"type" validate:"omitempty,oneof=special general"`
}
