package domain

import (
	"fmt"
	"math"
)

type Pagination struct {
	Page     int `json:"page" query:"page" validate:"required_with=PageSize,omitempty,gte=1" default:"1"`
	PageSize int `json:"page_size" query:"page_size" validate:"required_with=Page,omitempty,gte=1" default:"10"`
}

func (p *Pagination) GetPaginationQuery() *string {
	if p.Page == 0 || p.PageSize == 0 {
		return nil
	}
	offset := (p.Page - 1) * p.PageSize
	paginationClause := fmt.Sprintf("LIMIT %d OFFSET %d", p.PageSize, offset)
	return &paginationClause
}

func (p *Pagination) GetTotalPages(totalData int) int {
	if p.Page == 0 || p.PageSize == 0 || totalData == 0 {
		return 1
	}
	ceilCalculation := math.Ceil(
		float64(totalData) / float64(p.PageSize),
	)
	return int(ceilCalculation)
}
