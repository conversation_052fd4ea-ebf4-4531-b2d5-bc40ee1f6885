package domain

import (
	"github.com/golang-jwt/jwt/v5"
)

type LoginRequest struct {
	Email    string `json:"email" validate:"required,email" example:"<EMAIL>"`
	Password string `json:"password" validate:"required,min=8"`
}

type LoginResponse struct {
	Token string `json:"token"`
}

type JWTClaim struct {
	ID   string   `json:"id" validate:"required,uuid4" example:"3fa85f64-5717-4562-b3fc-2c963f66afa6"`
	Role UserRole `json:"role" validate:"required" example:"admin"`
	jwt.RegisteredClaims
}
