package domain

type UserRole string

const (
	Admin  UserRole = "admin"
	Branch UserRole = "branch"
	Client UserRole = "client"
)

type Gender string

const (
	Male   Gender = "male"
	Female Gender = "female"
)

type User struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Email       string   `json:"email"`
	PhoneNumber string   `json:"phone_number"`
	Password    *string  `json:"password"`
	Role        UserRole `json:"role"`
	Address     *string  `json:"address"`
	Gender      *Gender  `json:"gender"`
	BirthDate   *int64   `json:"birth_date"`
	Timestamp
}

type UserRequest struct {
	Name        string   `json:"name" validate:"required" example:"John Doe"`
	Email       string   `json:"email" validate:"required,email" example:"<EMAIL>"`
	PhoneNumber string   `json:"phone_number" validate:"required" example:"081234567890"`
	Password    string   `json:"password" validate:"omitempty,min=8" example:"password123"`
	Role        UserRole `json:"role" validate:"required" example:"admin"`
	Address     *string  `json:"address" example:"123 Main St"`
	Gender      *Gender  `json:"gender" example:"male"`
	BirthDate   *int64   `json:"birth_date" example:"946684800000"`
}

type UserResponse struct {
	ID          string   `json:"id" example:"3fa85f64-5717-4562-b3fc-2c963f66afa6"`
	Name        string   `json:"name" example:"John Doe"`
	Email       string   `json:"email" example:"<EMAIL>"`
	PhoneNumber string   `json:"phone_number" example:"081234567890"`
	Role        UserRole `json:"role" example:"admin"`
	Address     *string  `json:"address" example:"123 Main St"`
	Gender      *Gender  `json:"gender" example:"male"`
	BirthDate   *int64   `json:"birth_date" example:"946684800000"`
	Timestamp
}

type UserFilter struct {
	Pagination
	Name  *string  `json:"name" query:"name"`
	Roles []string `json:"roles" query:"roles" validate:"omitempty,dive,oneof=admin branch client" example:"admin"`
}

type UpdatePasswordRequest struct {
	Password string `json:"password" validate:"required,min=8" example:"new-password123"`
}

func (input *UserRequest) ToRepo() User {
	return User{
		Name:        input.Name,
		Email:       input.Email,
		PhoneNumber: input.PhoneNumber,
		Password:    &input.Password,
		Role:        input.Role,
		Address:     input.Address,
		Gender:      input.Gender,
		BirthDate:   input.BirthDate,
	}
}

func (data *User) ToResponse() UserResponse {
	return UserResponse{
		ID:          data.ID,
		Name:        data.Name,
		Email:       data.Email,
		PhoneNumber: data.PhoneNumber,
		Role:        data.Role,
		Address:     data.Address,
		Gender:      data.Gender,
		BirthDate:   data.BirthDate,
		Timestamp:   data.Timestamp,
	}
}
