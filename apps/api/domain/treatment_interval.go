package domain

type TreatmentInterval struct {
	ID   string `json:"id"`
	Days int    `json:"days"`
	Timestamp
}

type TreatmentIntervalRequest struct {
	Days int `json:"days" validate:"required,gte=0" example:"0"`
}

type TreatmentIntervalResponse struct {
	ID   string `json:"id" example:"3fa85f64-5717-4562-b3fc-2c963f66afa6"`
	Days int    `json:"days" example:"0"`
	Timestamp
}

type TreatmentIntervalFilter struct {
	Pagination
}

func (input *TreatmentIntervalRequest) ToRepo() TreatmentInterval {
	return TreatmentInterval{
		Days: input.Days,
	}
}

func (data *TreatmentInterval) ToResponse() TreatmentIntervalResponse {
	return TreatmentIntervalResponse{
		ID:        data.ID,
		Days:      data.Days,
		Timestamp: data.Timestamp,
	}
}
