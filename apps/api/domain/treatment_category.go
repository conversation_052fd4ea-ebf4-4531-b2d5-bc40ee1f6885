package domain

type TreatmentCategory struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Timestamp
}

type TreatmentCategoryRequest struct {
	Name string `json:"name" validate:"required" example:"Facial"`
}

type TreatmentCategoryResponse struct {
	ID   string `json:"id" example:"3fa85f64-5717-4562-b3fc-2c963f66afa6"`
	Name string `json:"name" example:"Facial"`
	Timestamp
}

type TreatmentCategoryFilter struct {
	Pagination
}

func (input *TreatmentCategoryRequest) ToRepo() TreatmentCategory {
	return TreatmentCategory{
		Name: input.Name,
	}
}

func (data *TreatmentCategory) ToResponse() TreatmentCategoryResponse {
	return TreatmentCategoryResponse{
		ID:        data.ID,
		Name:      data.Name,
		Timestamp: data.Timestamp,
	}
}
