package domain

type RecommendationConcern struct {
	Name  string `json:"name"`
	Label string `json:"label"`
	Score int    `json:"score"`
}

type RecommendationTreatmentMLRequest struct {
	SkinAnalyze   SkinAnalyze             `json:"skin_analyze"`
	TopConcern    []RecommendationConcern `json:"top_concern"`
	ConcernAnswer ConcernAnswer           `json:"concern_answer"`
	ConcernGroups []*ConcernGroup         `json:"concern_groups"`
	Treatments    *[]TreatmentProductGetMany            `json:"treatments"`
}

type RecommendationTreatmentMLResponse struct {
	Text  string      `json:"text"`
	Video string      `json:"video"`
	Data  []Treatment `json:"data"`
}


type GetRecommendationMLRequest struct {
	TopConcern    []string                `json:"top_concern"`
	Treatments    []TreatmentProductGetMany           `json:"treatments"`
	UserSurvey    *UserSurvey          `json:"user_survey"`
}

type RecommendedTreatment struct {
	Name          string   `json:"name"`
    Description   string   `json:"description"`
    Categories    []string `json:"categories"`
    Price         int64      `json:"price"`
    Quantity      int      `json:"quantity"`
    SolvedConcerns []string `json:"solved_concerns"`
    IsTopRecommendation bool `json:"is_top_recommendation"`
}

type RecommendationResponse struct {
    Summary    string `json:"summary"`
    Treatments []RecommendedTreatment `json:"treatments"`
}
