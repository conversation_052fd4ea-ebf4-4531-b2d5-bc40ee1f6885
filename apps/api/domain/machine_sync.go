package domain

import (
	"encoding/json"
)

type MachineSyncLog struct {
	ID        string          `json:"id"`
	Data      json.RawMessage `json:"data" swaggertype:"object"`
	CreatedAt int64           `json:"created_at"`
}

type MachineSyncLogStatCount struct {
	SuccessCount uint32 `json:"success_count"`
	ErrorCount   uint32 `json:"error_count"`
}

type CreateMachineSyncLog struct {
	Data json.RawMessage `json:"data" swaggertype:"object"`
}

func (m *CreateMachineSyncLog) ToMachineSyncLog() MachineSyncLog {
	return MachineSyncLog{
		Data: m.Data,
	}
}

type MachineSyncLogFilter struct {
	Pagination
}
