// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewTreatmentCategoryRepository creates a new instance of TreatmentCategoryRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTreatmentCategoryRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TreatmentCategoryRepository {
	mock := &TreatmentCategoryRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// TreatmentCategoryRepository is an autogenerated mock type for the TreatmentCategoryRepository type
type TreatmentCategoryRepository struct {
	mock.Mock
}

type TreatmentCategoryRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *TreatmentCategoryRepository) EXPECT() *TreatmentCategoryRepository_Expecter {
	return &TreatmentCategoryRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type TreatmentCategoryRepository
func (_mock *TreatmentCategoryRepository) Create(ctx context.Context, data *domain.TreatmentCategory) error {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentCategory) error); ok {
		r0 = returnFunc(ctx, data)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// TreatmentCategoryRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type TreatmentCategoryRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *TreatmentCategoryRepository_Expecter) Create(ctx interface{}, data interface{}) *TreatmentCategoryRepository_Create_Call {
	return &TreatmentCategoryRepository_Create_Call{Call: _e.mock.On("Create", ctx, data)}
}

func (_c *TreatmentCategoryRepository_Create_Call) Run(run func(ctx context.Context, data *domain.TreatmentCategory)) *TreatmentCategoryRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentCategory))
	})
	return _c
}

func (_c *TreatmentCategoryRepository_Create_Call) Return(err error) *TreatmentCategoryRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *TreatmentCategoryRepository_Create_Call) RunAndReturn(run func(ctx context.Context, data *domain.TreatmentCategory) error) *TreatmentCategoryRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByID provides a mock function for the type TreatmentCategoryRepository
func (_mock *TreatmentCategoryRepository) DeleteByID(ctx context.Context, id *string) (*domain.TreatmentCategory, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByID")
	}

	var r0 *domain.TreatmentCategory
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.TreatmentCategory, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.TreatmentCategory); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentCategory)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentCategoryRepository_DeleteByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByID'
type TreatmentCategoryRepository_DeleteByID_Call struct {
	*mock.Call
}

// DeleteByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *TreatmentCategoryRepository_Expecter) DeleteByID(ctx interface{}, id interface{}) *TreatmentCategoryRepository_DeleteByID_Call {
	return &TreatmentCategoryRepository_DeleteByID_Call{Call: _e.mock.On("DeleteByID", ctx, id)}
}

func (_c *TreatmentCategoryRepository_DeleteByID_Call) Run(run func(ctx context.Context, id *string)) *TreatmentCategoryRepository_DeleteByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *TreatmentCategoryRepository_DeleteByID_Call) Return(treatmentCategory *domain.TreatmentCategory, err error) *TreatmentCategoryRepository_DeleteByID_Call {
	_c.Call.Return(treatmentCategory, err)
	return _c
}

func (_c *TreatmentCategoryRepository_DeleteByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.TreatmentCategory, error)) *TreatmentCategoryRepository_DeleteByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type TreatmentCategoryRepository
func (_mock *TreatmentCategoryRepository) GetByID(ctx context.Context, id *string) (*domain.TreatmentCategory, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.TreatmentCategory
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.TreatmentCategory, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.TreatmentCategory); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentCategory)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentCategoryRepository_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type TreatmentCategoryRepository_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *TreatmentCategoryRepository_Expecter) GetByID(ctx interface{}, id interface{}) *TreatmentCategoryRepository_GetByID_Call {
	return &TreatmentCategoryRepository_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *TreatmentCategoryRepository_GetByID_Call) Run(run func(ctx context.Context, id *string)) *TreatmentCategoryRepository_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *TreatmentCategoryRepository_GetByID_Call) Return(treatmentCategory *domain.TreatmentCategory, err error) *TreatmentCategoryRepository_GetByID_Call {
	_c.Call.Return(treatmentCategory, err)
	return _c
}

func (_c *TreatmentCategoryRepository_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.TreatmentCategory, error)) *TreatmentCategoryRepository_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type TreatmentCategoryRepository
func (_mock *TreatmentCategoryRepository) GetMany(ctx context.Context, filter *domain.TreatmentCategoryFilter) ([]domain.TreatmentCategory, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.TreatmentCategory
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentCategoryFilter) ([]domain.TreatmentCategory, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentCategoryFilter) []domain.TreatmentCategory); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.TreatmentCategory)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentCategoryFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.TreatmentCategoryFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// TreatmentCategoryRepository_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type TreatmentCategoryRepository_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *TreatmentCategoryRepository_Expecter) GetMany(ctx interface{}, filter interface{}) *TreatmentCategoryRepository_GetMany_Call {
	return &TreatmentCategoryRepository_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *TreatmentCategoryRepository_GetMany_Call) Run(run func(ctx context.Context, filter *domain.TreatmentCategoryFilter)) *TreatmentCategoryRepository_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentCategoryFilter))
	})
	return _c
}

func (_c *TreatmentCategoryRepository_GetMany_Call) Return(treatmentCategorys []domain.TreatmentCategory, n int, err error) *TreatmentCategoryRepository_GetMany_Call {
	_c.Call.Return(treatmentCategorys, n, err)
	return _c
}

func (_c *TreatmentCategoryRepository_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.TreatmentCategoryFilter) ([]domain.TreatmentCategory, int, error)) *TreatmentCategoryRepository_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type TreatmentCategoryRepository
func (_mock *TreatmentCategoryRepository) UpdateByID(ctx context.Context, existingData *domain.TreatmentCategory, newData *domain.TreatmentCategory) error {
	ret := _mock.Called(ctx, existingData, newData)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentCategory, *domain.TreatmentCategory) error); ok {
		r0 = returnFunc(ctx, existingData, newData)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// TreatmentCategoryRepository_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type TreatmentCategoryRepository_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - existingData
//   - newData
func (_e *TreatmentCategoryRepository_Expecter) UpdateByID(ctx interface{}, existingData interface{}, newData interface{}) *TreatmentCategoryRepository_UpdateByID_Call {
	return &TreatmentCategoryRepository_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, existingData, newData)}
}

func (_c *TreatmentCategoryRepository_UpdateByID_Call) Run(run func(ctx context.Context, existingData *domain.TreatmentCategory, newData *domain.TreatmentCategory)) *TreatmentCategoryRepository_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentCategory), args[2].(*domain.TreatmentCategory))
	})
	return _c
}

func (_c *TreatmentCategoryRepository_UpdateByID_Call) Return(err error) *TreatmentCategoryRepository_UpdateByID_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *TreatmentCategoryRepository_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, existingData *domain.TreatmentCategory, newData *domain.TreatmentCategory) error) *TreatmentCategoryRepository_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}
