// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewSkinAnalyzeRepository creates a new instance of SkinAnalyzeRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSkinAnalyzeRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *SkinAnalyzeRepository {
	mock := &SkinAnalyzeRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SkinAnalyzeRepository is an autogenerated mock type for the SkinAnalyzeRepository type
type SkinAnalyzeRepository struct {
	mock.Mock
}

type SkinAnalyzeRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *SkinAnalyzeRepository) EXPECT() *SkinAnalyzeRepository_Expecter {
	return &SkinAnalyzeRepository_Expecter{mock: &_m.Mock}
}

// CreateSkinAnalyze provides a mock function for the type SkinAnalyzeRepository
func (_mock *SkinAnalyzeRepository) CreateSkinAnalyze(ctx context.Context, skinAnalyze *domain.SkinAnalyze) (*domain.SkinAnalyze, error) {
	ret := _mock.Called(ctx, skinAnalyze)

	if len(ret) == 0 {
		panic("no return value specified for CreateSkinAnalyze")
	}

	var r0 *domain.SkinAnalyze
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinAnalyze) (*domain.SkinAnalyze, error)); ok {
		return returnFunc(ctx, skinAnalyze)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinAnalyze) *domain.SkinAnalyze); ok {
		r0 = returnFunc(ctx, skinAnalyze)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinAnalyze)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinAnalyze) error); ok {
		r1 = returnFunc(ctx, skinAnalyze)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinAnalyzeRepository_CreateSkinAnalyze_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateSkinAnalyze'
type SkinAnalyzeRepository_CreateSkinAnalyze_Call struct {
	*mock.Call
}

// CreateSkinAnalyze is a helper method to define mock.On call
//   - ctx
//   - skinAnalyze
func (_e *SkinAnalyzeRepository_Expecter) CreateSkinAnalyze(ctx interface{}, skinAnalyze interface{}) *SkinAnalyzeRepository_CreateSkinAnalyze_Call {
	return &SkinAnalyzeRepository_CreateSkinAnalyze_Call{Call: _e.mock.On("CreateSkinAnalyze", ctx, skinAnalyze)}
}

func (_c *SkinAnalyzeRepository_CreateSkinAnalyze_Call) Run(run func(ctx context.Context, skinAnalyze *domain.SkinAnalyze)) *SkinAnalyzeRepository_CreateSkinAnalyze_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinAnalyze))
	})
	return _c
}

func (_c *SkinAnalyzeRepository_CreateSkinAnalyze_Call) Return(skinAnalyze1 *domain.SkinAnalyze, err error) *SkinAnalyzeRepository_CreateSkinAnalyze_Call {
	_c.Call.Return(skinAnalyze1, err)
	return _c
}

func (_c *SkinAnalyzeRepository_CreateSkinAnalyze_Call) RunAndReturn(run func(ctx context.Context, skinAnalyze *domain.SkinAnalyze) (*domain.SkinAnalyze, error)) *SkinAnalyzeRepository_CreateSkinAnalyze_Call {
	_c.Call.Return(run)
	return _c
}

// GetManySkinAnalyzes provides a mock function for the type SkinAnalyzeRepository
func (_mock *SkinAnalyzeRepository) GetManySkinAnalyzes(ctx context.Context, filter *domain.SkinAnalyzeFilter) ([]*domain.SkinAnalyze, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetManySkinAnalyzes")
	}

	var r0 []*domain.SkinAnalyze
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinAnalyzeFilter) ([]*domain.SkinAnalyze, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinAnalyzeFilter) []*domain.SkinAnalyze); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*domain.SkinAnalyze)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinAnalyzeFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.SkinAnalyzeFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// SkinAnalyzeRepository_GetManySkinAnalyzes_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetManySkinAnalyzes'
type SkinAnalyzeRepository_GetManySkinAnalyzes_Call struct {
	*mock.Call
}

// GetManySkinAnalyzes is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *SkinAnalyzeRepository_Expecter) GetManySkinAnalyzes(ctx interface{}, filter interface{}) *SkinAnalyzeRepository_GetManySkinAnalyzes_Call {
	return &SkinAnalyzeRepository_GetManySkinAnalyzes_Call{Call: _e.mock.On("GetManySkinAnalyzes", ctx, filter)}
}

func (_c *SkinAnalyzeRepository_GetManySkinAnalyzes_Call) Run(run func(ctx context.Context, filter *domain.SkinAnalyzeFilter)) *SkinAnalyzeRepository_GetManySkinAnalyzes_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinAnalyzeFilter))
	})
	return _c
}

func (_c *SkinAnalyzeRepository_GetManySkinAnalyzes_Call) Return(skinAnalyzes []*domain.SkinAnalyze, n int, err error) *SkinAnalyzeRepository_GetManySkinAnalyzes_Call {
	_c.Call.Return(skinAnalyzes, n, err)
	return _c
}

func (_c *SkinAnalyzeRepository_GetManySkinAnalyzes_Call) RunAndReturn(run func(ctx context.Context, filter *domain.SkinAnalyzeFilter) ([]*domain.SkinAnalyze, int, error)) *SkinAnalyzeRepository_GetManySkinAnalyzes_Call {
	_c.Call.Return(run)
	return _c
}

// GetSkinAnalyzeByID provides a mock function for the type SkinAnalyzeRepository
func (_mock *SkinAnalyzeRepository) GetSkinAnalyzeByID(ctx context.Context, id string) (*domain.SkinAnalyze, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetSkinAnalyzeByID")
	}

	var r0 *domain.SkinAnalyze
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*domain.SkinAnalyze, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *domain.SkinAnalyze); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinAnalyze)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinAnalyzeRepository_GetSkinAnalyzeByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSkinAnalyzeByID'
type SkinAnalyzeRepository_GetSkinAnalyzeByID_Call struct {
	*mock.Call
}

// GetSkinAnalyzeByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SkinAnalyzeRepository_Expecter) GetSkinAnalyzeByID(ctx interface{}, id interface{}) *SkinAnalyzeRepository_GetSkinAnalyzeByID_Call {
	return &SkinAnalyzeRepository_GetSkinAnalyzeByID_Call{Call: _e.mock.On("GetSkinAnalyzeByID", ctx, id)}
}

func (_c *SkinAnalyzeRepository_GetSkinAnalyzeByID_Call) Run(run func(ctx context.Context, id string)) *SkinAnalyzeRepository_GetSkinAnalyzeByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *SkinAnalyzeRepository_GetSkinAnalyzeByID_Call) Return(skinAnalyze *domain.SkinAnalyze, err error) *SkinAnalyzeRepository_GetSkinAnalyzeByID_Call {
	_c.Call.Return(skinAnalyze, err)
	return _c
}

func (_c *SkinAnalyzeRepository_GetSkinAnalyzeByID_Call) RunAndReturn(run func(ctx context.Context, id string) (*domain.SkinAnalyze, error)) *SkinAnalyzeRepository_GetSkinAnalyzeByID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateSkinAnalyzeByID provides a mock function for the type SkinAnalyzeRepository
func (_mock *SkinAnalyzeRepository) UpdateSkinAnalyzeByID(ctx context.Context, id string, skinAnalyze *domain.SkinAnalyze) (*domain.SkinAnalyze, error) {
	ret := _mock.Called(ctx, id, skinAnalyze)

	if len(ret) == 0 {
		panic("no return value specified for UpdateSkinAnalyzeByID")
	}

	var r0 *domain.SkinAnalyze
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.SkinAnalyze) (*domain.SkinAnalyze, error)); ok {
		return returnFunc(ctx, id, skinAnalyze)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.SkinAnalyze) *domain.SkinAnalyze); ok {
		r0 = returnFunc(ctx, id, skinAnalyze)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinAnalyze)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *domain.SkinAnalyze) error); ok {
		r1 = returnFunc(ctx, id, skinAnalyze)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinAnalyzeRepository_UpdateSkinAnalyzeByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateSkinAnalyzeByID'
type SkinAnalyzeRepository_UpdateSkinAnalyzeByID_Call struct {
	*mock.Call
}

// UpdateSkinAnalyzeByID is a helper method to define mock.On call
//   - ctx
//   - id
//   - skinAnalyze
func (_e *SkinAnalyzeRepository_Expecter) UpdateSkinAnalyzeByID(ctx interface{}, id interface{}, skinAnalyze interface{}) *SkinAnalyzeRepository_UpdateSkinAnalyzeByID_Call {
	return &SkinAnalyzeRepository_UpdateSkinAnalyzeByID_Call{Call: _e.mock.On("UpdateSkinAnalyzeByID", ctx, id, skinAnalyze)}
}

func (_c *SkinAnalyzeRepository_UpdateSkinAnalyzeByID_Call) Run(run func(ctx context.Context, id string, skinAnalyze *domain.SkinAnalyze)) *SkinAnalyzeRepository_UpdateSkinAnalyzeByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*domain.SkinAnalyze))
	})
	return _c
}

func (_c *SkinAnalyzeRepository_UpdateSkinAnalyzeByID_Call) Return(skinAnalyze1 *domain.SkinAnalyze, err error) *SkinAnalyzeRepository_UpdateSkinAnalyzeByID_Call {
	_c.Call.Return(skinAnalyze1, err)
	return _c
}

func (_c *SkinAnalyzeRepository_UpdateSkinAnalyzeByID_Call) RunAndReturn(run func(ctx context.Context, id string, skinAnalyze *domain.SkinAnalyze) (*domain.SkinAnalyze, error)) *SkinAnalyzeRepository_UpdateSkinAnalyzeByID_Call {
	_c.Call.Return(run)
	return _c
}
