// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"context"

	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	mock "github.com/stretchr/testify/mock"
)

// NewPresigner creates a new instance of Presigner. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPresigner(t interface {
	mock.TestingT
	Cleanup(func())
}) *Presigner {
	mock := &Presigner{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Presigner is an autogenerated mock type for the Presigner type
type Presigner struct {
	mock.Mock
}

type Presigner_Expecter struct {
	mock *mock.Mock
}

func (_m *Presigner) EXPECT() *Presigner_Expecter {
	return &Presigner_Expecter{mock: &_m.Mock}
}

// GetObject provides a mock function for the type Presigner
func (_mock *Presigner) GetObject(ctx context.Context, bucketName string, objectKey string, lifetimeSecs int64) (*v4.PresignedHTTPRequest, error) {
	ret := _mock.Called(ctx, bucketName, objectKey, lifetimeSecs)

	if len(ret) == 0 {
		panic("no return value specified for GetObject")
	}

	var r0 *v4.PresignedHTTPRequest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, int64) (*v4.PresignedHTTPRequest, error)); ok {
		return returnFunc(ctx, bucketName, objectKey, lifetimeSecs)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, int64) *v4.PresignedHTTPRequest); ok {
		r0 = returnFunc(ctx, bucketName, objectKey, lifetimeSecs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v4.PresignedHTTPRequest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string, int64) error); ok {
		r1 = returnFunc(ctx, bucketName, objectKey, lifetimeSecs)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Presigner_GetObject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetObject'
type Presigner_GetObject_Call struct {
	*mock.Call
}

// GetObject is a helper method to define mock.On call
//   - ctx
//   - bucketName
//   - objectKey
//   - lifetimeSecs
func (_e *Presigner_Expecter) GetObject(ctx interface{}, bucketName interface{}, objectKey interface{}, lifetimeSecs interface{}) *Presigner_GetObject_Call {
	return &Presigner_GetObject_Call{Call: _e.mock.On("GetObject", ctx, bucketName, objectKey, lifetimeSecs)}
}

func (_c *Presigner_GetObject_Call) Run(run func(ctx context.Context, bucketName string, objectKey string, lifetimeSecs int64)) *Presigner_GetObject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(int64))
	})
	return _c
}

func (_c *Presigner_GetObject_Call) Return(presignedHTTPRequest *v4.PresignedHTTPRequest, err error) *Presigner_GetObject_Call {
	_c.Call.Return(presignedHTTPRequest, err)
	return _c
}

func (_c *Presigner_GetObject_Call) RunAndReturn(run func(ctx context.Context, bucketName string, objectKey string, lifetimeSecs int64) (*v4.PresignedHTTPRequest, error)) *Presigner_GetObject_Call {
	_c.Call.Return(run)
	return _c
}

// PutObject provides a mock function for the type Presigner
func (_mock *Presigner) PutObject(ctx context.Context, bucketName string, objectKey string, lifetimeSecs int64) (*v4.PresignedHTTPRequest, error) {
	ret := _mock.Called(ctx, bucketName, objectKey, lifetimeSecs)

	if len(ret) == 0 {
		panic("no return value specified for PutObject")
	}

	var r0 *v4.PresignedHTTPRequest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, int64) (*v4.PresignedHTTPRequest, error)); ok {
		return returnFunc(ctx, bucketName, objectKey, lifetimeSecs)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, int64) *v4.PresignedHTTPRequest); ok {
		r0 = returnFunc(ctx, bucketName, objectKey, lifetimeSecs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v4.PresignedHTTPRequest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string, int64) error); ok {
		r1 = returnFunc(ctx, bucketName, objectKey, lifetimeSecs)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Presigner_PutObject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PutObject'
type Presigner_PutObject_Call struct {
	*mock.Call
}

// PutObject is a helper method to define mock.On call
//   - ctx
//   - bucketName
//   - objectKey
//   - lifetimeSecs
func (_e *Presigner_Expecter) PutObject(ctx interface{}, bucketName interface{}, objectKey interface{}, lifetimeSecs interface{}) *Presigner_PutObject_Call {
	return &Presigner_PutObject_Call{Call: _e.mock.On("PutObject", ctx, bucketName, objectKey, lifetimeSecs)}
}

func (_c *Presigner_PutObject_Call) Run(run func(ctx context.Context, bucketName string, objectKey string, lifetimeSecs int64)) *Presigner_PutObject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(int64))
	})
	return _c
}

func (_c *Presigner_PutObject_Call) Return(presignedHTTPRequest *v4.PresignedHTTPRequest, err error) *Presigner_PutObject_Call {
	_c.Call.Return(presignedHTTPRequest, err)
	return _c
}

func (_c *Presigner_PutObject_Call) RunAndReturn(run func(ctx context.Context, bucketName string, objectKey string, lifetimeSecs int64) (*v4.PresignedHTTPRequest, error)) *Presigner_PutObject_Call {
	_c.Call.Return(run)
	return _c
}
