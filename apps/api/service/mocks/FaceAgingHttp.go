// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewFaceAgingHttp creates a new instance of FaceAgingHttp. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewFaceAgingHttp(t interface {
	mock.TestingT
	Cleanup(func())
}) *FaceAgingHttp {
	mock := &FaceAgingHttp{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// FaceAgingHttp is an autogenerated mock type for the FaceAgingHttp type
type FaceAgingHttp struct {
	mock.Mock
}

type FaceAgingHttp_Expecter struct {
	mock *mock.Mock
}

func (_m *FaceAgingHttp) EXPECT() *FaceAgingHttp_Expecter {
	return &FaceAgingHttp_Expecter{mock: &_m.Mock}
}

// FaceAgingWithConcern provides a mock function for the type FaceAgingHttp
func (_mock *FaceAgingHttp) FaceAgingWithConcern(ctx context.Context, data *domain.FaceAgingConcernMLRequest) (*domain.FaceAgingConcernMLResponse, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for FaceAgingWithConcern")
	}

	var r0 *domain.FaceAgingConcernMLResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.FaceAgingConcernMLRequest) (*domain.FaceAgingConcernMLResponse, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.FaceAgingConcernMLRequest) *domain.FaceAgingConcernMLResponse); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.FaceAgingConcernMLResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.FaceAgingConcernMLRequest) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// FaceAgingHttp_FaceAgingWithConcern_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FaceAgingWithConcern'
type FaceAgingHttp_FaceAgingWithConcern_Call struct {
	*mock.Call
}

// FaceAgingWithConcern is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *FaceAgingHttp_Expecter) FaceAgingWithConcern(ctx interface{}, data interface{}) *FaceAgingHttp_FaceAgingWithConcern_Call {
	return &FaceAgingHttp_FaceAgingWithConcern_Call{Call: _e.mock.On("FaceAgingWithConcern", ctx, data)}
}

func (_c *FaceAgingHttp_FaceAgingWithConcern_Call) Run(run func(ctx context.Context, data *domain.FaceAgingConcernMLRequest)) *FaceAgingHttp_FaceAgingWithConcern_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.FaceAgingConcernMLRequest))
	})
	return _c
}

func (_c *FaceAgingHttp_FaceAgingWithConcern_Call) Return(faceAgingConcernMLResponse *domain.FaceAgingConcernMLResponse, err error) *FaceAgingHttp_FaceAgingWithConcern_Call {
	_c.Call.Return(faceAgingConcernMLResponse, err)
	return _c
}

func (_c *FaceAgingHttp_FaceAgingWithConcern_Call) RunAndReturn(run func(ctx context.Context, data *domain.FaceAgingConcernMLRequest) (*domain.FaceAgingConcernMLResponse, error)) *FaceAgingHttp_FaceAgingWithConcern_Call {
	_c.Call.Return(run)
	return _c
}
