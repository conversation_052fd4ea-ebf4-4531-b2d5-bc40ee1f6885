// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewUserRepository creates a new instance of UserRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserRepository {
	mock := &UserRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// UserRepository is an autogenerated mock type for the UserRepository type
type UserRepository struct {
	mock.Mock
}

type UserRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *UserRepository) EXPECT() *UserRepository_Expecter {
	return &UserRepository_Expecter{mock: &_m.<PERSON>ck}
}

// Create provides a mock function for the type UserRepository
func (_mock *UserRepository) Create(ctx context.Context, user *domain.User) (*domain.User, error) {
	ret := _mock.Called(ctx, user)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *domain.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.User) (*domain.User, error)); ok {
		return returnFunc(ctx, user)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.User) *domain.User); ok {
		r0 = returnFunc(ctx, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.User) error); ok {
		r1 = returnFunc(ctx, user)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// UserRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type UserRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - user
func (_e *UserRepository_Expecter) Create(ctx interface{}, user interface{}) *UserRepository_Create_Call {
	return &UserRepository_Create_Call{Call: _e.mock.On("Create", ctx, user)}
}

func (_c *UserRepository_Create_Call) Run(run func(ctx context.Context, user *domain.User)) *UserRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.User))
	})
	return _c
}

func (_c *UserRepository_Create_Call) Return(user1 *domain.User, err error) *UserRepository_Create_Call {
	_c.Call.Return(user1, err)
	return _c
}

func (_c *UserRepository_Create_Call) RunAndReturn(run func(ctx context.Context, user *domain.User) (*domain.User, error)) *UserRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByID provides a mock function for the type UserRepository
func (_mock *UserRepository) DeleteByID(ctx context.Context, id *string) (*domain.User, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByID")
	}

	var r0 *domain.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.User, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.User); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// UserRepository_DeleteByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByID'
type UserRepository_DeleteByID_Call struct {
	*mock.Call
}

// DeleteByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *UserRepository_Expecter) DeleteByID(ctx interface{}, id interface{}) *UserRepository_DeleteByID_Call {
	return &UserRepository_DeleteByID_Call{Call: _e.mock.On("DeleteByID", ctx, id)}
}

func (_c *UserRepository_DeleteByID_Call) Run(run func(ctx context.Context, id *string)) *UserRepository_DeleteByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *UserRepository_DeleteByID_Call) Return(user *domain.User, err error) *UserRepository_DeleteByID_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *UserRepository_DeleteByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.User, error)) *UserRepository_DeleteByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type UserRepository
func (_mock *UserRepository) GetByID(ctx context.Context, id *string) (*domain.User, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.User, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.User); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// UserRepository_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type UserRepository_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *UserRepository_Expecter) GetByID(ctx interface{}, id interface{}) *UserRepository_GetByID_Call {
	return &UserRepository_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *UserRepository_GetByID_Call) Run(run func(ctx context.Context, id *string)) *UserRepository_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *UserRepository_GetByID_Call) Return(user *domain.User, err error) *UserRepository_GetByID_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *UserRepository_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.User, error)) *UserRepository_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type UserRepository
func (_mock *UserRepository) GetMany(ctx context.Context, filter *domain.UserFilter) ([]*domain.User, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []*domain.User
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.UserFilter) ([]*domain.User, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.UserFilter) []*domain.User); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*domain.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.UserFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.UserFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// UserRepository_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type UserRepository_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *UserRepository_Expecter) GetMany(ctx interface{}, filter interface{}) *UserRepository_GetMany_Call {
	return &UserRepository_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *UserRepository_GetMany_Call) Run(run func(ctx context.Context, filter *domain.UserFilter)) *UserRepository_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.UserFilter))
	})
	return _c
}

func (_c *UserRepository_GetMany_Call) Return(users []*domain.User, n int, err error) *UserRepository_GetMany_Call {
	_c.Call.Return(users, n, err)
	return _c
}

func (_c *UserRepository_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.UserFilter) ([]*domain.User, int, error)) *UserRepository_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type UserRepository
func (_mock *UserRepository) UpdateByID(ctx context.Context, existingData *domain.User, newData *domain.User) (*domain.User, error) {
	ret := _mock.Called(ctx, existingData, newData)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 *domain.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.User, *domain.User) (*domain.User, error)); ok {
		return returnFunc(ctx, existingData, newData)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.User, *domain.User) *domain.User); ok {
		r0 = returnFunc(ctx, existingData, newData)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.User, *domain.User) error); ok {
		r1 = returnFunc(ctx, existingData, newData)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// UserRepository_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type UserRepository_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - existingData
//   - newData
func (_e *UserRepository_Expecter) UpdateByID(ctx interface{}, existingData interface{}, newData interface{}) *UserRepository_UpdateByID_Call {
	return &UserRepository_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, existingData, newData)}
}

func (_c *UserRepository_UpdateByID_Call) Run(run func(ctx context.Context, existingData *domain.User, newData *domain.User)) *UserRepository_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.User), args[2].(*domain.User))
	})
	return _c
}

func (_c *UserRepository_UpdateByID_Call) Return(user *domain.User, err error) *UserRepository_UpdateByID_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *UserRepository_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, existingData *domain.User, newData *domain.User) (*domain.User, error)) *UserRepository_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePassword provides a mock function for the type UserRepository
func (_mock *UserRepository) UpdatePassword(ctx context.Context, id *string, password *string) error {
	ret := _mock.Called(ctx, id, password)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePassword")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string) error); ok {
		r0 = returnFunc(ctx, id, password)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// UserRepository_UpdatePassword_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePassword'
type UserRepository_UpdatePassword_Call struct {
	*mock.Call
}

// UpdatePassword is a helper method to define mock.On call
//   - ctx
//   - id
//   - password
func (_e *UserRepository_Expecter) UpdatePassword(ctx interface{}, id interface{}, password interface{}) *UserRepository_UpdatePassword_Call {
	return &UserRepository_UpdatePassword_Call{Call: _e.mock.On("UpdatePassword", ctx, id, password)}
}

func (_c *UserRepository_UpdatePassword_Call) Run(run func(ctx context.Context, id *string, password *string)) *UserRepository_UpdatePassword_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*string))
	})
	return _c
}

func (_c *UserRepository_UpdatePassword_Call) Return(err error) *UserRepository_UpdatePassword_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *UserRepository_UpdatePassword_Call) RunAndReturn(run func(ctx context.Context, id *string, password *string) error) *UserRepository_UpdatePassword_Call {
	_c.Call.Return(run)
	return _c
}
