// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"context"
	"io"

	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	mock "github.com/stretchr/testify/mock"
)

// NewUploader creates a new instance of Uploader. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUploader(t interface {
	mock.TestingT
	Cleanup(func())
}) *Uploader {
	mock := &Uploader{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Uploader is an autogenerated mock type for the Uploader type
type Uploader struct {
	mock.Mock
}

type Uploader_Expecter struct {
	mock *mock.Mock
}

func (_m *Uploader) EXPECT() *Uploader_Expecter {
	return &Uploader_Expecter{mock: &_m.<PERSON>ck}
}

// UploadS3Object provides a mock function for the type Uploader
func (_mock *Uploader) UploadS3Object(ctx context.Context, bucketName string, objectKey string, buffer io.Reader) (*manager.UploadOutput, error) {
	ret := _mock.Called(ctx, bucketName, objectKey, buffer)

	if len(ret) == 0 {
		panic("no return value specified for UploadS3Object")
	}

	var r0 *manager.UploadOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, io.Reader) (*manager.UploadOutput, error)); ok {
		return returnFunc(ctx, bucketName, objectKey, buffer)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, io.Reader) *manager.UploadOutput); ok {
		r0 = returnFunc(ctx, bucketName, objectKey, buffer)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*manager.UploadOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string, io.Reader) error); ok {
		r1 = returnFunc(ctx, bucketName, objectKey, buffer)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Uploader_UploadS3Object_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadS3Object'
type Uploader_UploadS3Object_Call struct {
	*mock.Call
}

// UploadS3Object is a helper method to define mock.On call
//   - ctx
//   - bucketName
//   - objectKey
//   - buffer
func (_e *Uploader_Expecter) UploadS3Object(ctx interface{}, bucketName interface{}, objectKey interface{}, buffer interface{}) *Uploader_UploadS3Object_Call {
	return &Uploader_UploadS3Object_Call{Call: _e.mock.On("UploadS3Object", ctx, bucketName, objectKey, buffer)}
}

func (_c *Uploader_UploadS3Object_Call) Run(run func(ctx context.Context, bucketName string, objectKey string, buffer io.Reader)) *Uploader_UploadS3Object_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(io.Reader))
	})
	return _c
}

func (_c *Uploader_UploadS3Object_Call) Return(uploadOutput *manager.UploadOutput, err error) *Uploader_UploadS3Object_Call {
	_c.Call.Return(uploadOutput, err)
	return _c
}

func (_c *Uploader_UploadS3Object_Call) RunAndReturn(run func(ctx context.Context, bucketName string, objectKey string, buffer io.Reader) (*manager.UploadOutput, error)) *Uploader_UploadS3Object_Call {
	_c.Call.Return(run)
	return _c
}
