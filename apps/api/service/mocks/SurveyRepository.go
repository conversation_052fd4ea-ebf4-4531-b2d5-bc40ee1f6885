// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewSurveyRepository creates a new instance of SurveyRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSurveyRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *SurveyRepository {
	mock := &SurveyRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SurveyRepository is an autogenerated mock type for the SurveyRepository type
type SurveyRepository struct {
	mock.Mock
}

type SurveyRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *SurveyRepository) EXPECT() *SurveyRepository_Expecter {
	return &SurveyRepository_Expecter{mock: &_m.<PERSON>ck}
}

// C<PERSON> provides a mock function for the type SurveyRepository
func (_mock *SurveyRepository) Create(ctx context.Context, data *domain.Survey) (*domain.Survey, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *domain.Survey
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.Survey) (*domain.Survey, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.Survey) *domain.Survey); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Survey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.Survey) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type SurveyRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *SurveyRepository_Expecter) Create(ctx interface{}, data interface{}) *SurveyRepository_Create_Call {
	return &SurveyRepository_Create_Call{Call: _e.mock.On("Create", ctx, data)}
}

func (_c *SurveyRepository_Create_Call) Run(run func(ctx context.Context, data *domain.Survey)) *SurveyRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.Survey))
	})
	return _c
}

func (_c *SurveyRepository_Create_Call) Return(survey *domain.Survey, err error) *SurveyRepository_Create_Call {
	_c.Call.Return(survey, err)
	return _c
}

func (_c *SurveyRepository_Create_Call) RunAndReturn(run func(ctx context.Context, data *domain.Survey) (*domain.Survey, error)) *SurveyRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// CreateNested provides a mock function for the type SurveyRepository
func (_mock *SurveyRepository) CreateNested(ctx context.Context, data *domain.SurveyRequestNested) (*domain.SurveyResponseNested, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for CreateNested")
	}

	var r0 *domain.SurveyResponseNested
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SurveyRequestNested) (*domain.SurveyResponseNested, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SurveyRequestNested) *domain.SurveyResponseNested); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SurveyResponseNested)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SurveyRequestNested) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyRepository_CreateNested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNested'
type SurveyRepository_CreateNested_Call struct {
	*mock.Call
}

// CreateNested is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *SurveyRepository_Expecter) CreateNested(ctx interface{}, data interface{}) *SurveyRepository_CreateNested_Call {
	return &SurveyRepository_CreateNested_Call{Call: _e.mock.On("CreateNested", ctx, data)}
}

func (_c *SurveyRepository_CreateNested_Call) Run(run func(ctx context.Context, data *domain.SurveyRequestNested)) *SurveyRepository_CreateNested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SurveyRequestNested))
	})
	return _c
}

func (_c *SurveyRepository_CreateNested_Call) Return(surveyResponseNested *domain.SurveyResponseNested, err error) *SurveyRepository_CreateNested_Call {
	_c.Call.Return(surveyResponseNested, err)
	return _c
}

func (_c *SurveyRepository_CreateNested_Call) RunAndReturn(run func(ctx context.Context, data *domain.SurveyRequestNested) (*domain.SurveyResponseNested, error)) *SurveyRepository_CreateNested_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByID provides a mock function for the type SurveyRepository
func (_mock *SurveyRepository) DeleteByID(ctx context.Context, id *string) (*domain.Survey, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByID")
	}

	var r0 *domain.Survey
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.Survey, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.Survey); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Survey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyRepository_DeleteByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByID'
type SurveyRepository_DeleteByID_Call struct {
	*mock.Call
}

// DeleteByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SurveyRepository_Expecter) DeleteByID(ctx interface{}, id interface{}) *SurveyRepository_DeleteByID_Call {
	return &SurveyRepository_DeleteByID_Call{Call: _e.mock.On("DeleteByID", ctx, id)}
}

func (_c *SurveyRepository_DeleteByID_Call) Run(run func(ctx context.Context, id *string)) *SurveyRepository_DeleteByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *SurveyRepository_DeleteByID_Call) Return(survey *domain.Survey, err error) *SurveyRepository_DeleteByID_Call {
	_c.Call.Return(survey, err)
	return _c
}

func (_c *SurveyRepository_DeleteByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.Survey, error)) *SurveyRepository_DeleteByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type SurveyRepository
func (_mock *SurveyRepository) GetByID(ctx context.Context, id *string) (*domain.Survey, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.Survey
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.Survey, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.Survey); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Survey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyRepository_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type SurveyRepository_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SurveyRepository_Expecter) GetByID(ctx interface{}, id interface{}) *SurveyRepository_GetByID_Call {
	return &SurveyRepository_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *SurveyRepository_GetByID_Call) Run(run func(ctx context.Context, id *string)) *SurveyRepository_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *SurveyRepository_GetByID_Call) Return(survey *domain.Survey, err error) *SurveyRepository_GetByID_Call {
	_c.Call.Return(survey, err)
	return _c
}

func (_c *SurveyRepository_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.Survey, error)) *SurveyRepository_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByIDNested provides a mock function for the type SurveyRepository
func (_mock *SurveyRepository) GetByIDNested(ctx context.Context, id *string) (*domain.SurveyResponseNested, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByIDNested")
	}

	var r0 *domain.SurveyResponseNested
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.SurveyResponseNested, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.SurveyResponseNested); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SurveyResponseNested)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyRepository_GetByIDNested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByIDNested'
type SurveyRepository_GetByIDNested_Call struct {
	*mock.Call
}

// GetByIDNested is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SurveyRepository_Expecter) GetByIDNested(ctx interface{}, id interface{}) *SurveyRepository_GetByIDNested_Call {
	return &SurveyRepository_GetByIDNested_Call{Call: _e.mock.On("GetByIDNested", ctx, id)}
}

func (_c *SurveyRepository_GetByIDNested_Call) Run(run func(ctx context.Context, id *string)) *SurveyRepository_GetByIDNested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *SurveyRepository_GetByIDNested_Call) Return(surveyResponseNested *domain.SurveyResponseNested, err error) *SurveyRepository_GetByIDNested_Call {
	_c.Call.Return(surveyResponseNested, err)
	return _c
}

func (_c *SurveyRepository_GetByIDNested_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.SurveyResponseNested, error)) *SurveyRepository_GetByIDNested_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type SurveyRepository
func (_mock *SurveyRepository) GetMany(ctx context.Context, filter *domain.SurveyFilter) ([]domain.Survey, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.Survey
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SurveyFilter) ([]domain.Survey, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SurveyFilter) []domain.Survey); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Survey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SurveyFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.SurveyFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// SurveyRepository_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type SurveyRepository_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *SurveyRepository_Expecter) GetMany(ctx interface{}, filter interface{}) *SurveyRepository_GetMany_Call {
	return &SurveyRepository_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *SurveyRepository_GetMany_Call) Run(run func(ctx context.Context, filter *domain.SurveyFilter)) *SurveyRepository_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SurveyFilter))
	})
	return _c
}

func (_c *SurveyRepository_GetMany_Call) Return(surveys []domain.Survey, n int, err error) *SurveyRepository_GetMany_Call {
	_c.Call.Return(surveys, n, err)
	return _c
}

func (_c *SurveyRepository_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.SurveyFilter) ([]domain.Survey, int, error)) *SurveyRepository_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// GetManyNested provides a mock function for the type SurveyRepository
func (_mock *SurveyRepository) GetManyNested(ctx context.Context, filter *domain.SurveyFilterNested) ([]domain.SurveyResponseNested, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetManyNested")
	}

	var r0 []domain.SurveyResponseNested
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SurveyFilterNested) ([]domain.SurveyResponseNested, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SurveyFilterNested) []domain.SurveyResponseNested); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.SurveyResponseNested)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SurveyFilterNested) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.SurveyFilterNested) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// SurveyRepository_GetManyNested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetManyNested'
type SurveyRepository_GetManyNested_Call struct {
	*mock.Call
}

// GetManyNested is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *SurveyRepository_Expecter) GetManyNested(ctx interface{}, filter interface{}) *SurveyRepository_GetManyNested_Call {
	return &SurveyRepository_GetManyNested_Call{Call: _e.mock.On("GetManyNested", ctx, filter)}
}

func (_c *SurveyRepository_GetManyNested_Call) Run(run func(ctx context.Context, filter *domain.SurveyFilterNested)) *SurveyRepository_GetManyNested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SurveyFilterNested))
	})
	return _c
}

func (_c *SurveyRepository_GetManyNested_Call) Return(surveyResponseNesteds []domain.SurveyResponseNested, n int, err error) *SurveyRepository_GetManyNested_Call {
	_c.Call.Return(surveyResponseNesteds, n, err)
	return _c
}

func (_c *SurveyRepository_GetManyNested_Call) RunAndReturn(run func(ctx context.Context, filter *domain.SurveyFilterNested) ([]domain.SurveyResponseNested, int, error)) *SurveyRepository_GetManyNested_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type SurveyRepository
func (_mock *SurveyRepository) UpdateByID(ctx context.Context, existingData *domain.Survey, newData *domain.Survey) (*domain.Survey, error) {
	ret := _mock.Called(ctx, existingData, newData)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 *domain.Survey
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.Survey, *domain.Survey) (*domain.Survey, error)); ok {
		return returnFunc(ctx, existingData, newData)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.Survey, *domain.Survey) *domain.Survey); ok {
		r0 = returnFunc(ctx, existingData, newData)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Survey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.Survey, *domain.Survey) error); ok {
		r1 = returnFunc(ctx, existingData, newData)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyRepository_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type SurveyRepository_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - existingData
//   - newData
func (_e *SurveyRepository_Expecter) UpdateByID(ctx interface{}, existingData interface{}, newData interface{}) *SurveyRepository_UpdateByID_Call {
	return &SurveyRepository_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, existingData, newData)}
}

func (_c *SurveyRepository_UpdateByID_Call) Run(run func(ctx context.Context, existingData *domain.Survey, newData *domain.Survey)) *SurveyRepository_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.Survey), args[2].(*domain.Survey))
	})
	return _c
}

func (_c *SurveyRepository_UpdateByID_Call) Return(survey *domain.Survey, err error) *SurveyRepository_UpdateByID_Call {
	_c.Call.Return(survey, err)
	return _c
}

func (_c *SurveyRepository_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, existingData *domain.Survey, newData *domain.Survey) (*domain.Survey, error)) *SurveyRepository_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByIDNested provides a mock function for the type SurveyRepository
func (_mock *SurveyRepository) UpdateByIDNested(ctx context.Context, id *string, data *domain.SurveyRequestNested) (*domain.SurveyResponseNested, error) {
	ret := _mock.Called(ctx, id, data)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByIDNested")
	}

	var r0 *domain.SurveyResponseNested
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.SurveyRequestNested) (*domain.SurveyResponseNested, error)); ok {
		return returnFunc(ctx, id, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.SurveyRequestNested) *domain.SurveyResponseNested); ok {
		r0 = returnFunc(ctx, id, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SurveyResponseNested)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *domain.SurveyRequestNested) error); ok {
		r1 = returnFunc(ctx, id, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SurveyRepository_UpdateByIDNested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByIDNested'
type SurveyRepository_UpdateByIDNested_Call struct {
	*mock.Call
}

// UpdateByIDNested is a helper method to define mock.On call
//   - ctx
//   - id
//   - data
func (_e *SurveyRepository_Expecter) UpdateByIDNested(ctx interface{}, id interface{}, data interface{}) *SurveyRepository_UpdateByIDNested_Call {
	return &SurveyRepository_UpdateByIDNested_Call{Call: _e.mock.On("UpdateByIDNested", ctx, id, data)}
}

func (_c *SurveyRepository_UpdateByIDNested_Call) Run(run func(ctx context.Context, id *string, data *domain.SurveyRequestNested)) *SurveyRepository_UpdateByIDNested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*domain.SurveyRequestNested))
	})
	return _c
}

func (_c *SurveyRepository_UpdateByIDNested_Call) Return(surveyResponseNested *domain.SurveyResponseNested, err error) *SurveyRepository_UpdateByIDNested_Call {
	_c.Call.Return(surveyResponseNested, err)
	return _c
}

func (_c *SurveyRepository_UpdateByIDNested_Call) RunAndReturn(run func(ctx context.Context, id *string, data *domain.SurveyRequestNested) (*domain.SurveyResponseNested, error)) *SurveyRepository_UpdateByIDNested_Call {
	_c.Call.Return(run)
	return _c
}
