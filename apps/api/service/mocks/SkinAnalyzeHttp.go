// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewSkinAnalyzeHttp creates a new instance of SkinAnalyzeHttp. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSkinAnalyzeHttp(t interface {
	mock.TestingT
	Cleanup(func())
}) *SkinAnalyzeHttp {
	mock := &SkinAnalyzeHttp{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SkinAnalyzeHttp is an autogenerated mock type for the SkinAnalyzeHttp type
type SkinAnalyzeHttp struct {
	mock.Mock
}

type SkinAnalyzeHttp_Expecter struct {
	mock *mock.Mock
}

func (_m *SkinAnalyzeHttp) EXPECT() *SkinAnalyzeHttp_Expecter {
	return &SkinAnalyzeHttp_Expecter{mock: &_m.Mock}
}

// UploadImage provides a mock function for the type SkinAnalyzeHttp
func (_mock *SkinAnalyzeHttp) UploadImage(ctx context.Context, data *domain.SkinAnalyzeUploadRequest) (*domain.SkinAnalyze, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for UploadImage")
	}

	var r0 *domain.SkinAnalyze
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinAnalyzeUploadRequest) (*domain.SkinAnalyze, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinAnalyzeUploadRequest) *domain.SkinAnalyze); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinAnalyze)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinAnalyzeUploadRequest) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinAnalyzeHttp_UploadImage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadImage'
type SkinAnalyzeHttp_UploadImage_Call struct {
	*mock.Call
}

// UploadImage is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *SkinAnalyzeHttp_Expecter) UploadImage(ctx interface{}, data interface{}) *SkinAnalyzeHttp_UploadImage_Call {
	return &SkinAnalyzeHttp_UploadImage_Call{Call: _e.mock.On("UploadImage", ctx, data)}
}

func (_c *SkinAnalyzeHttp_UploadImage_Call) Run(run func(ctx context.Context, data *domain.SkinAnalyzeUploadRequest)) *SkinAnalyzeHttp_UploadImage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinAnalyzeUploadRequest))
	})
	return _c
}

func (_c *SkinAnalyzeHttp_UploadImage_Call) Return(skinAnalyze *domain.SkinAnalyze, err error) *SkinAnalyzeHttp_UploadImage_Call {
	_c.Call.Return(skinAnalyze, err)
	return _c
}

func (_c *SkinAnalyzeHttp_UploadImage_Call) RunAndReturn(run func(ctx context.Context, data *domain.SkinAnalyzeUploadRequest) (*domain.SkinAnalyze, error)) *SkinAnalyzeHttp_UploadImage_Call {
	_c.Call.Return(run)
	return _c
}
