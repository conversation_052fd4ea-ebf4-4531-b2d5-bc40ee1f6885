// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewTreatmentIntervalRepository creates a new instance of TreatmentIntervalRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTreatmentIntervalRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TreatmentIntervalRepository {
	mock := &TreatmentIntervalRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// TreatmentIntervalRepository is an autogenerated mock type for the TreatmentIntervalRepository type
type TreatmentIntervalRepository struct {
	mock.Mock
}

type TreatmentIntervalRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *TreatmentIntervalRepository) EXPECT() *TreatmentIntervalRepository_Expecter {
	return &TreatmentIntervalRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type TreatmentIntervalRepository
func (_mock *TreatmentIntervalRepository) Create(ctx context.Context, data *domain.TreatmentInterval) error {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentInterval) error); ok {
		r0 = returnFunc(ctx, data)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// TreatmentIntervalRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type TreatmentIntervalRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *TreatmentIntervalRepository_Expecter) Create(ctx interface{}, data interface{}) *TreatmentIntervalRepository_Create_Call {
	return &TreatmentIntervalRepository_Create_Call{Call: _e.mock.On("Create", ctx, data)}
}

func (_c *TreatmentIntervalRepository_Create_Call) Run(run func(ctx context.Context, data *domain.TreatmentInterval)) *TreatmentIntervalRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentInterval))
	})
	return _c
}

func (_c *TreatmentIntervalRepository_Create_Call) Return(err error) *TreatmentIntervalRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *TreatmentIntervalRepository_Create_Call) RunAndReturn(run func(ctx context.Context, data *domain.TreatmentInterval) error) *TreatmentIntervalRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByID provides a mock function for the type TreatmentIntervalRepository
func (_mock *TreatmentIntervalRepository) DeleteByID(ctx context.Context, id *string) (*domain.TreatmentInterval, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByID")
	}

	var r0 *domain.TreatmentInterval
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.TreatmentInterval, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.TreatmentInterval); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentInterval)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentIntervalRepository_DeleteByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByID'
type TreatmentIntervalRepository_DeleteByID_Call struct {
	*mock.Call
}

// DeleteByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *TreatmentIntervalRepository_Expecter) DeleteByID(ctx interface{}, id interface{}) *TreatmentIntervalRepository_DeleteByID_Call {
	return &TreatmentIntervalRepository_DeleteByID_Call{Call: _e.mock.On("DeleteByID", ctx, id)}
}

func (_c *TreatmentIntervalRepository_DeleteByID_Call) Run(run func(ctx context.Context, id *string)) *TreatmentIntervalRepository_DeleteByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *TreatmentIntervalRepository_DeleteByID_Call) Return(treatmentInterval *domain.TreatmentInterval, err error) *TreatmentIntervalRepository_DeleteByID_Call {
	_c.Call.Return(treatmentInterval, err)
	return _c
}

func (_c *TreatmentIntervalRepository_DeleteByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.TreatmentInterval, error)) *TreatmentIntervalRepository_DeleteByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type TreatmentIntervalRepository
func (_mock *TreatmentIntervalRepository) GetByID(ctx context.Context, id *string) (*domain.TreatmentInterval, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.TreatmentInterval
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.TreatmentInterval, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.TreatmentInterval); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentInterval)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentIntervalRepository_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type TreatmentIntervalRepository_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *TreatmentIntervalRepository_Expecter) GetByID(ctx interface{}, id interface{}) *TreatmentIntervalRepository_GetByID_Call {
	return &TreatmentIntervalRepository_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *TreatmentIntervalRepository_GetByID_Call) Run(run func(ctx context.Context, id *string)) *TreatmentIntervalRepository_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *TreatmentIntervalRepository_GetByID_Call) Return(treatmentInterval *domain.TreatmentInterval, err error) *TreatmentIntervalRepository_GetByID_Call {
	_c.Call.Return(treatmentInterval, err)
	return _c
}

func (_c *TreatmentIntervalRepository_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.TreatmentInterval, error)) *TreatmentIntervalRepository_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type TreatmentIntervalRepository
func (_mock *TreatmentIntervalRepository) GetMany(ctx context.Context, filter *domain.TreatmentIntervalFilter) ([]domain.TreatmentInterval, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.TreatmentInterval
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentIntervalFilter) ([]domain.TreatmentInterval, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentIntervalFilter) []domain.TreatmentInterval); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.TreatmentInterval)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentIntervalFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.TreatmentIntervalFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// TreatmentIntervalRepository_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type TreatmentIntervalRepository_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *TreatmentIntervalRepository_Expecter) GetMany(ctx interface{}, filter interface{}) *TreatmentIntervalRepository_GetMany_Call {
	return &TreatmentIntervalRepository_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *TreatmentIntervalRepository_GetMany_Call) Run(run func(ctx context.Context, filter *domain.TreatmentIntervalFilter)) *TreatmentIntervalRepository_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentIntervalFilter))
	})
	return _c
}

func (_c *TreatmentIntervalRepository_GetMany_Call) Return(treatmentIntervals []domain.TreatmentInterval, n int, err error) *TreatmentIntervalRepository_GetMany_Call {
	_c.Call.Return(treatmentIntervals, n, err)
	return _c
}

func (_c *TreatmentIntervalRepository_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.TreatmentIntervalFilter) ([]domain.TreatmentInterval, int, error)) *TreatmentIntervalRepository_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type TreatmentIntervalRepository
func (_mock *TreatmentIntervalRepository) UpdateByID(ctx context.Context, existingData *domain.TreatmentInterval, newData *domain.TreatmentInterval) error {
	ret := _mock.Called(ctx, existingData, newData)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentInterval, *domain.TreatmentInterval) error); ok {
		r0 = returnFunc(ctx, existingData, newData)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// TreatmentIntervalRepository_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type TreatmentIntervalRepository_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - existingData
//   - newData
func (_e *TreatmentIntervalRepository_Expecter) UpdateByID(ctx interface{}, existingData interface{}, newData interface{}) *TreatmentIntervalRepository_UpdateByID_Call {
	return &TreatmentIntervalRepository_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, existingData, newData)}
}

func (_c *TreatmentIntervalRepository_UpdateByID_Call) Run(run func(ctx context.Context, existingData *domain.TreatmentInterval, newData *domain.TreatmentInterval)) *TreatmentIntervalRepository_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentInterval), args[2].(*domain.TreatmentInterval))
	})
	return _c
}

func (_c *TreatmentIntervalRepository_UpdateByID_Call) Return(err error) *TreatmentIntervalRepository_UpdateByID_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *TreatmentIntervalRepository_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, existingData *domain.TreatmentInterval, newData *domain.TreatmentInterval) error) *TreatmentIntervalRepository_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}
