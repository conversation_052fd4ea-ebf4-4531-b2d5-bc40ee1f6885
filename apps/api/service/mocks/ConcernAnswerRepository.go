// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewConcernAnswerRepository creates a new instance of ConcernAnswerRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewConcernAnswerRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *ConcernAnswerRepository {
	mock := &ConcernAnswerRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// ConcernAnswerRepository is an autogenerated mock type for the ConcernAnswerRepository type
type ConcernAnswerRepository struct {
	mock.Mock
}

type ConcernAnswerRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *ConcernAnswerRepository) EXPECT() *ConcernAnswerRepository_Expecter {
	return &ConcernAnswerRepository_Expecter{mock: &_m.Mock}
}

// GetConcernAnswerByKey provides a mock function for the type ConcernAnswerRepository
func (_mock *ConcernAnswerRepository) GetConcernAnswerByKey(ctx context.Context, key string) (*domain.ConcernAnswer, error) {
	ret := _mock.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for GetConcernAnswerByKey")
	}

	var r0 *domain.ConcernAnswer
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*domain.ConcernAnswer, error)); ok {
		return returnFunc(ctx, key)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *domain.ConcernAnswer); ok {
		r0 = returnFunc(ctx, key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.ConcernAnswer)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, key)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// ConcernAnswerRepository_GetConcernAnswerByKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetConcernAnswerByKey'
type ConcernAnswerRepository_GetConcernAnswerByKey_Call struct {
	*mock.Call
}

// GetConcernAnswerByKey is a helper method to define mock.On call
//   - ctx
//   - key
func (_e *ConcernAnswerRepository_Expecter) GetConcernAnswerByKey(ctx interface{}, key interface{}) *ConcernAnswerRepository_GetConcernAnswerByKey_Call {
	return &ConcernAnswerRepository_GetConcernAnswerByKey_Call{Call: _e.mock.On("GetConcernAnswerByKey", ctx, key)}
}

func (_c *ConcernAnswerRepository_GetConcernAnswerByKey_Call) Run(run func(ctx context.Context, key string)) *ConcernAnswerRepository_GetConcernAnswerByKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *ConcernAnswerRepository_GetConcernAnswerByKey_Call) Return(concernAnswer *domain.ConcernAnswer, err error) *ConcernAnswerRepository_GetConcernAnswerByKey_Call {
	_c.Call.Return(concernAnswer, err)
	return _c
}

func (_c *ConcernAnswerRepository_GetConcernAnswerByKey_Call) RunAndReturn(run func(ctx context.Context, key string) (*domain.ConcernAnswer, error)) *ConcernAnswerRepository_GetConcernAnswerByKey_Call {
	_c.Call.Return(run)
	return _c
}
