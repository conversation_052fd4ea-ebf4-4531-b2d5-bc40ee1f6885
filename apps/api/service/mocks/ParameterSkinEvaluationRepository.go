// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewParameterSkinEvaluationRepository creates a new instance of ParameterSkinEvaluationRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewParameterSkinEvaluationRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *ParameterSkinEvaluationRepository {
	mock := &ParameterSkinEvaluationRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// ParameterSkinEvaluationRepository is an autogenerated mock type for the ParameterSkinEvaluationRepository type
type ParameterSkinEvaluationRepository struct {
	mock.Mock
}

type ParameterSkinEvaluationRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *ParameterSkinEvaluationRepository) EXPECT() *ParameterSkinEvaluationRepository_Expecter {
	return &ParameterSkinEvaluationRepository_Expecter{mock: &_m.Mock}
}

// GetByID provides a mock function for the type ParameterSkinEvaluationRepository
func (_mock *ParameterSkinEvaluationRepository) GetByID(ctx context.Context, id *string) (*domain.ParameterSkinEvaluation, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.ParameterSkinEvaluation
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.ParameterSkinEvaluation, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.ParameterSkinEvaluation); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.ParameterSkinEvaluation)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// ParameterSkinEvaluationRepository_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type ParameterSkinEvaluationRepository_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *ParameterSkinEvaluationRepository_Expecter) GetByID(ctx interface{}, id interface{}) *ParameterSkinEvaluationRepository_GetByID_Call {
	return &ParameterSkinEvaluationRepository_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *ParameterSkinEvaluationRepository_GetByID_Call) Run(run func(ctx context.Context, id *string)) *ParameterSkinEvaluationRepository_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *ParameterSkinEvaluationRepository_GetByID_Call) Return(parameterSkinEvaluation *domain.ParameterSkinEvaluation, err error) *ParameterSkinEvaluationRepository_GetByID_Call {
	_c.Call.Return(parameterSkinEvaluation, err)
	return _c
}

func (_c *ParameterSkinEvaluationRepository_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.ParameterSkinEvaluation, error)) *ParameterSkinEvaluationRepository_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type ParameterSkinEvaluationRepository
func (_mock *ParameterSkinEvaluationRepository) GetMany(ctx context.Context, filter *domain.ParameterSkinEvaluationFilter) ([]domain.ParameterSkinEvaluation, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.ParameterSkinEvaluation
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.ParameterSkinEvaluationFilter) ([]domain.ParameterSkinEvaluation, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.ParameterSkinEvaluationFilter) []domain.ParameterSkinEvaluation); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.ParameterSkinEvaluation)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.ParameterSkinEvaluationFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.ParameterSkinEvaluationFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// ParameterSkinEvaluationRepository_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type ParameterSkinEvaluationRepository_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *ParameterSkinEvaluationRepository_Expecter) GetMany(ctx interface{}, filter interface{}) *ParameterSkinEvaluationRepository_GetMany_Call {
	return &ParameterSkinEvaluationRepository_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *ParameterSkinEvaluationRepository_GetMany_Call) Run(run func(ctx context.Context, filter *domain.ParameterSkinEvaluationFilter)) *ParameterSkinEvaluationRepository_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.ParameterSkinEvaluationFilter))
	})
	return _c
}

func (_c *ParameterSkinEvaluationRepository_GetMany_Call) Return(parameterSkinEvaluations []domain.ParameterSkinEvaluation, n int, err error) *ParameterSkinEvaluationRepository_GetMany_Call {
	_c.Call.Return(parameterSkinEvaluations, n, err)
	return _c
}

func (_c *ParameterSkinEvaluationRepository_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.ParameterSkinEvaluationFilter) ([]domain.ParameterSkinEvaluation, int, error)) *ParameterSkinEvaluationRepository_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type ParameterSkinEvaluationRepository
func (_mock *ParameterSkinEvaluationRepository) UpdateByID(ctx context.Context, id *string, request *domain.ParameterSkinEvaluationRequest) (*domain.ParameterSkinEvaluation, error) {
	ret := _mock.Called(ctx, id, request)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 *domain.ParameterSkinEvaluation
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.ParameterSkinEvaluationRequest) (*domain.ParameterSkinEvaluation, error)); ok {
		return returnFunc(ctx, id, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *domain.ParameterSkinEvaluationRequest) *domain.ParameterSkinEvaluation); ok {
		r0 = returnFunc(ctx, id, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.ParameterSkinEvaluation)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *domain.ParameterSkinEvaluationRequest) error); ok {
		r1 = returnFunc(ctx, id, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// ParameterSkinEvaluationRepository_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type ParameterSkinEvaluationRepository_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - id
//   - request
func (_e *ParameterSkinEvaluationRepository_Expecter) UpdateByID(ctx interface{}, id interface{}, request interface{}) *ParameterSkinEvaluationRepository_UpdateByID_Call {
	return &ParameterSkinEvaluationRepository_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, id, request)}
}

func (_c *ParameterSkinEvaluationRepository_UpdateByID_Call) Run(run func(ctx context.Context, id *string, request *domain.ParameterSkinEvaluationRequest)) *ParameterSkinEvaluationRepository_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*domain.ParameterSkinEvaluationRequest))
	})
	return _c
}

func (_c *ParameterSkinEvaluationRepository_UpdateByID_Call) Return(parameterSkinEvaluation *domain.ParameterSkinEvaluation, err error) *ParameterSkinEvaluationRepository_UpdateByID_Call {
	_c.Call.Return(parameterSkinEvaluation, err)
	return _c
}

func (_c *ParameterSkinEvaluationRepository_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, id *string, request *domain.ParameterSkinEvaluationRequest) (*domain.ParameterSkinEvaluation, error)) *ParameterSkinEvaluationRepository_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}
