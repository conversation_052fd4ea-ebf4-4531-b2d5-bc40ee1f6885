// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewAuthRepository creates a new instance of AuthRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewAuthRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *AuthRepository {
	mock := &AuthRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// AuthRepository is an autogenerated mock type for the AuthRepository type
type AuthRepository struct {
	mock.Mock
}

type AuthRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *AuthRepository) EXPECT() *AuthRepository_Expecter {
	return &AuthRepository_Expecter{mock: &_m.<PERSON>}
}

// VerifyEmail provides a mock function for the type AuthRepository
func (_mock *AuthRepository) VerifyEmail(ctx context.Context, request *domain.LoginRequest) (*domain.User, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for VerifyEmail")
	}

	var r0 *domain.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.LoginRequest) (*domain.User, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.LoginRequest) *domain.User); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.LoginRequest) error); ok {
		r1 = returnFunc(ctx, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// AuthRepository_VerifyEmail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'VerifyEmail'
type AuthRepository_VerifyEmail_Call struct {
	*mock.Call
}

// VerifyEmail is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *AuthRepository_Expecter) VerifyEmail(ctx interface{}, request interface{}) *AuthRepository_VerifyEmail_Call {
	return &AuthRepository_VerifyEmail_Call{Call: _e.mock.On("VerifyEmail", ctx, request)}
}

func (_c *AuthRepository_VerifyEmail_Call) Run(run func(ctx context.Context, request *domain.LoginRequest)) *AuthRepository_VerifyEmail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.LoginRequest))
	})
	return _c
}

func (_c *AuthRepository_VerifyEmail_Call) Return(user *domain.User, err error) *AuthRepository_VerifyEmail_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *AuthRepository_VerifyEmail_Call) RunAndReturn(run func(ctx context.Context, request *domain.LoginRequest) (*domain.User, error)) *AuthRepository_VerifyEmail_Call {
	_c.Call.Return(run)
	return _c
}
