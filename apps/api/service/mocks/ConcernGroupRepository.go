// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewConcernGroupRepository creates a new instance of ConcernGroupRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewConcernGroupRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *ConcernGroupRepository {
	mock := &ConcernGroupRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// ConcernGroupRepository is an autogenerated mock type for the ConcernGroupRepository type
type ConcernGroupRepository struct {
	mock.Mock
}

type ConcernGroupRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *ConcernGroupRepository) EXPECT() *ConcernGroupRepository_Expecter {
	return &ConcernGroupRepository_Expecter{mock: &_m.Mock}
}

// GetAllConcernGroup provides a mock function for the type ConcernGroupRepository
func (_mock *ConcernGroupRepository) GetAllConcernGroup(ctx context.Context) ([]*domain.ConcernGroup, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllConcernGroup")
	}

	var r0 []*domain.ConcernGroup
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*domain.ConcernGroup, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*domain.ConcernGroup); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*domain.ConcernGroup)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// ConcernGroupRepository_GetAllConcernGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllConcernGroup'
type ConcernGroupRepository_GetAllConcernGroup_Call struct {
	*mock.Call
}

// GetAllConcernGroup is a helper method to define mock.On call
//   - ctx
func (_e *ConcernGroupRepository_Expecter) GetAllConcernGroup(ctx interface{}) *ConcernGroupRepository_GetAllConcernGroup_Call {
	return &ConcernGroupRepository_GetAllConcernGroup_Call{Call: _e.mock.On("GetAllConcernGroup", ctx)}
}

func (_c *ConcernGroupRepository_GetAllConcernGroup_Call) Run(run func(ctx context.Context)) *ConcernGroupRepository_GetAllConcernGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *ConcernGroupRepository_GetAllConcernGroup_Call) Return(concernGroups []*domain.ConcernGroup, err error) *ConcernGroupRepository_GetAllConcernGroup_Call {
	_c.Call.Return(concernGroups, err)
	return _c
}

func (_c *ConcernGroupRepository_GetAllConcernGroup_Call) RunAndReturn(run func(ctx context.Context) ([]*domain.ConcernGroup, error)) *ConcernGroupRepository_GetAllConcernGroup_Call {
	_c.Call.Return(run)
	return _c
}
