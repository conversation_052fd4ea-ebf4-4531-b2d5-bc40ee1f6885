// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewTreatmentProductRepository creates a new instance of TreatmentProductRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTreatmentProductRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TreatmentProductRepository {
	mock := &TreatmentProductRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// TreatmentProductRepository is an autogenerated mock type for the TreatmentProductRepository type
type TreatmentProductRepository struct {
	mock.Mock
}

type TreatmentProductRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *TreatmentProductRepository) EXPECT() *TreatmentProductRepository_Expecter {
	return &TreatmentProductRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type TreatmentProductRepository
func (_mock *TreatmentProductRepository) Create(ctx context.Context, data *domain.TreatmentProduct, concernIDs []string, categoryIDs []string, surveyInput []domain.TreatmentProductSurveyQuestionInput) (*domain.TreatmentProduct, error) {
	ret := _mock.Called(ctx, data, concernIDs, categoryIDs, surveyInput)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *domain.TreatmentProduct
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentProduct, []string, []string, []domain.TreatmentProductSurveyQuestionInput) (*domain.TreatmentProduct, error)); ok {
		return returnFunc(ctx, data, concernIDs, categoryIDs, surveyInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentProduct, []string, []string, []domain.TreatmentProductSurveyQuestionInput) *domain.TreatmentProduct); ok {
		r0 = returnFunc(ctx, data, concernIDs, categoryIDs, surveyInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentProduct)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentProduct, []string, []string, []domain.TreatmentProductSurveyQuestionInput) error); ok {
		r1 = returnFunc(ctx, data, concernIDs, categoryIDs, surveyInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentProductRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type TreatmentProductRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - data
//   - concernIDs
//   - categoryIDs
//   - surveyInput
func (_e *TreatmentProductRepository_Expecter) Create(ctx interface{}, data interface{}, concernIDs interface{}, categoryIDs interface{}, surveyInput interface{}) *TreatmentProductRepository_Create_Call {
	return &TreatmentProductRepository_Create_Call{Call: _e.mock.On("Create", ctx, data, concernIDs, categoryIDs, surveyInput)}
}

func (_c *TreatmentProductRepository_Create_Call) Run(run func(ctx context.Context, data *domain.TreatmentProduct, concernIDs []string, categoryIDs []string, surveyInput []domain.TreatmentProductSurveyQuestionInput)) *TreatmentProductRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentProduct), args[2].([]string), args[3].([]string), args[4].([]domain.TreatmentProductSurveyQuestionInput))
	})
	return _c
}

func (_c *TreatmentProductRepository_Create_Call) Return(treatmentProduct *domain.TreatmentProduct, err error) *TreatmentProductRepository_Create_Call {
	_c.Call.Return(treatmentProduct, err)
	return _c
}

func (_c *TreatmentProductRepository_Create_Call) RunAndReturn(run func(ctx context.Context, data *domain.TreatmentProduct, concernIDs []string, categoryIDs []string, surveyInput []domain.TreatmentProductSurveyQuestionInput) (*domain.TreatmentProduct, error)) *TreatmentProductRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByID provides a mock function for the type TreatmentProductRepository
func (_mock *TreatmentProductRepository) DeleteByID(ctx context.Context, id *string) (*domain.TreatmentProduct, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByID")
	}

	var r0 *domain.TreatmentProduct
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.TreatmentProduct, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.TreatmentProduct); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentProduct)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentProductRepository_DeleteByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByID'
type TreatmentProductRepository_DeleteByID_Call struct {
	*mock.Call
}

// DeleteByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *TreatmentProductRepository_Expecter) DeleteByID(ctx interface{}, id interface{}) *TreatmentProductRepository_DeleteByID_Call {
	return &TreatmentProductRepository_DeleteByID_Call{Call: _e.mock.On("DeleteByID", ctx, id)}
}

func (_c *TreatmentProductRepository_DeleteByID_Call) Run(run func(ctx context.Context, id *string)) *TreatmentProductRepository_DeleteByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *TreatmentProductRepository_DeleteByID_Call) Return(treatmentProduct *domain.TreatmentProduct, err error) *TreatmentProductRepository_DeleteByID_Call {
	_c.Call.Return(treatmentProduct, err)
	return _c
}

func (_c *TreatmentProductRepository_DeleteByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.TreatmentProduct, error)) *TreatmentProductRepository_DeleteByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type TreatmentProductRepository
func (_mock *TreatmentProductRepository) GetByID(ctx context.Context, id *string) (*domain.TreatmentProductResponse, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.TreatmentProductResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.TreatmentProductResponse, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.TreatmentProductResponse); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentProductResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentProductRepository_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type TreatmentProductRepository_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *TreatmentProductRepository_Expecter) GetByID(ctx interface{}, id interface{}) *TreatmentProductRepository_GetByID_Call {
	return &TreatmentProductRepository_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *TreatmentProductRepository_GetByID_Call) Run(run func(ctx context.Context, id *string)) *TreatmentProductRepository_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *TreatmentProductRepository_GetByID_Call) Return(treatmentProductResponse *domain.TreatmentProductResponse, err error) *TreatmentProductRepository_GetByID_Call {
	_c.Call.Return(treatmentProductResponse, err)
	return _c
}

func (_c *TreatmentProductRepository_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.TreatmentProductResponse, error)) *TreatmentProductRepository_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type TreatmentProductRepository
func (_mock *TreatmentProductRepository) GetMany(ctx context.Context, filter *domain.TreatmentProductFilter) ([]domain.TreatmentProductGetMany, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.TreatmentProductGetMany
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentProductFilter) ([]domain.TreatmentProductGetMany, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentProductFilter) []domain.TreatmentProductGetMany); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.TreatmentProductGetMany)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentProductFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.TreatmentProductFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// TreatmentProductRepository_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type TreatmentProductRepository_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *TreatmentProductRepository_Expecter) GetMany(ctx interface{}, filter interface{}) *TreatmentProductRepository_GetMany_Call {
	return &TreatmentProductRepository_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *TreatmentProductRepository_GetMany_Call) Run(run func(ctx context.Context, filter *domain.TreatmentProductFilter)) *TreatmentProductRepository_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentProductFilter))
	})
	return _c
}

func (_c *TreatmentProductRepository_GetMany_Call) Return(treatmentProductGetManys []domain.TreatmentProductGetMany, n int, err error) *TreatmentProductRepository_GetMany_Call {
	_c.Call.Return(treatmentProductGetManys, n, err)
	return _c
}

func (_c *TreatmentProductRepository_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.TreatmentProductFilter) ([]domain.TreatmentProductGetMany, int, error)) *TreatmentProductRepository_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// GetSupplementaryData provides a mock function for the type TreatmentProductRepository
func (_mock *TreatmentProductRepository) GetSupplementaryData(ctx context.Context, request *domain.TreatmentProductRequest) (*domain.TreatmentProductSupplementaryData, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for GetSupplementaryData")
	}

	var r0 *domain.TreatmentProductSupplementaryData
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentProductRequest) (*domain.TreatmentProductSupplementaryData, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentProductRequest) *domain.TreatmentProductSupplementaryData); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentProductSupplementaryData)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentProductRequest) error); ok {
		r1 = returnFunc(ctx, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentProductRepository_GetSupplementaryData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSupplementaryData'
type TreatmentProductRepository_GetSupplementaryData_Call struct {
	*mock.Call
}

// GetSupplementaryData is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *TreatmentProductRepository_Expecter) GetSupplementaryData(ctx interface{}, request interface{}) *TreatmentProductRepository_GetSupplementaryData_Call {
	return &TreatmentProductRepository_GetSupplementaryData_Call{Call: _e.mock.On("GetSupplementaryData", ctx, request)}
}

func (_c *TreatmentProductRepository_GetSupplementaryData_Call) Run(run func(ctx context.Context, request *domain.TreatmentProductRequest)) *TreatmentProductRepository_GetSupplementaryData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentProductRequest))
	})
	return _c
}

func (_c *TreatmentProductRepository_GetSupplementaryData_Call) Return(treatmentProductSupplementaryData *domain.TreatmentProductSupplementaryData, err error) *TreatmentProductRepository_GetSupplementaryData_Call {
	_c.Call.Return(treatmentProductSupplementaryData, err)
	return _c
}

func (_c *TreatmentProductRepository_GetSupplementaryData_Call) RunAndReturn(run func(ctx context.Context, request *domain.TreatmentProductRequest) (*domain.TreatmentProductSupplementaryData, error)) *TreatmentProductRepository_GetSupplementaryData_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type TreatmentProductRepository
func (_mock *TreatmentProductRepository) UpdateByID(ctx context.Context, existingData *domain.TreatmentProductResponse, newData *domain.TreatmentProduct, concernIDs []string, categoryIDs []string, surveyInput []domain.TreatmentProductSurveyQuestionInput) (*domain.TreatmentProduct, error) {
	ret := _mock.Called(ctx, existingData, newData, concernIDs, categoryIDs, surveyInput)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 *domain.TreatmentProduct
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentProductResponse, *domain.TreatmentProduct, []string, []string, []domain.TreatmentProductSurveyQuestionInput) (*domain.TreatmentProduct, error)); ok {
		return returnFunc(ctx, existingData, newData, concernIDs, categoryIDs, surveyInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.TreatmentProductResponse, *domain.TreatmentProduct, []string, []string, []domain.TreatmentProductSurveyQuestionInput) *domain.TreatmentProduct); ok {
		r0 = returnFunc(ctx, existingData, newData, concernIDs, categoryIDs, surveyInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.TreatmentProduct)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.TreatmentProductResponse, *domain.TreatmentProduct, []string, []string, []domain.TreatmentProductSurveyQuestionInput) error); ok {
		r1 = returnFunc(ctx, existingData, newData, concernIDs, categoryIDs, surveyInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentProductRepository_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type TreatmentProductRepository_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - existingData
//   - newData
//   - concernIDs
//   - categoryIDs
//   - surveyInput
func (_e *TreatmentProductRepository_Expecter) UpdateByID(ctx interface{}, existingData interface{}, newData interface{}, concernIDs interface{}, categoryIDs interface{}, surveyInput interface{}) *TreatmentProductRepository_UpdateByID_Call {
	return &TreatmentProductRepository_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, existingData, newData, concernIDs, categoryIDs, surveyInput)}
}

func (_c *TreatmentProductRepository_UpdateByID_Call) Run(run func(ctx context.Context, existingData *domain.TreatmentProductResponse, newData *domain.TreatmentProduct, concernIDs []string, categoryIDs []string, surveyInput []domain.TreatmentProductSurveyQuestionInput)) *TreatmentProductRepository_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.TreatmentProductResponse), args[2].(*domain.TreatmentProduct), args[3].([]string), args[4].([]string), args[5].([]domain.TreatmentProductSurveyQuestionInput))
	})
	return _c
}

func (_c *TreatmentProductRepository_UpdateByID_Call) Return(treatmentProduct *domain.TreatmentProduct, err error) *TreatmentProductRepository_UpdateByID_Call {
	_c.Call.Return(treatmentProduct, err)
	return _c
}

func (_c *TreatmentProductRepository_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, existingData *domain.TreatmentProductResponse, newData *domain.TreatmentProduct, concernIDs []string, categoryIDs []string, surveyInput []domain.TreatmentProductSurveyQuestionInput) (*domain.TreatmentProduct, error)) *TreatmentProductRepository_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}
