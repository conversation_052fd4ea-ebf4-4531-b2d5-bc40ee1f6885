// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewSummaryHttp creates a new instance of SummaryHttp. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSummaryHttp(t interface {
	mock.TestingT
	Cleanup(func())
}) *SummaryHttp {
	mock := &SummaryHttp{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SummaryHttp is an autogenerated mock type for the SummaryHttp type
type SummaryHttp struct {
	mock.Mock
}

type SummaryHttp_Expecter struct {
	mock *mock.Mock
}

func (_m *SummaryHttp) EXPECT() *SummaryHttp_Expecter {
	return &SummaryHttp_Expecter{mock: &_m.Mock}
}

// GetSummary provides a mock function for the type SummaryHttp
func (_mock *SummaryHttp) GetSummary(ctx context.Context, data *domain.GetSummaryMLRequest) (*domain.SummaryResponse, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for GetSummary")
	}

	var r0 *domain.SummaryResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.GetSummaryMLRequest) (*domain.SummaryResponse, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.GetSummaryMLRequest) *domain.SummaryResponse); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SummaryResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.GetSummaryMLRequest) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SummaryHttp_GetSummary_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSummary'
type SummaryHttp_GetSummary_Call struct {
	*mock.Call
}

// GetSummary is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *SummaryHttp_Expecter) GetSummary(ctx interface{}, data interface{}) *SummaryHttp_GetSummary_Call {
	return &SummaryHttp_GetSummary_Call{Call: _e.mock.On("GetSummary", ctx, data)}
}

func (_c *SummaryHttp_GetSummary_Call) Run(run func(ctx context.Context, data *domain.GetSummaryMLRequest)) *SummaryHttp_GetSummary_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.GetSummaryMLRequest))
	})
	return _c
}

func (_c *SummaryHttp_GetSummary_Call) Return(summaryResponse *domain.SummaryResponse, err error) *SummaryHttp_GetSummary_Call {
	_c.Call.Return(summaryResponse, err)
	return _c
}

func (_c *SummaryHttp_GetSummary_Call) RunAndReturn(run func(ctx context.Context, data *domain.GetSummaryMLRequest) (*domain.SummaryResponse, error)) *SummaryHttp_GetSummary_Call {
	_c.Call.Return(run)
	return _c
}
