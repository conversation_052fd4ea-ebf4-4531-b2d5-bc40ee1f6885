// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewSkinDetectionHttp creates a new instance of SkinDetectionHttp. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSkinDetectionHttp(t interface {
	mock.TestingT
	Cleanup(func())
}) *SkinDetectionHttp {
	mock := &SkinDetectionHttp{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SkinDetectionHttp is an autogenerated mock type for the SkinDetectionHttp type
type SkinDetectionHttp struct {
	mock.Mock
}

type SkinDetectionHttp_Expecter struct {
	mock *mock.Mock
}

func (_m *SkinDetectionHttp) EXPECT() *SkinDetectionHttp_Expecter {
	return &SkinDetectionHttp_Expecter{mock: &_m.Mock}
}

// DetectPores provides a mock function for the type SkinDetectionHttp
func (_mock *SkinDetectionHttp) DetectPores(ctx context.Context, data *domain.SkinDetectionMLRequest) (*domain.SkinDetectionMLResponse, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for DetectPores")
	}

	var r0 *domain.SkinDetectionMLResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinDetectionMLRequest) (*domain.SkinDetectionMLResponse, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinDetectionMLRequest) *domain.SkinDetectionMLResponse); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinDetectionMLResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinDetectionMLRequest) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinDetectionHttp_DetectPores_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DetectPores'
type SkinDetectionHttp_DetectPores_Call struct {
	*mock.Call
}

// DetectPores is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *SkinDetectionHttp_Expecter) DetectPores(ctx interface{}, data interface{}) *SkinDetectionHttp_DetectPores_Call {
	return &SkinDetectionHttp_DetectPores_Call{Call: _e.mock.On("DetectPores", ctx, data)}
}

func (_c *SkinDetectionHttp_DetectPores_Call) Run(run func(ctx context.Context, data *domain.SkinDetectionMLRequest)) *SkinDetectionHttp_DetectPores_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinDetectionMLRequest))
	})
	return _c
}

func (_c *SkinDetectionHttp_DetectPores_Call) Return(skinDetectionMLResponse *domain.SkinDetectionMLResponse, err error) *SkinDetectionHttp_DetectPores_Call {
	_c.Call.Return(skinDetectionMLResponse, err)
	return _c
}

func (_c *SkinDetectionHttp_DetectPores_Call) RunAndReturn(run func(ctx context.Context, data *domain.SkinDetectionMLRequest) (*domain.SkinDetectionMLResponse, error)) *SkinDetectionHttp_DetectPores_Call {
	_c.Call.Return(run)
	return _c
}

// DetectWrinkles provides a mock function for the type SkinDetectionHttp
func (_mock *SkinDetectionHttp) DetectWrinkles(ctx context.Context, data *domain.SkinDetectionMLRequest) (*domain.SkinDetectionMLResponse, error) {
	ret := _mock.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for DetectWrinkles")
	}

	var r0 *domain.SkinDetectionMLResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinDetectionMLRequest) (*domain.SkinDetectionMLResponse, error)); ok {
		return returnFunc(ctx, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinDetectionMLRequest) *domain.SkinDetectionMLResponse); ok {
		r0 = returnFunc(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinDetectionMLResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinDetectionMLRequest) error); ok {
		r1 = returnFunc(ctx, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinDetectionHttp_DetectWrinkles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DetectWrinkles'
type SkinDetectionHttp_DetectWrinkles_Call struct {
	*mock.Call
}

// DetectWrinkles is a helper method to define mock.On call
//   - ctx
//   - data
func (_e *SkinDetectionHttp_Expecter) DetectWrinkles(ctx interface{}, data interface{}) *SkinDetectionHttp_DetectWrinkles_Call {
	return &SkinDetectionHttp_DetectWrinkles_Call{Call: _e.mock.On("DetectWrinkles", ctx, data)}
}

func (_c *SkinDetectionHttp_DetectWrinkles_Call) Run(run func(ctx context.Context, data *domain.SkinDetectionMLRequest)) *SkinDetectionHttp_DetectWrinkles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinDetectionMLRequest))
	})
	return _c
}

func (_c *SkinDetectionHttp_DetectWrinkles_Call) Return(skinDetectionMLResponse *domain.SkinDetectionMLResponse, err error) *SkinDetectionHttp_DetectWrinkles_Call {
	_c.Call.Return(skinDetectionMLResponse, err)
	return _c
}

func (_c *SkinDetectionHttp_DetectWrinkles_Call) RunAndReturn(run func(ctx context.Context, data *domain.SkinDetectionMLRequest) (*domain.SkinDetectionMLResponse, error)) *SkinDetectionHttp_DetectWrinkles_Call {
	_c.Call.Return(run)
	return _c
}
