// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewSkinProblemRepository creates a new instance of SkinProblemRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSkinProblemRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *SkinProblemRepository {
	mock := &SkinProblemRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SkinProblemRepository is an autogenerated mock type for the SkinProblemRepository type
type SkinProblemRepository struct {
	mock.Mock
}

type SkinProblemRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *SkinProblemRepository) EXPECT() *SkinProblemRepository_Expecter {
	return &SkinProblemRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type SkinProblemRepository
func (_mock *SkinProblemRepository) Create(ctx context.Context, request *domain.SkinProblemRequest) (*domain.SkinProblem, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *domain.SkinProblem
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemRequest) (*domain.SkinProblem, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemRequest) *domain.SkinProblem); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinProblem)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinProblemRequest) error); ok {
		r1 = returnFunc(ctx, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinProblemRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type SkinProblemRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *SkinProblemRepository_Expecter) Create(ctx interface{}, request interface{}) *SkinProblemRepository_Create_Call {
	return &SkinProblemRepository_Create_Call{Call: _e.mock.On("Create", ctx, request)}
}

func (_c *SkinProblemRepository_Create_Call) Run(run func(ctx context.Context, request *domain.SkinProblemRequest)) *SkinProblemRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinProblemRequest))
	})
	return _c
}

func (_c *SkinProblemRepository_Create_Call) Return(skinProblem *domain.SkinProblem, err error) *SkinProblemRepository_Create_Call {
	_c.Call.Return(skinProblem, err)
	return _c
}

func (_c *SkinProblemRepository_Create_Call) RunAndReturn(run func(ctx context.Context, request *domain.SkinProblemRequest) (*domain.SkinProblem, error)) *SkinProblemRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByID provides a mock function for the type SkinProblemRepository
func (_mock *SkinProblemRepository) DeleteByID(ctx context.Context, id *string) (*domain.SkinProblem, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByID")
	}

	var r0 *domain.SkinProblem
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.SkinProblem, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.SkinProblem); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinProblem)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinProblemRepository_DeleteByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByID'
type SkinProblemRepository_DeleteByID_Call struct {
	*mock.Call
}

// DeleteByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SkinProblemRepository_Expecter) DeleteByID(ctx interface{}, id interface{}) *SkinProblemRepository_DeleteByID_Call {
	return &SkinProblemRepository_DeleteByID_Call{Call: _e.mock.On("DeleteByID", ctx, id)}
}

func (_c *SkinProblemRepository_DeleteByID_Call) Run(run func(ctx context.Context, id *string)) *SkinProblemRepository_DeleteByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *SkinProblemRepository_DeleteByID_Call) Return(skinProblem *domain.SkinProblem, err error) *SkinProblemRepository_DeleteByID_Call {
	_c.Call.Return(skinProblem, err)
	return _c
}

func (_c *SkinProblemRepository_DeleteByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.SkinProblem, error)) *SkinProblemRepository_DeleteByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type SkinProblemRepository
func (_mock *SkinProblemRepository) GetByID(ctx context.Context, id *string) (*domain.SkinProblemResponse, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.SkinProblemResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.SkinProblemResponse, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.SkinProblemResponse); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinProblemResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinProblemRepository_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type SkinProblemRepository_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *SkinProblemRepository_Expecter) GetByID(ctx interface{}, id interface{}) *SkinProblemRepository_GetByID_Call {
	return &SkinProblemRepository_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *SkinProblemRepository_GetByID_Call) Run(run func(ctx context.Context, id *string)) *SkinProblemRepository_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *SkinProblemRepository_GetByID_Call) Return(skinProblemResponse *domain.SkinProblemResponse, err error) *SkinProblemRepository_GetByID_Call {
	_c.Call.Return(skinProblemResponse, err)
	return _c
}

func (_c *SkinProblemRepository_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.SkinProblemResponse, error)) *SkinProblemRepository_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type SkinProblemRepository
func (_mock *SkinProblemRepository) GetMany(ctx context.Context, filter *domain.SkinProblemFilter) ([]domain.SkinProblemResponse, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.SkinProblemResponse
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemFilter) ([]domain.SkinProblemResponse, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemFilter) []domain.SkinProblemResponse); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.SkinProblemResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinProblemFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.SkinProblemFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// SkinProblemRepository_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type SkinProblemRepository_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *SkinProblemRepository_Expecter) GetMany(ctx interface{}, filter interface{}) *SkinProblemRepository_GetMany_Call {
	return &SkinProblemRepository_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *SkinProblemRepository_GetMany_Call) Run(run func(ctx context.Context, filter *domain.SkinProblemFilter)) *SkinProblemRepository_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinProblemFilter))
	})
	return _c
}

func (_c *SkinProblemRepository_GetMany_Call) Return(skinProblemResponses []domain.SkinProblemResponse, n int, err error) *SkinProblemRepository_GetMany_Call {
	_c.Call.Return(skinProblemResponses, n, err)
	return _c
}

func (_c *SkinProblemRepository_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.SkinProblemFilter) ([]domain.SkinProblemResponse, int, error)) *SkinProblemRepository_GetMany_Call {
	_c.Call.Return(run)
	return _c
}

// GetSkinProblemIndications provides a mock function for the type SkinProblemRepository
func (_mock *SkinProblemRepository) GetSkinProblemIndications(ctx context.Context, ids []string) ([]domain.SkinProblemIndication, error) {
	ret := _mock.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for GetSkinProblemIndications")
	}

	var r0 []domain.SkinProblemIndication
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) ([]domain.SkinProblemIndication, error)); ok {
		return returnFunc(ctx, ids)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) []domain.SkinProblemIndication); ok {
		r0 = returnFunc(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.SkinProblemIndication)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = returnFunc(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinProblemRepository_GetSkinProblemIndications_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSkinProblemIndications'
type SkinProblemRepository_GetSkinProblemIndications_Call struct {
	*mock.Call
}

// GetSkinProblemIndications is a helper method to define mock.On call
//   - ctx
//   - ids
func (_e *SkinProblemRepository_Expecter) GetSkinProblemIndications(ctx interface{}, ids interface{}) *SkinProblemRepository_GetSkinProblemIndications_Call {
	return &SkinProblemRepository_GetSkinProblemIndications_Call{Call: _e.mock.On("GetSkinProblemIndications", ctx, ids)}
}

func (_c *SkinProblemRepository_GetSkinProblemIndications_Call) Run(run func(ctx context.Context, ids []string)) *SkinProblemRepository_GetSkinProblemIndications_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]string))
	})
	return _c
}

func (_c *SkinProblemRepository_GetSkinProblemIndications_Call) Return(skinProblemIndications []domain.SkinProblemIndication, err error) *SkinProblemRepository_GetSkinProblemIndications_Call {
	_c.Call.Return(skinProblemIndications, err)
	return _c
}

func (_c *SkinProblemRepository_GetSkinProblemIndications_Call) RunAndReturn(run func(ctx context.Context, ids []string) ([]domain.SkinProblemIndication, error)) *SkinProblemRepository_GetSkinProblemIndications_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function for the type SkinProblemRepository
func (_mock *SkinProblemRepository) UpdateByID(ctx context.Context, existingData *domain.SkinProblemResponse, request *domain.SkinProblemRequest) (*domain.SkinProblem, error) {
	ret := _mock.Called(ctx, existingData, request)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 *domain.SkinProblem
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemResponse, *domain.SkinProblemRequest) (*domain.SkinProblem, error)); ok {
		return returnFunc(ctx, existingData, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.SkinProblemResponse, *domain.SkinProblemRequest) *domain.SkinProblem); ok {
		r0 = returnFunc(ctx, existingData, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.SkinProblem)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.SkinProblemResponse, *domain.SkinProblemRequest) error); ok {
		r1 = returnFunc(ctx, existingData, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SkinProblemRepository_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type SkinProblemRepository_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx
//   - existingData
//   - request
func (_e *SkinProblemRepository_Expecter) UpdateByID(ctx interface{}, existingData interface{}, request interface{}) *SkinProblemRepository_UpdateByID_Call {
	return &SkinProblemRepository_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, existingData, request)}
}

func (_c *SkinProblemRepository_UpdateByID_Call) Run(run func(ctx context.Context, existingData *domain.SkinProblemResponse, request *domain.SkinProblemRequest)) *SkinProblemRepository_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.SkinProblemResponse), args[2].(*domain.SkinProblemRequest))
	})
	return _c
}

func (_c *SkinProblemRepository_UpdateByID_Call) Return(skinProblem *domain.SkinProblem, err error) *SkinProblemRepository_UpdateByID_Call {
	_c.Call.Return(skinProblem, err)
	return _c
}

func (_c *SkinProblemRepository_UpdateByID_Call) RunAndReturn(run func(ctx context.Context, existingData *domain.SkinProblemResponse, request *domain.SkinProblemRequest) (*domain.SkinProblem, error)) *SkinProblemRepository_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}
