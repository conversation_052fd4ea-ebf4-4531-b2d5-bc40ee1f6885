// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewTreatmentRepository creates a new instance of TreatmentRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTreatmentRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TreatmentRepository {
	mock := &TreatmentRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// TreatmentRepository is an autogenerated mock type for the TreatmentRepository type
type TreatmentRepository struct {
	mock.Mock
}

type TreatmentRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *TreatmentRepository) EXPECT() *TreatmentRepository_Expecter {
	return &TreatmentRepository_Expecter{mock: &_m.Mock}
}

// GetAllTreatment provides a mock function for the type TreatmentRepository
func (_mock *TreatmentRepository) GetAllTreatment(ctx context.Context) ([]*domain.Treatment, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllTreatment")
	}

	var r0 []*domain.Treatment
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*domain.Treatment, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*domain.Treatment); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*domain.Treatment)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TreatmentRepository_GetAllTreatment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllTreatment'
type TreatmentRepository_GetAllTreatment_Call struct {
	*mock.Call
}

// GetAllTreatment is a helper method to define mock.On call
//   - ctx
func (_e *TreatmentRepository_Expecter) GetAllTreatment(ctx interface{}) *TreatmentRepository_GetAllTreatment_Call {
	return &TreatmentRepository_GetAllTreatment_Call{Call: _e.mock.On("GetAllTreatment", ctx)}
}

func (_c *TreatmentRepository_GetAllTreatment_Call) Run(run func(ctx context.Context)) *TreatmentRepository_GetAllTreatment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *TreatmentRepository_GetAllTreatment_Call) Return(treatments []*domain.Treatment, err error) *TreatmentRepository_GetAllTreatment_Call {
	_c.Call.Return(treatments, err)
	return _c
}

func (_c *TreatmentRepository_GetAllTreatment_Call) RunAndReturn(run func(ctx context.Context) ([]*domain.Treatment, error)) *TreatmentRepository_GetAllTreatment_Call {
	_c.Call.Return(run)
	return _c
}
