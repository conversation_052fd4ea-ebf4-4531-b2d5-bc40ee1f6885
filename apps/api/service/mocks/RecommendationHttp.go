// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewRecommendationHttp creates a new instance of RecommendationHttp. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRecommendationHttp(t interface {
	mock.TestingT
	Cleanup(func())
}) *RecommendationHttp {
	mock := &RecommendationHttp{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// RecommendationHttp is an autogenerated mock type for the RecommendationHttp type
type RecommendationHttp struct {
	mock.Mock
}

type RecommendationHttp_Expecter struct {
	mock *mock.Mock
}

func (_m *RecommendationHttp) EXPECT() *RecommendationHttp_Expecter {
	return &RecommendationHttp_Expecter{mock: &_m.<PERSON>ck}
}

// GetRecommendation provides a mock function for the type RecommendationHttp
func (_mock *RecommendationHttp) GetRecommendation(ctx context.Context, request *domain.GetRecommendationMLRequest) (*domain.RecommendationResponse, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for GetRecommendation")
	}

	var r0 *domain.RecommendationResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.GetRecommendationMLRequest) (*domain.RecommendationResponse, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.GetRecommendationMLRequest) *domain.RecommendationResponse); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.RecommendationResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.GetRecommendationMLRequest) error); ok {
		r1 = returnFunc(ctx, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// RecommendationHttp_GetRecommendation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRecommendation'
type RecommendationHttp_GetRecommendation_Call struct {
	*mock.Call
}

// GetRecommendation is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *RecommendationHttp_Expecter) GetRecommendation(ctx interface{}, request interface{}) *RecommendationHttp_GetRecommendation_Call {
	return &RecommendationHttp_GetRecommendation_Call{Call: _e.mock.On("GetRecommendation", ctx, request)}
}

func (_c *RecommendationHttp_GetRecommendation_Call) Run(run func(ctx context.Context, request *domain.GetRecommendationMLRequest)) *RecommendationHttp_GetRecommendation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.GetRecommendationMLRequest))
	})
	return _c
}

func (_c *RecommendationHttp_GetRecommendation_Call) Return(recommendationResponse *domain.RecommendationResponse, err error) *RecommendationHttp_GetRecommendation_Call {
	_c.Call.Return(recommendationResponse, err)
	return _c
}

func (_c *RecommendationHttp_GetRecommendation_Call) RunAndReturn(run func(ctx context.Context, request *domain.GetRecommendationMLRequest) (*domain.RecommendationResponse, error)) *RecommendationHttp_GetRecommendation_Call {
	_c.Call.Return(run)
	return _c
}

// RecommendationTreatment provides a mock function for the type RecommendationHttp
func (_mock *RecommendationHttp) RecommendationTreatment(ctx context.Context, request *domain.RecommendationTreatmentMLRequest) (*domain.RecommendationTreatmentMLResponse, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for RecommendationTreatment")
	}

	var r0 *domain.RecommendationTreatmentMLResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.RecommendationTreatmentMLRequest) (*domain.RecommendationTreatmentMLResponse, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.RecommendationTreatmentMLRequest) *domain.RecommendationTreatmentMLResponse); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.RecommendationTreatmentMLResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.RecommendationTreatmentMLRequest) error); ok {
		r1 = returnFunc(ctx, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// RecommendationHttp_RecommendationTreatment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecommendationTreatment'
type RecommendationHttp_RecommendationTreatment_Call struct {
	*mock.Call
}

// RecommendationTreatment is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *RecommendationHttp_Expecter) RecommendationTreatment(ctx interface{}, request interface{}) *RecommendationHttp_RecommendationTreatment_Call {
	return &RecommendationHttp_RecommendationTreatment_Call{Call: _e.mock.On("RecommendationTreatment", ctx, request)}
}

func (_c *RecommendationHttp_RecommendationTreatment_Call) Run(run func(ctx context.Context, request *domain.RecommendationTreatmentMLRequest)) *RecommendationHttp_RecommendationTreatment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.RecommendationTreatmentMLRequest))
	})
	return _c
}

func (_c *RecommendationHttp_RecommendationTreatment_Call) Return(recommendationTreatmentMLResponse *domain.RecommendationTreatmentMLResponse, err error) *RecommendationHttp_RecommendationTreatment_Call {
	_c.Call.Return(recommendationTreatmentMLResponse, err)
	return _c
}

func (_c *RecommendationHttp_RecommendationTreatment_Call) RunAndReturn(run func(ctx context.Context, request *domain.RecommendationTreatmentMLRequest) (*domain.RecommendationTreatmentMLResponse, error)) *RecommendationHttp_RecommendationTreatment_Call {
	_c.Call.Return(run)
	return _c
}
