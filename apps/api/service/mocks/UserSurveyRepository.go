// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewUserSurveyRepository creates a new instance of UserSurveyRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserSurveyRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserSurveyRepository {
	mock := &UserSurveyRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// UserSurveyRepository is an autogenerated mock type for the UserSurveyRepository type
type UserSurveyRepository struct {
	mock.Mock
}

type UserSurveyRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *UserSurveyRepository) EXPECT() *UserSurveyRepository_Expecter {
	return &UserSurveyRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type UserSurveyRepository
func (_mock *UserSurveyRepository) Create(ctx context.Context, request *domain.UserSurveyRequest) (*domain.UserSurvey, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *domain.UserSurvey
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.UserSurveyRequest) (*domain.UserSurvey, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.UserSurveyRequest) *domain.UserSurvey); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.UserSurvey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.UserSurveyRequest) error); ok {
		r1 = returnFunc(ctx, request)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// UserSurveyRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type UserSurveyRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *UserSurveyRepository_Expecter) Create(ctx interface{}, request interface{}) *UserSurveyRepository_Create_Call {
	return &UserSurveyRepository_Create_Call{Call: _e.mock.On("Create", ctx, request)}
}

func (_c *UserSurveyRepository_Create_Call) Run(run func(ctx context.Context, request *domain.UserSurveyRequest)) *UserSurveyRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.UserSurveyRequest))
	})
	return _c
}

func (_c *UserSurveyRepository_Create_Call) Return(userSurvey *domain.UserSurvey, err error) *UserSurveyRepository_Create_Call {
	_c.Call.Return(userSurvey, err)
	return _c
}

func (_c *UserSurveyRepository_Create_Call) RunAndReturn(run func(ctx context.Context, request *domain.UserSurveyRequest) (*domain.UserSurvey, error)) *UserSurveyRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type UserSurveyRepository
func (_mock *UserSurveyRepository) GetByID(ctx context.Context, id *string) (*domain.UserSurvey, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *domain.UserSurvey
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*domain.UserSurvey, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *domain.UserSurvey); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.UserSurvey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// UserSurveyRepository_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type UserSurveyRepository_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *UserSurveyRepository_Expecter) GetByID(ctx interface{}, id interface{}) *UserSurveyRepository_GetByID_Call {
	return &UserSurveyRepository_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *UserSurveyRepository_GetByID_Call) Run(run func(ctx context.Context, id *string)) *UserSurveyRepository_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *UserSurveyRepository_GetByID_Call) Return(userSurvey *domain.UserSurvey, err error) *UserSurveyRepository_GetByID_Call {
	_c.Call.Return(userSurvey, err)
	return _c
}

func (_c *UserSurveyRepository_GetByID_Call) RunAndReturn(run func(ctx context.Context, id *string) (*domain.UserSurvey, error)) *UserSurveyRepository_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMany provides a mock function for the type UserSurveyRepository
func (_mock *UserSurveyRepository) GetMany(ctx context.Context, filter *domain.UserSurveyFilter) ([]domain.UserSurvey, int, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetMany")
	}

	var r0 []domain.UserSurvey
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.UserSurveyFilter) ([]domain.UserSurvey, int, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.UserSurveyFilter) []domain.UserSurvey); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.UserSurvey)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.UserSurveyFilter) int); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.UserSurveyFilter) error); ok {
		r2 = returnFunc(ctx, filter)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// UserSurveyRepository_GetMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMany'
type UserSurveyRepository_GetMany_Call struct {
	*mock.Call
}

// GetMany is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *UserSurveyRepository_Expecter) GetMany(ctx interface{}, filter interface{}) *UserSurveyRepository_GetMany_Call {
	return &UserSurveyRepository_GetMany_Call{Call: _e.mock.On("GetMany", ctx, filter)}
}

func (_c *UserSurveyRepository_GetMany_Call) Run(run func(ctx context.Context, filter *domain.UserSurveyFilter)) *UserSurveyRepository_GetMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.UserSurveyFilter))
	})
	return _c
}

func (_c *UserSurveyRepository_GetMany_Call) Return(userSurveys []domain.UserSurvey, n int, err error) *UserSurveyRepository_GetMany_Call {
	_c.Call.Return(userSurveys, n, err)
	return _c
}

func (_c *UserSurveyRepository_GetMany_Call) RunAndReturn(run func(ctx context.Context, filter *domain.UserSurveyFilter) ([]domain.UserSurvey, int, error)) *UserSurveyRepository_GetMany_Call {
	_c.Call.Return(run)
	return _c
}
