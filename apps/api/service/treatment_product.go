package service

import (
	"context"
	"fmt"
	"os"

	"github.com/rs/zerolog/log"

	"api/domain"
)

//go:generate mockery
type TreatmentProductRepository interface {
	GetSupplementaryData(
		ctx context.Context,
		request *domain.TreatmentProductRequest,
	) (*domain.TreatmentProductSupplementaryData, error)
	Create(
		ctx context.Context,
		data *domain.TreatmentProduct,
		concernIDs, categoryIDs []string,
		surveyInput []domain.TreatmentProductSurveyQuestionInput,
	) (*domain.TreatmentProduct, error)
	GetByID(ctx context.Context, id *string) (
		*domain.TreatmentProductResponse,
		error,
	)
	UpdateByID(
		ctx context.Context,
		existingData *domain.TreatmentProductResponse,
		newData *domain.TreatmentProduct,
		concernIDs, categoryIDs []string,
		surveyInput []domain.TreatmentProductSurveyQuestionInput,
	) (*domain.TreatmentProduct, error)
	DeleteByID(ctx context.Context, id *string) (*domain.TreatmentProduct, error)
	GetMany(
		ctx context.Context,
		filter *domain.TreatmentProductFilter,
	) ([]domain.TreatmentProductGetMany, int, error)
}

type treatmentProductService struct {
	repo      TreatmentProductRepository
	presigner Presigner
}

func NewTreatmentProductService(
	repo TreatmentProductRepository,
	presigner Presigner,
) *treatmentProductService {
	return &treatmentProductService{repo, presigner}
}

func preparePrimaryData(
	request *domain.TreatmentProductRequest,
	supplementaryData *domain.TreatmentProductSupplementaryData,
) (*domain.TreatmentProduct, domain.TreatmentProductSurveyQuestionJSON) {
	var surveyData domain.TreatmentProductSurveyQuestionJSON
	for i := 0; i < len(request.SurveyQuestions); i++ {
		surveyData = append(
			surveyData,
			domain.TreatmentProductSurveyQuestion{
				ID:             supplementaryData.SurveyQuestions[i].ID,
				Question:       supplementaryData.SurveyQuestions[i].Question,
				Answers:        supplementaryData.SurveyQuestions[i].Answers,
				SelectedAnswer: request.SurveyQuestions[i].SelectedAnswer,
				QuestionOrder:  supplementaryData.SurveyQuestions[i].QuestionOrder,
			},
		)
	}

	primaryData := domain.TreatmentProduct{
		ItemCode:                  request.ItemCode,
		Name:                      request.Name,
		Type:                      request.Type,
		Description:               request.Description,
		Price:                     request.Price,
		IntervalID:                request.IntervalID,
		MediaUrl:                  request.MediaUrl,
		ThumbnailUrl:              request.ThumbnailUrl,
		Notes:                     request.Notes,
		Quantity:                  request.Quantity,
		IsTopRecommendation:       request.IsTopRecommendation,
		DurationTopRecommendation: request.DurationTopRecommendation,
	}

	return &primaryData, surveyData
}

func (service *treatmentProductService) Create(
	ctx context.Context,
	request *domain.TreatmentProductRequest,
) (
	*domain.TreatmentProduct,
	*domain.TreatmentProductSupplementaryData,
	error,
) {
	supplementaryData, err := service.repo.GetSupplementaryData(ctx, request)

	if err != nil {
		return nil, nil, err
	}

	// prevent nil pointer dereference error.
	if supplementaryData == nil {
		return nil, nil, fmt.Errorf("Supplementary data is nil")
	}

	primaryData, surveyData := preparePrimaryData(request, supplementaryData)

	newData, err := service.repo.Create(
		ctx,
		primaryData,
		request.ConcernIDs,
		request.CategoryIDs,
		request.SurveyQuestions,
	)

	if err != nil {
		return nil, nil, err
	}

	supplementaryData.SurveyQuestions = surveyData

	return newData, supplementaryData, nil
}

func (service *treatmentProductService) GetByID(
	ctx context.Context,
	id *string,
) (*domain.TreatmentProductResponse, error) {
	data, err := service.repo.GetByID(ctx, id)

	switch {
	case err != nil:
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	if data.MediaUrl != nil {
		bucketName := os.Getenv("AWS_S3_BUCKET")

		presignedHttpRequest, err := service.presigner.GetObject(
			ctx,
			bucketName,
			*data.MediaUrl,
			3600,
		)

		if err != nil {
			log.Error().Err(err).Msg("Failed to get presigned URL")
			return nil, err
		}

		data.MediaUrl = &presignedHttpRequest.URL
	}

	if data.ThumbnailUrl != nil {
		bucketName := os.Getenv("AWS_S3_BUCKET")

		presignedHttpRequest, err := service.presigner.GetObject(
			ctx,
			bucketName,
			*data.ThumbnailUrl,
			3600,
		)

		if err != nil {
			log.Error().Err(err).Msg("Failed to get presigned URL")
			return nil, err
		}

		data.ThumbnailUrl = &presignedHttpRequest.URL
	}

	return data, nil
}

func (service *treatmentProductService) UpdateByID(
	ctx context.Context,
	id *string,
	request *domain.TreatmentProductRequest,
) (
	*domain.TreatmentProduct,
	*domain.TreatmentProductSupplementaryData,
	error,
) {
	existingData, err := service.GetByID(ctx, id)

	if err != nil {
		return nil, nil, err
	}

	supplementaryData, err := service.repo.GetSupplementaryData(ctx, request)

	if err != nil {
		return nil, nil, err
	}

	// prevent nil pointer dereference error.
	if supplementaryData == nil {
		return nil, nil, fmt.Errorf("Supplementary data is nil")
	}

	newPrimaryData, newSurveyData := preparePrimaryData(request, supplementaryData)

	updatedData, err := service.repo.UpdateByID(
		ctx,
		existingData,
		newPrimaryData,
		request.ConcernIDs,
		request.CategoryIDs,
		request.SurveyQuestions,
	)

	if err != nil {
		return nil, nil, err
	}

	supplementaryData.SurveyQuestions = newSurveyData

	return updatedData, supplementaryData, nil
}

func (service *treatmentProductService) DeleteByID(
	ctx context.Context,
	id *string,
) (*domain.TreatmentProduct, error) {
	data, err := service.repo.DeleteByID(ctx, id)

	switch {
	case err != nil:
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return data, nil
}

func (service *treatmentProductService) GetMany(
	ctx context.Context,
	filter *domain.TreatmentProductFilter,
) ([]domain.TreatmentProductGetMany, int, error) {
	data, totalData, err := service.repo.GetMany(ctx, filter)

	if err != nil {
		return nil, 0, err
	}

	for i := 0; i < len(data); i++ {
		if data[i].MediaUrl != nil {
			bucketName := os.Getenv("AWS_S3_BUCKET")

			presignedHttpRequest, err := service.presigner.GetObject(
				ctx,
				bucketName,
				*data[i].MediaUrl,
				3600,
			)

			if err != nil {
				log.Error().Err(err).Msg("Failed to get presigned URL")
				return nil, 0, err
			}

			data[i].MediaUrl = &presignedHttpRequest.URL
		}

		if data[i].ThumbnailUrl != nil {
			bucketName := os.Getenv("AWS_S3_BUCKET")

			presignedHttpRequest, err := service.presigner.GetObject(
				ctx,
				bucketName,
				*data[i].ThumbnailUrl,
				3600,
			)

			if err != nil {
				log.Error().Err(err).Msg("Failed to get presigned URL")
				return nil, 0, err
			}

			data[i].ThumbnailUrl = &presignedHttpRequest.URL
		}
	}

	return data, totalData, err
}
