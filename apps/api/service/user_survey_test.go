package service_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api/domain"
	"api/service"
	"api/service/mocks"
	"api/utils"
)

func TestUserSurveyService(test *testing.T) {
	var (
		ctx      = context.Background()
		mockRepo = new(mocks.UserSurveyRepository)
		svc      = service.NewUserSurveyService(mockRepo)
	)

	var (
		sharedRequest = domain.UserSurveyRequest{
			UserID: &utils.DummyID,
			Results: domain.UserSurveyResultJSON{
				{
					Question: "Masalah Kulit apa yang ingin diselesaikan?",
					Answers:  []string{"Wrinkle", "Dark spot", "Acne"},
				},
			},
		}

		sharedData = domain.UserSurvey{
			UserID: &utils.DummyID,
			Results: domain.UserSurveyResultJSON{
				{
					Question: "Masalah Kulit apa yang ingin diselesaikan?",
					Answers:  []string{"Wrinkle", "Dark spot", "Acne"},
				},
			},
		}

		sharedFilterData = domain.UserSurveyFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
			UserID: utils.DummyID,
		}
	)

	test.Run("Success create data", func(t *testing.T) {
		mockRepo.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.UserSurveyRequest"),
		).
			Return(&sharedData, nil).
			Once()

		data, err := svc.Create(
			ctx,
			&sharedRequest,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed create data", func(t *testing.T) {
		mockRepo.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.UserSurveyRequest"),
		).
			Return(nil, fmt.Errorf("Error in Create repo")).
			Once()

		data, err := svc.Create(
			ctx,
			&sharedRequest,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success get data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedData, nil).
			Once()

		data, err := svc.GetByID(
			ctx,
			&sharedData.ID,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed get data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(nil, fmt.Errorf("Error in GetByID repo")).
			Once()

		data, err := svc.GetByID(
			ctx,
			&sharedData.ID,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success get many data", func(t *testing.T) {
		mockRepo.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.UserSurveyFilter"),
		).
			Return([]domain.UserSurvey{sharedData}, 1, nil).
			Once()

		data, totalData, err := svc.GetMany(
			ctx,
			&sharedFilterData,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.NotEmpty(t, totalData)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed get many data", func(t *testing.T) {
		mockRepo.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.UserSurveyFilter"),
		).
			Return(nil, 0, fmt.Errorf("Error in GetMany repo")).
			Once()

		data, totalData, err := svc.GetMany(
			ctx,
			&sharedFilterData,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		assert.Empty(t, totalData)
		mockRepo.AssertExpectations(t)
	})
}
