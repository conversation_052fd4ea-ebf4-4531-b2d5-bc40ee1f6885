package service

import (
	"context"
	"fmt"
	"os"
	"slices"
	"strings"

	"github.com/rs/zerolog/log"

	"api/domain"
)

//go:generate mockery
type SkinDetectionHttp interface {
	DetectWrinkles(
		ctx context.Context,
		data *domain.SkinDetectionMLRequest,
	) (*domain.SkinDetectionMLResponse, error)
	DetectPores(
		ctx context.Context,
		data *domain.SkinDetectionMLRequest,
	) (*domain.SkinDetectionMLResponse, error)
}

type skinDetectionService struct {
	repo      SkinAnalyzeRepository
	http      SkinDetectionHttp
	presigner Presigner
}

func NewSkinDetectionService(
	repo SkinAnalyzeRepository,
	http SkinDetectionHttp,
	presigner Presigner,
) *skinDetectionService {
	return &skinDetectionService{repo, http, presigner}
}

func (service *skinDetectionService) DetectWrinkles(
	ctx context.Context,
	id string,
	queryParams *domain.SkinDetectionQueryParams,
) (*domain.SkinDetectionResponse, error) {
	skinAnalyze, err := service.repo.GetSkinAnalyzeByID(ctx, id)

	if err != nil {
		return nil, err
	}

	filename := fmt.Sprintf("face_aging_%s.png", queryParams.Concern)

	var faceAgingImagePath string
	for _, path := range skinAnalyze.PathImages {
		if strings.Contains(path, filename) {
			faceAgingImagePath = path
			break
		}
	}

	if faceAgingImagePath == "" {
		return nil, fmt.Errorf("File %s is not found", filename)
	}

	mlRequest := domain.SkinDetectionMLRequest{
		ImagePath: faceAgingImagePath,
	}

	wrinkleDetectionOutput, err := service.http.DetectWrinkles(ctx, &mlRequest)

	if err != nil {
		return nil, err
	}

	updatedSkinAnalyze := skinAnalyze

	if !slices.Contains(
		updatedSkinAnalyze.PathImages,
		wrinkleDetectionOutput.ImagePath,
	) {
		updatedSkinAnalyze.PathImages = append(
			updatedSkinAnalyze.PathImages,
			wrinkleDetectionOutput.ImagePath,
		)

		_, err := service.repo.UpdateSkinAnalyzeByID(
			ctx,
			skinAnalyze.ID,
			updatedSkinAnalyze,
		)

		if err != nil {
			log.Error().Err(err).Msg("Error updated skin analyze data")
			return nil, err
		}
	}

	bucketName := os.Getenv("AWS_S3_BUCKET")

	presignedURL, err := service.presigner.GetObject(
		ctx,
		bucketName,
		wrinkleDetectionOutput.ImagePath,
		3600,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to presign URL")
		return nil, err
	}

	output := domain.SkinDetectionResponse{
		GeneratedImageURL: presignedURL.URL,
	}

	return &output, nil
}

func (service *skinDetectionService) DetectPores(
	ctx context.Context,
	id string,
	queryParams *domain.SkinDetectionQueryParams,
) (*domain.SkinDetectionResponse, error) {
	skinAnalyze, err := service.repo.GetSkinAnalyzeByID(ctx, id)

	if err != nil {
		return nil, err
	}

	filename := fmt.Sprintf("face_aging_%s.png", queryParams.Concern)

	var faceAgingImagePath string
	for _, path := range skinAnalyze.PathImages {
		if strings.Contains(path, filename) {
			faceAgingImagePath = path
			break
		}
	}

	if faceAgingImagePath == "" {
		return nil, fmt.Errorf("File %s is not found", filename)
	}

	mlRequest := domain.SkinDetectionMLRequest{
		ImagePath: faceAgingImagePath,
	}

	poreDetectionOutput, err := service.http.DetectPores(ctx, &mlRequest)

	if err != nil {
		return nil, err
	}

	updatedSkinAnalyze := skinAnalyze

	if !slices.Contains(
		updatedSkinAnalyze.PathImages,
		poreDetectionOutput.ImagePath,
	) {
		updatedSkinAnalyze.PathImages = append(
			updatedSkinAnalyze.PathImages,
			poreDetectionOutput.ImagePath,
		)

		_, err := service.repo.UpdateSkinAnalyzeByID(
			ctx,
			skinAnalyze.ID,
			updatedSkinAnalyze,
		)

		if err != nil {
			log.Error().Err(err).Msg("Error updated skin analyze data")
			return nil, err
		}
	}

	bucketName := os.Getenv("AWS_S3_BUCKET")

	presignedURL, err := service.presigner.GetObject(
		ctx,
		bucketName,
		poreDetectionOutput.ImagePath,
		3600,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to presign URL")
		return nil, err
	}

	output := domain.SkinDetectionResponse{
		GeneratedImageURL: presignedURL.URL,
	}

	return &output, nil
}
