package service_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api/domain"
	"api/service"
	"api/service/mocks"
	"api/utils"
)

func TestSkinProblemService(t *testing.T) {
	var (
		ctx      = context.Background()
		mockRepo = new(mocks.SkinProblemRepository)
		svc      = service.NewSkinProblemService(mockRepo)
	)

	// Copy the value, not the pointer.
	var (
		sharedRequest = domain.SkinProblemRequest{
			Name: "Test create skin problem",
		}

		sharedData = domain.SkinProblem{
			Name: sharedRequest.Name,
		}

		sharedFilterData = domain.SkinProblemFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
		}

		sharedIndication = domain.SkinProblemIndication{
			Name: "Porphyrin",
		}

		sharedResponse = domain.SkinProblemResponse{
			Name:                   sharedRequest.Name,
			SkinProblemIndications: []domain.SkinProblemIndication{sharedIndication},
		}
	)

	t.Run("Success create data", func(t *testing.T) {
		mockRepo.On(
			"GetSkinProblemIndications",
			mock.Anything,
			mock.AnythingOfType("[]string"),
		).
			Return(sharedResponse.SkinProblemIndications, nil).
			Once()

		mockRepo.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.SkinProblemRequest"),
		).
			Return(&sharedData, nil).
			Once()

		skinProblemData, skinProblemIndicationData, err := svc.Create(
			ctx,
			&sharedRequest,
		)

		assert.NoError(t, err)
		assert.NotNil(t, skinProblemData)
		assert.NotNil(t, skinProblemIndicationData)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Failed create data", func(t *testing.T) {
		mockRepo.On(
			"GetSkinProblemIndications",
			mock.Anything,
			mock.AnythingOfType("[]string"),
		).
			Return(sharedResponse.SkinProblemIndications, nil).
			Once()

		mockRepo.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.SkinProblemRequest"),
		).
			Return(nil, fmt.Errorf("Error in Create repo")).
			Once()

		skinProblemData, skinProblemIndicationData, err := svc.Create(
			ctx,
			&sharedRequest,
		)

		assert.Error(t, err)
		assert.Nil(t, skinProblemData)
		assert.Nil(t, skinProblemIndicationData)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Success get data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedResponse, nil).
			Once()

		data, err := svc.GetByID(
			ctx,
			&utils.DummyID,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Failed get data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(nil, fmt.Errorf("Error in GetByID repo")).
			Once()

		data, err := svc.GetByID(
			ctx,
			&utils.DummyID,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Success update data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedResponse, nil).
			Once()

		mockRepo.On(
			"GetSkinProblemIndications",
			mock.Anything,
			mock.AnythingOfType("[]string"),
		).
			Return(sharedResponse.SkinProblemIndications, nil).
			Once()

		mockRepo.On(
			"UpdateByID",
			mock.Anything,
			mock.AnythingOfType("*domain.SkinProblemResponse"),
			mock.AnythingOfType("*domain.SkinProblemRequest"),
		).
			Return(&sharedData, nil).
			Once()

		skinProblemData, skinProblemIndicationData, err := svc.UpdateByID(
			ctx,
			&utils.DummyID,
			&sharedRequest,
		)

		assert.NoError(t, err)
		assert.NotNil(t, skinProblemData)
		assert.NotNil(t, skinProblemIndicationData)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Failed update data by id", func(t *testing.T) {
		t.Run("Error in existing data", func(t *testing.T) {
			mockRepo.On(
				"GetByID",
				mock.Anything,
				mock.AnythingOfType("*string"),
			).
				Return(nil, fmt.Errorf("Error in GetByID repo")).
				Once()

			skinProblemData, skinProblemIndicationData, err := svc.UpdateByID(
				ctx,
				&utils.DummyID,
				&sharedRequest,
			)

			assert.Error(t, err)
			assert.Nil(t, skinProblemData)
			assert.Nil(t, skinProblemIndicationData)
			mockRepo.AssertExpectations(t)
		})

		t.Run("Error in update repo", func(t *testing.T) {
			mockRepo.On(
				"GetByID",
				mock.Anything,
				mock.AnythingOfType("*string"),
			).
				Return(&sharedResponse, nil).
				Once()

			mockRepo.On(
				"GetSkinProblemIndications",
				mock.Anything,
				mock.AnythingOfType("[]string"),
			).
				Return(sharedResponse.SkinProblemIndications, nil).
				Once()

			mockRepo.On(
				"UpdateByID",
				mock.Anything,
				mock.AnythingOfType("*domain.SkinProblemResponse"),
				mock.AnythingOfType("*domain.SkinProblemRequest"),
			).
				Return(&sharedData, fmt.Errorf("Error in UpdateByID repo")).
				Once()

			skinProblemData, skinProblemIndicationData, err := svc.UpdateByID(
				ctx,
				&utils.DummyID,
				&sharedRequest,
			)

			assert.Error(t, err)
			assert.Nil(t, skinProblemData)
			assert.Nil(t, skinProblemIndicationData)
			mockRepo.AssertExpectations(t)
		})
	})

	t.Run("Success delete data by id", func(t *testing.T) {
		mockRepo.On(
			"DeleteByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedData, nil).
			Once()

		data, err := svc.DeleteByID(
			ctx,
			&utils.DummyID,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Failed delete data by id", func(t *testing.T) {
		mockRepo.On(
			"DeleteByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(nil, fmt.Errorf("Error in DeleteByID repo")).
			Once()

		data, err := svc.DeleteByID(
			ctx,
			&utils.DummyID,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Success get many data", func(t *testing.T) {
		mockRepo.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.SkinProblemFilter"),
		).
			Return(
				[]domain.SkinProblemResponse{
					{
						ID:                     utils.DummyID,
						Name:                   sharedRequest.Name,
						SkinProblemIndications: nil,
					},
				},
				1,
				nil,
			).
			Once()

		data, totalData, err := svc.GetMany(
			ctx,
			&sharedFilterData,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.NotEmpty(t, totalData)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Failed get many data", func(t *testing.T) {
		mockRepo.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.SkinProblemFilter"),
		).
			Return(nil, 0, fmt.Errorf("Error in GetMany repo")).
			Once()

		data, totalData, err := svc.GetMany(
			ctx,
			&sharedFilterData,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		assert.Empty(t, totalData)
		mockRepo.AssertExpectations(t)
	})
}
