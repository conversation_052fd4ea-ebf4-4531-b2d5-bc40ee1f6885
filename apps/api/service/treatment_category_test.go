package service_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api/domain"
	"api/service"
	"api/service/mocks"
	"api/utils"
)

func TestTreatmentCategoryService(test *testing.T) {
	var (
		ctx      = context.Background()
		mockRepo = new(mocks.TreatmentCategoryRepository)
		svc      = service.NewTreatmentCategoryService(mockRepo)
	)

	// Copy the value, not the pointer.
	var (
		sharedData = domain.TreatmentCategory{
			Name: "Test create treatment category",
		}

		sharedFilterData = domain.TreatmentCategoryFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
		}
	)

	test.Run("Success create data", func(t *testing.T) {
		mockRepo.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentCategory"),
		).
			Return(nil).
			Once()

		data, err := svc.Create(
			ctx,
			&sharedData,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed create data", func(t *testing.T) {
		mockRepo.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentCategory"),
		).
			Return(fmt.Errorf("Error in Create repo")).
			Once()

		data, err := svc.Create(
			ctx,
			&sharedData,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success get data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedData, nil).
			Once()

		data, err := svc.GetByID(
			ctx,
			&sharedData.ID,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed get data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(nil, fmt.Errorf("Error in GetByID repo")).
			Once()

		data, err := svc.GetByID(
			ctx,
			&sharedData.ID,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success update data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedData, nil).
			Once()

		mockRepo.On(
			"UpdateByID",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentCategory"),
			mock.AnythingOfType("*domain.TreatmentCategory"),
		).
			Return(nil).
			Once()

		data, err := svc.UpdateByID(
			ctx,
			&sharedData,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed update data by id", func(t *testing.T) {
		t.Run("Error in existing data", func(t *testing.T) {
			mockRepo.On(
				"GetByID",
				mock.Anything,
				mock.AnythingOfType("*string"),
			).
				Return(nil, fmt.Errorf("Error in GetByID repo")).
				Once()

			data, err := svc.UpdateByID(
				ctx,
				&sharedData,
			)

			assert.Error(t, err)
			assert.Nil(t, data)
			mockRepo.AssertExpectations(t)
		})

		t.Run("Error in update repo", func(t *testing.T) {
			mockRepo.On(
				"GetByID",
				mock.Anything,
				mock.AnythingOfType("*string"),
			).
				Return(&sharedData, nil).
				Once()

			mockRepo.On(
				"UpdateByID",
				mock.Anything,
				mock.AnythingOfType("*domain.TreatmentCategory"),
				mock.AnythingOfType("*domain.TreatmentCategory"),
			).
				Return(fmt.Errorf("Error in UpdateByID repo")).
				Once()

			data, err := svc.UpdateByID(
				ctx,
				&sharedData,
			)

			assert.Error(t, err)
			assert.Nil(t, data)
			mockRepo.AssertExpectations(t)
		})
	})

	test.Run("Success delete data by id", func(t *testing.T) {
		mockRepo.On(
			"DeleteByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedData, nil).
			Once()

		data, err := svc.DeleteByID(
			ctx,
			&utils.DummyID,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed delete data by id", func(t *testing.T) {
		mockRepo.On(
			"DeleteByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(nil, fmt.Errorf("Error in DeleteByID repo")).
			Once()

		data, err := svc.DeleteByID(
			ctx,
			&utils.DummyID,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success get many data", func(t *testing.T) {
		mockRepo.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentCategoryFilter"),
		).
			Return([]domain.TreatmentCategory{sharedData}, 1, nil).
			Once()

		data, totalData, err := svc.GetMany(
			ctx,
			&sharedFilterData,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.NotEmpty(t, totalData)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed get many data", func(t *testing.T) {
		mockRepo.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentCategoryFilter"),
		).
			Return(nil, 0, fmt.Errorf("Error in GetMany repo")).
			Once()

		data, totalData, err := svc.GetMany(
			ctx,
			&sharedFilterData,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		assert.Empty(t, totalData)
		mockRepo.AssertExpectations(t)
	})
}
