package service

import (
	"context"
	"fmt"
	"os"

	"github.com/rs/zerolog/log"

	"api/domain"
)

//go:generate mockery
type SurveyRepository interface {
	Create(ctx context.Context, data *domain.Survey) (*domain.Survey, error)
	CreateNested(
		ctx context.Context,
		data *domain.SurveyRequestNested,
	) (*domain.SurveyResponseNested, error)
	GetByID(ctx context.Context, id *string) (*domain.Survey, error)
	GetByIDNested(
		ctx context.Context,
		id *string,
	) (*domain.SurveyResponseNested, error)
	UpdateByID(
		ctx context.Context,
		existingData *domain.Survey,
		newData *domain.Survey,
	) (*domain.Survey, error)
	UpdateByIDNested(
		ctx context.Context,
		id *string,
		data *domain.SurveyRequestNested,
	) (*domain.SurveyResponseNested, error)
	DeleteByID(ctx context.Context, id *string) (*domain.Survey, error)
	GetMany(
		ctx context.Context,
		filter *domain.SurveyFilter,
	) ([]domain.Survey, int, error)
	GetManyNested(
		ctx context.Context,
		filter *domain.SurveyFilterNested,
	) ([]domain.SurveyResponseNested, int, error)
}

type surveyService struct {
	repo      SurveyRepository
	presigner Presigner
}

func NewSurveyService(
	repo SurveyRepository,
	presigner Presigner,
) *surveyService {
	return &surveyService{repo, presigner}
}

func checkParentQuestionAnswerIndex(
	childQuestions []domain.SurveyRequestChildQuestion,
	answers domain.SurveyAnswerJSON,
) error {
	for i, childQuestion := range childQuestions {
		if childQuestion.ParentQuestionAnswer >= len(answers) {
			// Prevent confusion which child question should be changed by end user,
			// so start indexing from 1.
			currentQuestionIndex := i + 1
			return fmt.Errorf(
				"Child question %d ParentQuestionAnswer index out of bounds",
				currentQuestionIndex,
			)
		}
	}

	return nil
}

func (service *surveyService) Create(
	ctx context.Context,
	data *domain.Survey,
) (*domain.Survey, error) {
	newData, err := service.repo.Create(ctx, data)

	if err != nil {
		return nil, err
	}

	return newData, nil
}

func (service *surveyService) CreateNested(
	ctx context.Context,
	data *domain.SurveyRequestNested,
) (*domain.SurveyResponseNested, error) {
	err := checkParentQuestionAnswerIndex(data.ChildQuestions, data.Answers)

	if err != nil {
		return nil, err
	}

	newData, err := service.repo.CreateNested(ctx, data)

	if err != nil {
		return nil, err
	}

	return newData, nil
}

func (service *surveyService) GetByID(
	ctx context.Context,
	id *string,
) (*domain.Survey, error) {
	data, err := service.repo.GetByID(ctx, id)

	switch {
	case err != nil:
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	for i := 0; i < len(data.Answers); i++ {
		if data.Answers[i].ImageUrl != nil {
			bucketName := os.Getenv("AWS_S3_BUCKET")

			presignedHttpRequest, err := service.presigner.GetObject(
				ctx,
				bucketName,
				*data.Answers[i].ImageUrl,
				3600,
			)

			if err != nil {
				log.Error().Err(err).Msg("Failed to get presigned URL")
				return nil, err
			}

			data.Answers[i].ImageUrl = &presignedHttpRequest.URL
		}
	}

	return data, nil
}

func (service *surveyService) GetByIDNested(
	ctx context.Context,
	id *string,
) (*domain.SurveyResponseNested, error) {
	data, err := service.repo.GetByIDNested(ctx, id)

	switch {
	case err != nil:
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	for i := 0; i < len(data.Answers); i++ {
		if data.Answers[i].ImageUrl != nil {
			bucketName := os.Getenv("AWS_S3_BUCKET")

			presignedHttpRequest, err := service.presigner.GetObject(
				ctx,
				bucketName,
				*data.Answers[i].ImageUrl,
				3600,
			)

			if err != nil {
				log.Error().Err(err).Msg("Failed to get presigned URL")
				return nil, err
			}

			data.Answers[i].ImageUrl = &presignedHttpRequest.URL
		}
	}

	for i := 0; i < len(data.ChildQuestions); i++ {
		for j := 0; j < len(data.ChildQuestions[i].Answers); j++ {
			if data.ChildQuestions[i].Answers[j].ImageUrl != nil {
				bucketName := os.Getenv("AWS_S3_BUCKET")

				presignedHttpRequest, err := service.presigner.GetObject(
					ctx,
					bucketName,
					*data.ChildQuestions[i].Answers[j].ImageUrl,
					3600,
				)

				if err != nil {
					log.Error().Err(err).Msg("Failed to get presigned URL")
					return nil, err
				}

				data.ChildQuestions[i].Answers[j].ImageUrl = &presignedHttpRequest.URL
			}
		}
	}

	return data, nil
}

func (service *surveyService) UpdateByID(
	ctx context.Context,
	input *domain.Survey,
) (*domain.Survey, error) {
	existingData, err := service.GetByID(ctx, &input.ID)

	if err != nil {
		return nil, err
	}

	updatedData, err := service.repo.UpdateByID(
		ctx,
		existingData,
		input,
	)

	if err != nil {
		return nil, err
	}

	return updatedData, nil
}

func (service *surveyService) UpdateByIDNested(
	ctx context.Context,
	id *string,
	data *domain.SurveyRequestNested,
) (*domain.SurveyResponseNested, error) {
	err := checkParentQuestionAnswerIndex(data.ChildQuestions, data.Answers)

	if err != nil {
		return nil, err
	}

	result, err := service.repo.UpdateByIDNested(ctx, id, data)

	switch {
	case err != nil:
		return nil, err
	case result == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return result, nil
}

func (service *surveyService) DeleteByID(
	ctx context.Context,
	id *string,
) (*domain.Survey, error) {
	data, err := service.repo.DeleteByID(ctx, id)

	switch {
	case err != nil:
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return data, nil
}

func (service *surveyService) GetMany(
	ctx context.Context,
	filter *domain.SurveyFilter,
) ([]domain.Survey, int, error) {
	data, totalData, err := service.repo.GetMany(ctx, filter)

	if err != nil {
		return nil, 0, err
	}

	for i := 0; i < len(data); i++ {
		for j := 0; j < len(data[i].Answers); j++ {
			if data[i].Answers[j].ImageUrl != nil {
				bucketName := os.Getenv("AWS_S3_BUCKET")

				presignedHttpRequest, err := service.presigner.GetObject(
					ctx,
					bucketName,
					*data[i].Answers[j].ImageUrl,
					3600,
				)

				if err != nil {
					log.Error().Err(err).Msg("Failed to get presigned URL")
					return nil, 0, err
				}

				data[i].Answers[j].ImageUrl = &presignedHttpRequest.URL
			}
		}
	}

	return data, totalData, nil
}

func (service *surveyService) GetManyNested(
	ctx context.Context,
	filter *domain.SurveyFilterNested,
) ([]domain.SurveyResponseNested, int, error) {
	data, totalData, err := service.repo.GetManyNested(ctx, filter)

	if err != nil {
		return nil, 0, err
	}

	for i := 0; i < len(data); i++ {
		for j := 0; j < len(data[i].Answers); j++ {
			if data[i].Answers[j].ImageUrl != nil {
				bucketName := os.Getenv("AWS_S3_BUCKET")

				presignedHttpRequest, err := service.presigner.GetObject(
					ctx,
					bucketName,
					*data[i].Answers[j].ImageUrl,
					3600,
				)

				if err != nil {
					log.Error().Err(err).Msg("Failed to get presigned URL")
					return nil, 0, err
				}

				data[i].Answers[j].ImageUrl = &presignedHttpRequest.URL
			}
		}

		for j := 0; j < len(data[i].ChildQuestions); j++ {
			for k := 0; k < len(data[i].ChildQuestions[j].Answers); k++ {
				if data[i].ChildQuestions[j].Answers[k].ImageUrl != nil {
					bucketName := os.Getenv("AWS_S3_BUCKET")

					presignedHttpRequest, err := service.presigner.GetObject(
						ctx,
						bucketName,
						*data[i].ChildQuestions[j].Answers[k].ImageUrl,
						3600,
					)

					if err != nil {
						log.Error().Err(err).Msg("Failed to get presigned URL")
						return nil, 0, err
					}

					data[i].ChildQuestions[j].Answers[k].ImageUrl = &presignedHttpRequest.URL
				}
			}
		}
	}

	return data, totalData, nil
}
