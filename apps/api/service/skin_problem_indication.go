package service

import (
	"context"
	"fmt"

	"github.com/rs/zerolog/log"

	"api/domain"
)

//go:generate mockery
type SkinProblemIndicationRepository interface {
	Create(
		ctx context.Context,
		request *domain.SkinProblemIndicationRequest,
	) (*domain.SkinProblemIndication, error)
	GetByID(
		ctx context.Context,
		id *string,
	) (*domain.SkinProblemIndication, error)
	UpdateByID(
		ctx context.Context,
		id *string,
		request *domain.SkinProblemIndicationRequest,
	) (*domain.SkinProblemIndication, error)
	DeleteByID(
		ctx context.Context,
		id *string,
	) (*domain.SkinProblemIndication, error)
	GetMany(
		ctx context.Context,
		filter *domain.SkinProblemIndicationFilter,
	) ([]domain.SkinProblemIndication, int, error)
}

type skinProblemIndicationService struct {
	repo SkinProblemIndicationRepository
}

func NewSkinProblemIndicationService(
	repo SkinProblemIndicationRepository,
) *skinProblemIndicationService {
	return &skinProblemIndicationService{repo}
}

func (service *skinProblemIndicationService) Create(
	ctx context.Context,
	request *domain.SkinProblemIndicationRequest,
) (*domain.SkinProblemIndication, error) {
	data, err := service.repo.Create(ctx, request)

	if err != nil {
		log.Error().Err(err).Msg("Failed to create skin problem indication")
		return nil, err
	}

	return data, nil
}

func (service *skinProblemIndicationService) GetByID(
	ctx context.Context,
	id *string,
) (*domain.SkinProblemIndication, error) {
	data, err := service.repo.GetByID(ctx, id)

	switch {
	case err != nil:
		log.Error().Err(err).Msg("Failed to get skin problem indication by id result")
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return data, nil
}

func (service *skinProblemIndicationService) UpdateByID(
	ctx context.Context,
	id *string,
	request *domain.SkinProblemIndicationRequest,
) (*domain.SkinProblemIndication, error) {
	updatedData, err := service.repo.UpdateByID(
		ctx,
		id,
		request,
	)

	switch {
	case err != nil:
		log.Error().Err(err).Msg("Failed to update skin problem indication by id")
		return nil, err
	case updatedData == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return updatedData, nil
}

func (service *skinProblemIndicationService) DeleteByID(
	ctx context.Context,
	id *string,
) (*domain.SkinProblemIndication, error) {
	data, err := service.repo.DeleteByID(ctx, id)

	switch {
	case err != nil:
		log.Error().Err(err).Msg("Failed to delete skin problem indication by id")
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return data, nil
}

func (service *skinProblemIndicationService) GetMany(
	ctx context.Context,
	filter *domain.SkinProblemIndicationFilter,
) ([]domain.SkinProblemIndication, int, error) {
	data, totalData, err := service.repo.GetMany(ctx, filter)

	if err != nil {
		return nil, 0, err
	}

	return data, totalData, nil
}
