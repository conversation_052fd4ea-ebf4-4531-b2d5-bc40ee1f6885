package service

import (
	"api/domain"
	"context"

	"github.com/rs/zerolog/log"
)

type SummaryHttp interface {
	GetSummary(ctx context.Context, data *domain.GetSummaryMLRequest) (*domain.SummaryResponse, error)
}

type summaryService struct {
	summaryHttp SummaryHttp
	saRepo SkinAnalyzeRepository
}

func NewSummaryService(summaryHttp SummaryHttp, saRepo SkinAnalyzeRepository) *summaryService {
	return &summaryService{summaryHttp, saRepo}
}


func (s *summaryService) GetSummary(
	ctx context.Context,
	id string,
) (*domain.SummaryResponse, error) {
	skinAnalyze, err := s.saRepo.GetSkinAnalyzeByID(ctx, id)
	if err != nil {
		log.Error().Err(err).Msg("failed to get skin analyze by id")
		return nil, err
	}

	mlRequest := domain.GetSummaryMLRequest{
		SkinAnalyze: *skinAnalyze,
	}

	summary, err := s.summaryHttp.GetSummary(ctx, &mlRequest)
	if err != nil {
		log.Error().Err(err).Msg("failed to get summary")
		return nil, err
	}

	return summary, nil
}
