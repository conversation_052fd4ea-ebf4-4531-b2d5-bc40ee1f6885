package service_test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api/domain"
	"api/service"
	"api/service/mocks"
	"api/utils"
)

func TestTreatmentProductService(test *testing.T) {
	var (
		ctx           = context.Background()
		mockRepo      = new(mocks.TreatmentProductRepository)
		mockPresigner = new(mocks.Presigner)
		svc           = service.NewTreatmentProductService(mockRepo, mockPresigner)
	)

	// Copy the value, not the pointer.
	var (
		surveyQuestions = []domain.TreatmentProductSurveyQuestion{
			{
				Question: "Apakah hidup anda tenang?",
				Answers: []domain.SurveyAnswer{
					{Title: "Ya"},
					{Title: "Tidak"},
				},
				SelectedAnswer: 1,
				QuestionOrder:  0,
			},
		}

		testItemCode              = "UWU69420"
		testNotes                 = "Test notes from doctor"
		mediaUrl                  = "https://example.com/s3-object"
		thumbnailUrl              = "https://example.com/s3-object"
		durationTopRecommendation = time.Now().UnixNano()

		sharedRequest = domain.TreatmentProductRequest{
			ItemCode:                  testItemCode,
			Name:                      "Test Treatment Product",
			Type:                      domain.TreatmentType,
			Description:               "Dummy data test",
			Price:                     69420,
			IntervalID:                &utils.DummyID,
			ConcernIDs:                []string{utils.DummyID},
			Notes:                     &testNotes,
			Quantity:                  69,
			MediaUrl:                  &mediaUrl,
			ThumbnailUrl:              &thumbnailUrl,
			IsTopRecommendation:       true,
			DurationTopRecommendation: &durationTopRecommendation,
			SurveyQuestions: []domain.TreatmentProductSurveyQuestionInput{
				{
					SurveyQuestionID: utils.DummyID,
					SelectedAnswer:   1,
				},
			},
		}

		category = domain.TreatmentCategory{Name: "Test treatment product category"}
		interval = domain.TreatmentInterval{Days: 69420}

		sharedSupplementaryData = domain.TreatmentProductSupplementaryData{
			Category: []domain.TreatmentCategory{category},
			Interval: &interval,
			Concern: []domain.SkinProblem{
				{Name: "test acne"},
				{Name: "test pore"},
				{Name: "test wrinkle"},
			},
			SurveyQuestions: surveyQuestions,
		}

		sharedData = domain.TreatmentProduct{
			ItemCode:     testItemCode,
			Name:         "Test Treatment Product",
			Type:         domain.TreatmentType,
			Description:  "Dummy data test",
			Price:        69420,
			IntervalID:   &utils.DummyID,
			Notes:        sharedRequest.Notes,
			MediaUrl:     sharedRequest.MediaUrl,
			ThumbnailUrl: sharedRequest.ThumbnailUrl,
		}

		sharedResponse = domain.TreatmentProductResponse{
			ItemCode:                          testItemCode,
			Name:                              "Test Treatment Product",
			Type:                              domain.TreatmentType,
			Description:                       "Dummy data test",
			Price:                             69420,
			MediaUrl:                          sharedRequest.MediaUrl,
			ThumbnailUrl:                      sharedRequest.ThumbnailUrl,
			Notes:                             sharedRequest.Notes,
			TreatmentProductSupplementaryData: sharedSupplementaryData,
		}

		sharedFilterData = domain.TreatmentProductFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
			SortColumn: "created_at",
			SortOrder:  "asc",
		}
	)

	presignedHttpRequest := v4.PresignedHTTPRequest{
		URL:    "https://example.com/presigned-url",
		Method: http.MethodGet,
	}

	test.Run("Success create data", func(t *testing.T) {
		mockRepo.On(
			"GetSupplementaryData",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProductRequest"),
		).
			Return(
				&sharedSupplementaryData,
				nil,
			).
			Once()

		mockRepo.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProduct"),
			mock.AnythingOfType("[]string"),
			mock.AnythingOfType("[]string"),
			mock.AnythingOfType("[]domain.TreatmentProductSurveyQuestionInput"),
		).
			Return(&sharedData, nil).
			Once()

		primaryData, supplementaryData, err := svc.Create(ctx, &sharedRequest)

		assert.NoError(t, err)
		assert.NotNil(t, primaryData)
		assert.NotNil(t, supplementaryData)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed create data in supplementary data", func(t *testing.T) {
		mockRepo.On(
			"GetSupplementaryData",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProductRequest"),
		).
			Return(
				nil,
				fmt.Errorf("Error supplementary data"),
			).
			Once()

		primaryData, supplementaryData, err := svc.Create(ctx, &sharedRequest)

		assert.Error(t, err)
		assert.Nil(t, primaryData)
		assert.Nil(t, supplementaryData)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed create data in primary data", func(t *testing.T) {
		mockRepo.On(
			"GetSupplementaryData",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProductRequest"),
		).
			Return(
				&sharedSupplementaryData,
				nil,
			).
			Once()

		mockRepo.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProduct"),
			mock.AnythingOfType("[]string"),
			mock.AnythingOfType("[]string"),
			mock.AnythingOfType("[]domain.TreatmentProductSurveyQuestionInput"),
		).
			Return(nil, fmt.Errorf("Error primary data")).
			Once()

		primaryData, supplementaryData, err := svc.Create(ctx, &sharedRequest)

		assert.Error(t, err)
		assert.Nil(t, primaryData)
		assert.Nil(t, supplementaryData)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success get data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedResponse, nil).
			Once()

		// Mock presigner for media url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		// Mock presigner for thumbnail url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		data, err := svc.GetByID(
			ctx,
			&sharedData.ID,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed get data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(nil, fmt.Errorf("Error in GetByID repo")).
			Once()

		data, err := svc.GetByID(
			ctx,
			&sharedData.ID,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success update data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedResponse, nil).
			Once()

		// Mock presigner for media url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		// Mock presigner for thumbnail url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		mockRepo.On(
			"GetSupplementaryData",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProductRequest"),
		).
			Return(
				&sharedSupplementaryData,
				nil,
			).
			Once()

		mockRepo.On(
			"UpdateByID",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProductResponse"),
			mock.AnythingOfType("*domain.TreatmentProduct"),
			mock.AnythingOfType("[]string"),
			mock.AnythingOfType("[]string"),
			mock.AnythingOfType("[]domain.TreatmentProductSurveyQuestionInput"),
		).
			Return(&sharedData, nil).
			Once()

		updatedData, supplementaryData, err := svc.UpdateByID(
			ctx,
			&utils.DummyID,
			&sharedRequest,
		)

		assert.NoError(t, err)
		assert.NotNil(t, updatedData)
		assert.NotNil(t, supplementaryData)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed update data by id not found", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(nil, fmt.Errorf("ID not found")).
			Once()

		updatedData, supplementaryData, err := svc.UpdateByID(
			ctx,
			&utils.DummyID,
			&sharedRequest,
		)

		assert.Error(t, err)
		assert.Nil(t, updatedData)
		assert.Nil(t, supplementaryData)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed update data by id get supplementary", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedResponse, nil).
			Once()

		// Mock presigner for media url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		// Mock presigner for thumbnail url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		mockRepo.On(
			"GetSupplementaryData",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProductRequest"),
		).
			Return(nil, fmt.Errorf("Error supplementary data")).
			Once()

		updatedData, supplementaryData, err := svc.UpdateByID(
			ctx,
			&utils.DummyID,
			&sharedRequest,
		)

		assert.Error(t, err)
		assert.Nil(t, updatedData)
		assert.Nil(t, supplementaryData)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed update data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedResponse, nil).
			Once()

		// Mock presigner for media url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		// Mock presigner for thumbnail url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		mockRepo.On(
			"GetSupplementaryData",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProductRequest"),
		).
			Return(
				&sharedSupplementaryData,
				nil,
			).
			Once()

		mockRepo.On(
			"UpdateByID",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProductResponse"),
			mock.AnythingOfType("*domain.TreatmentProduct"),
			mock.AnythingOfType("[]string"),
			mock.AnythingOfType("[]string"),
			mock.AnythingOfType("[]domain.TreatmentProductSurveyQuestionInput"),
		).
			Return(&sharedData, fmt.Errorf("Error in UpdateByID repo")).
			Once()

		updatedData, supplementaryData, err := svc.UpdateByID(
			ctx,
			&utils.DummyID,
			&sharedRequest,
		)

		assert.Error(t, err)
		assert.Nil(t, updatedData)
		assert.Nil(t, supplementaryData)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success delete data by id", func(t *testing.T) {
		mockRepo.On(
			"DeleteByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedData, nil).
			Once()

		data, err := svc.DeleteByID(
			ctx,
			&utils.DummyID,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed delete data by id", func(t *testing.T) {
		mockRepo.On(
			"DeleteByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(nil, fmt.Errorf("Error in DeleteByID repo")).
			Once()

		data, err := svc.DeleteByID(
			ctx,
			&utils.DummyID,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success get many data", func(t *testing.T) {
		mockRepo.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProductFilter"),
		).
			Return(
				[]domain.TreatmentProductGetMany{
					{
						TreatmentProduct: sharedData,
						Category:         []domain.TreatmentCategory{category},
					},
				},
				1,
				nil,
			).
			Once()

		// Mock presigner for media url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		// Mock presigner for thumbnail url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		data, totalData, err := svc.GetMany(ctx, &sharedFilterData)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.NotEmpty(t, totalData)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed get many data", func(t *testing.T) {
		mockRepo.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProductFilter"),
		).
			Return(nil, 0, fmt.Errorf("Error in GetMany repo")).
			Once()

		data, totalData, err := svc.GetMany(ctx, &sharedFilterData)

		assert.Error(t, err)
		assert.Nil(t, data)
		assert.Empty(t, totalData)
		mockRepo.AssertExpectations(t)
	})
}
