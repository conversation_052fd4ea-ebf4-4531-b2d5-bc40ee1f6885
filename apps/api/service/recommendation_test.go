package service_test

import (
	"api/domain"
	"api/service"
	"api/service/mocks"
	"context"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
)

func TestRecommendationService(test *testing.T) {
	var (
		ctx                   = context.Background()
		mockSARepo            = new(mocks.SkinAnalyzeRepository)
		mockConcernAnswerRepo = new(mocks.ConcernAnswerRepository)
		mockConcernGroupRepo  = new(mocks.ConcernGroupRepository)
		mockTreatmentRepo     = new(mocks.TreatmentProductRepository)
		mockUserSurveyRepo    = new(mocks.UserSurveyRepository)
		mockHttp              = new(mocks.RecommendationHttp)
		mockPresigner         = new(mocks.Presigner)
        mockSkinProblemRepo   = new(mocks.SkinProblemRepository)
		svc                   = service.NewRecommendationService(mockSARepo, mockConcernAnswerRepo, mockConcernGroupRepo, mockTreatmentRepo, mockUserSurveyRepo, mockHttp, mockPresigner, mockSkinProblemRepo)
	)

	skinAnalyzeID := "123e4567-e89b-12d3-a456-426614174000"
	actualAge := 30
	phoneNumber := "1234567890"
	evaluationRate := 5
	skinAge := 30
	skinCondition := "Good"
	plTexture := 75
	uvPorphyrin := 80
	uvPigmentation := 60
	uvMoisture := 70
	sensitiveArea := 70
	brownArea := 80
	uvDamage := 90
	suggestion := "Use sunscreen"
	skinAnalyze := domain.SkinAnalyze{
		ID:             skinAnalyzeID,
		Name:           "John Doe",
		ActualAge:      &actualAge,
		InputDate:      "2023-10-01",
		PhoneNumber:    &phoneNumber,
		EvaluationRate: &evaluationRate,
		SkinAge:        &skinAge,
		SkinCondition:  &skinCondition,
		RGBPore:        75,
		RGBSpot:        50,
		RGBWrinkle:     70,
		PLTexture:      &plTexture,
		UVPorphyrin:    &uvPorphyrin,
		UVPigmentation: &uvPigmentation,
		UVMoisture:     &uvMoisture,
		SensitiveArea:  &sensitiveArea,
		BrownArea:      &brownArea,
		UVDamage:       &uvDamage,
		Suggestion:     &suggestion,
		PathImages:     []string{"image1.jpg", "image2.jpg"},
		PathPDF:        "/path/to/pdf",
	}

	recommendationTreatmentOutput := domain.RecommendationTreatmentMLResponse{
		Text:  "Recommended treatment for the image",
		Video: "video_url",
		Data: []domain.Treatment{
			{
				ID:           "12345",
				Type:         "treatment",
				SKU:          "sku123",
				Name:         "Treatment Name",
				ConcernGroup: "Concern Group",
				Concern:      "Concern",
				Price:        100,
			},
		},
	}

	test.Run("Success get recommendation treatment", func(t *testing.T) {
		mockSARepo.On(
			"GetSkinAnalyzeByID",
			mock.Anything,
			skinAnalyzeID,
		).
			Return(&skinAnalyze, nil).
			Once()

		top3Concern := []domain.RecommendationConcern{
			{
				Name:  "Spot",
				Label: "spots",
				Score: 50,
			},
			{
				Name:  "Pigmentation",
				Label: "pigmentation",
				Score: 60,
			},
			{
				Name:  "Moisture",
				Label: "moisture",
				Score: 70,
			},
		}

		concernAnswer := domain.ConcernAnswer{
			ConcernKey: "rgb_spot",
			Answer:     "Answer",
			VideoURL:   nil,
		}
		mockConcernAnswerRepo.On(
			"GetConcernAnswerByKey",
			mock.Anything,
			mock.AnythingOfType("string"),
		).
			Return(&concernAnswer, nil).
			Once()

		concernGroups := []*domain.ConcernGroup{
			{
				Name:    "Concern Group",
				Concern: "Concern",
			},
			{
				Name:    "Concern Group 2",
				Concern: "Concern 2",
			},
		}
		mockConcernGroupRepo.On(
			"GetAllConcernGroup",
			mock.Anything,
		).
			Return(concernGroups, nil).
			Once()

		treatments := []domain.TreatmentProductGetMany{
			{
				TreatmentProduct: domain.TreatmentProduct{
					ID:          "12345",
					ItemCode:    "sku123",
					Name:        "Treatment Name",
					Type:        domain.TreatmentType,
					Description: "Treatment Description",
					Price:       100,
					Quantity:    1,
				},
				Category: []domain.TreatmentCategory{},
			},
			{
				TreatmentProduct: domain.TreatmentProduct{
					ID:          "123456",
					ItemCode:    "sku1234",
					Name:        "Treatment Name 2",
					Type:        domain.TreatmentType,
					Description: "Treatment Description 2",
					Price:       100,
					Quantity:    1,
				},
				Category: []domain.TreatmentCategory{},
			},
		}
		mockTreatmentRepo.On(
			"GetMany",
			mock.Anything,
			&domain.TreatmentProductFilter{
				Pagination: domain.Pagination{
					Page:     1,
					PageSize: 100,
				},
			},
		).
			Return(treatments, 2, nil).
			Once()

		mlRequest := domain.RecommendationTreatmentMLRequest{
			SkinAnalyze:   skinAnalyze,
			TopConcern:    top3Concern,
			ConcernAnswer: concernAnswer,
			ConcernGroups: concernGroups,
			Treatments:    &treatments,
		}

		mockHttp.On(
			"RecommendationTreatment",
			mock.Anything,
			&mlRequest,
		).
			Return(&recommendationTreatmentOutput, nil).
			Once()

		presignedHttpRequest := v4.PresignedHTTPRequest{
			URL:    "https://example.com/video_url",
			Method: http.MethodGet,
		}
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil)

		data, err := svc.RecommendationTreatment(
			ctx,
			skinAnalyzeID,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, recommendationTreatmentOutput.Text, data.Text)
		assert.Equal(t, recommendationTreatmentOutput.Video, data.Video)
		assert.Equal(t, recommendationTreatmentOutput.Data[0].ID, data.Data[0].ID)

		mockSARepo.AssertExpectations(t)
		mockConcernAnswerRepo.AssertExpectations(t)
		mockConcernGroupRepo.AssertExpectations(t)
		mockTreatmentRepo.AssertExpectations(t)
		mockHttp.AssertExpectations(t)
		mockPresigner.AssertExpectations(t)
	})

	test.Run("Success get recommendation treatment on partial null", func(t *testing.T) {
		tempSkinAnalyze := skinAnalyze
		tempSkinAnalyze.PLTexture = nil
		tempSkinAnalyze.UVPorphyrin = nil
		tempSkinAnalyze.UVPigmentation = nil
		tempSkinAnalyze.UVMoisture = nil
		tempSkinAnalyze.SensitiveArea = nil
		tempSkinAnalyze.BrownArea = nil
		tempSkinAnalyze.UVDamage = nil

		mockSARepo.On(
			"GetSkinAnalyzeByID",
			mock.Anything,
			skinAnalyzeID,
		).
			Return(&tempSkinAnalyze, nil).
			Once()

		top3Concern := []domain.RecommendationConcern{
			{
				Name:  "Spot",
				Label: "spots",
				Score: 50,
			},
			{
				Name:  "Wrinkle",
				Label: "wrinkles",
				Score: 70,
			},
			{
				Name:  "Pore",
				Label: "pores",
				Score: 75,
			},
		}

		concernAnswer := domain.ConcernAnswer{
			ConcernKey: "rgb_spot",
			Answer:     "Answer",
			VideoURL:   nil,
		}
		mockConcernAnswerRepo.On(
			"GetConcernAnswerByKey",
			mock.Anything,
			mock.AnythingOfType("string"),
		).
			Return(&concernAnswer, nil).
			Once()

		concernGroups := []*domain.ConcernGroup{
			{
				Name:    "Concern Group",
				Concern: "Concern",
			},
			{
				Name:    "Concern Group 2",
				Concern: "Concern 2",
			},
		}
		mockConcernGroupRepo.On(
			"GetAllConcernGroup",
			mock.Anything,
		).
			Return(concernGroups, nil).
			Once()

		treatments := []domain.TreatmentProductGetMany{
			{
				TreatmentProduct: domain.TreatmentProduct{
					ID:          "12345",
					ItemCode:    "sku123",
					Name:        "Treatment Name",
					Type:        domain.TreatmentType,
					Description: "Treatment Description",
					Price:       100,
					Quantity:    1,
				},
				Category: []domain.TreatmentCategory{},
			},
			{
				TreatmentProduct: domain.TreatmentProduct{
					ID:          "123456",
					ItemCode:    "sku1234",
					Name:        "Treatment Name 2",
					Type:        domain.TreatmentType,
					Description: "Treatment Description 2",
					Price:       100,
					Quantity:    1,
				},
				Category: []domain.TreatmentCategory{},
			},
		}
		mockTreatmentRepo.On(
			"GetMany",
			mock.Anything,
			&domain.TreatmentProductFilter{
				Pagination: domain.Pagination{
					Page:     1,
					PageSize: 100,
				},
			},
		).
			Return(treatments, 2, nil).
			Once()

		mlRequest := domain.RecommendationTreatmentMLRequest{
			SkinAnalyze:   tempSkinAnalyze,
			TopConcern:    top3Concern,
			ConcernAnswer: concernAnswer,
			ConcernGroups: concernGroups,
			Treatments:    &treatments,
		}

		mockHttp.On(
			"RecommendationTreatment",
			mock.Anything,
			&mlRequest,
		).
			Return(&recommendationTreatmentOutput, nil).
			Once()

		presignedHttpRequest := v4.PresignedHTTPRequest{
			URL:    "https://example.com/video_url",
			Method: http.MethodGet,
		}
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil)

		data, err := svc.RecommendationTreatment(
			ctx,
			skinAnalyzeID,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, recommendationTreatmentOutput.Text, data.Text)
		assert.Equal(t, recommendationTreatmentOutput.Video, data.Video)
		assert.Equal(t, recommendationTreatmentOutput.Data[0].ID, data.Data[0].ID)

		mockSARepo.AssertExpectations(t)
		mockConcernAnswerRepo.AssertExpectations(t)
		mockConcernGroupRepo.AssertExpectations(t)
		mockTreatmentRepo.AssertExpectations(t)
		mockHttp.AssertExpectations(t)
		mockPresigner.AssertExpectations(t)
	})
}
