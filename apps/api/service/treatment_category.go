package service

import (
	"context"
	"fmt"

	"api/domain"
)

//go:generate mockery
type TreatmentCategoryRepository interface {
	Create(ctx context.Context, data *domain.TreatmentCategory) error
	GetByID(ctx context.Context, id *string) (*domain.TreatmentCategory, error)
	UpdateByID(
		ctx context.Context,
		existingData *domain.TreatmentCategory,
		newData *domain.TreatmentCategory,
	) error
	DeleteByID(
		ctx context.Context,
		id *string,
	) (*domain.TreatmentCategory, error)
	GetMany(
		ctx context.Context,
		filter *domain.TreatmentCategoryFilter,
	) ([]domain.TreatmentCategory, int, error)
}

type treatmentCategoryService struct {
	repo TreatmentCategoryRepository
}

func NewTreatmentCategoryService(
	repo TreatmentCategoryRepository,
) *treatmentCategoryService {
	return &treatmentCategoryService{repo}
}

func (service *treatmentCategoryService) Create(
	ctx context.Context,
	data *domain.TreatmentCategory,
) (*domain.TreatmentCategory, error) {
	err := service.repo.Create(ctx, data)

	if err != nil {
		return nil, err
	}

	return data, nil
}

func (service *treatmentCategoryService) GetByID(
	ctx context.Context,
	id *string,
) (*domain.TreatmentCategory, error) {
	data, err := service.repo.GetByID(ctx, id)

	switch {
	case err != nil:
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return data, nil
}

func (service *treatmentCategoryService) UpdateByID(
	ctx context.Context,
	input *domain.TreatmentCategory,
) (*domain.TreatmentCategory, error) {
	existingData, err := service.GetByID(ctx, &input.ID)

	if err != nil {
		return nil, err
	}

	err = service.repo.UpdateByID(
		ctx,
		existingData,
		input,
	)

	if err != nil {
		return nil, err
	}

	return existingData, nil
}

func (service *treatmentCategoryService) DeleteByID(
	ctx context.Context,
	id *string,
) (*domain.TreatmentCategory, error) {
	data, err := service.repo.DeleteByID(ctx, id)

	switch {
	case err != nil:
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return data, nil
}

func (service *treatmentCategoryService) GetMany(
	ctx context.Context,
	filter *domain.TreatmentCategoryFilter,
) ([]domain.TreatmentCategory, int, error) {
	data, totalData, err := service.repo.GetMany(ctx, filter)

	if err != nil {
		return nil, 0, err
	}

	return data, totalData, nil
}
