package service

import (
	"context"
	"fmt"

	"api/domain"
)

//go:generate mockery
type TreatmentIntervalRepository interface {
	Create(ctx context.Context, data *domain.TreatmentInterval) error
	GetByID(ctx context.Context, id *string) (*domain.TreatmentInterval, error)
	UpdateByID(
		ctx context.Context,
		existingData *domain.TreatmentInterval,
		newData *domain.TreatmentInterval,
	) error
	DeleteByID(
		ctx context.Context,
		id *string,
	) (*domain.TreatmentInterval, error)
	GetMany(
		ctx context.Context,
		filter *domain.TreatmentIntervalFilter,
	) ([]domain.TreatmentInterval, int, error)
}

type treatmentIntervalService struct {
	repo TreatmentIntervalRepository
}

func NewTreatmentIntervalService(
	repo TreatmentIntervalRepository,
) *treatmentIntervalService {
	return &treatmentIntervalService{repo}
}

func (service *treatmentIntervalService) Create(
	ctx context.Context,
	data *domain.TreatmentInterval,
) (*domain.TreatmentInterval, error) {
	err := service.repo.Create(ctx, data)

	if err != nil {
		return nil, err
	}

	return data, nil
}

func (service *treatmentIntervalService) GetByID(
	ctx context.Context,
	id *string,
) (*domain.TreatmentInterval, error) {
	data, err := service.repo.GetByID(ctx, id)

	switch {
	case err != nil:
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return data, nil
}

func (service *treatmentIntervalService) UpdateByID(
	ctx context.Context,
	input *domain.TreatmentInterval,
) (*domain.TreatmentInterval, error) {
	existingData, err := service.GetByID(ctx, &input.ID)

	if err != nil {
		return nil, err
	}

	err = service.repo.UpdateByID(
		ctx,
		existingData,
		input,
	)

	if err != nil {
		return nil, err
	}

	return existingData, nil
}

func (service *treatmentIntervalService) DeleteByID(
	ctx context.Context,
	id *string,
) (*domain.TreatmentInterval, error) {
	data, err := service.repo.DeleteByID(ctx, id)

	switch {
	case err != nil:
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return data, nil
}

func (service *treatmentIntervalService) GetMany(
	ctx context.Context,
	filter *domain.TreatmentIntervalFilter,
) ([]domain.TreatmentInterval, int, error) {
	data, totalData, err := service.repo.GetMany(ctx, filter)

	if err != nil {
		return nil, 0, err
	}

	return data, totalData, nil
}
