package service_test

import (
	"api/domain"
	"api/service"
	"api/service/mocks"
	"context"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
)

func TestSkinAnalyzeService(t *testing.T) {
	var (
		ctx           = context.Background()
		mockRepo      = new(mocks.SkinAnalyzeRepository)
		mockLogRepo   = new(mocks.MachineSyncLogRepository)
		mockHttp      = new(mocks.SkinAnalyzeHttp)
		mockPresigner = new(mocks.Presigner)
		svc           = service.NewSkinAnalyzeService(mockRepo, mockLogRepo, mockHttp, mockPresigner)
	)

	actualAge := 30
	phoneNumber := "1234567890"
	evaluationRate := 5
	skinAge := 30
	skinCondition := "Good"
	plTexture := 75
	uvPorphyrin := 80
	uvPigmentation := 60
	uvMoisture := 70
	sensitiveArea := 70
	brownArea := 80
	uvDamage := 90
	suggestion := "Use sunscreen"
	skinAnalyze := &domain.SkinAnalyze{
		Name:           "John Doe",
		ActualAge:      &actualAge,
		InputDate:      "2023-10-01",
		PhoneNumber:    &phoneNumber,
		EvaluationRate: &evaluationRate,
		SkinAge:        &skinAge,
		SkinCondition:  &skinCondition,
		RGBPore:        75,
		RGBSpot:        50,
		RGBWrinkle:     70,
		PLTexture:      &plTexture,
		UVPorphyrin:    &uvPorphyrin,
		UVPigmentation: &uvPigmentation,
		UVMoisture:     &uvMoisture,
		SensitiveArea:  &sensitiveArea,
		BrownArea:      &brownArea,
		UVDamage:       &uvDamage,
		Suggestion:     &suggestion,
		PathImages:     []string{"image1.jpg", "image2.jpg"},
		PathPDF:        "/path/to/pdf",
	}

	t.Run("Create skin analyze", func(t *testing.T) {
		t.Run("Success", func(t *testing.T) {
			mockHttp.On(
				"UploadImage",
				mock.Anything,
				mock.AnythingOfType("*domain.SkinAnalyzeUploadRequest"),
			).
				Return(skinAnalyze, nil).
				Once()

			mockRepo.On(
				"CreateSkinAnalyze",
				mock.Anything,
				mock.AnythingOfType("*domain.SkinAnalyze"),
			).
				Return(skinAnalyze, nil).
				Once()

			mockLogRepo.On(
				"CreateMachineSyncLog",
				mock.Anything,
				mock.AnythingOfType("*domain.MachineSyncLog"),
			).
				Return(nil, nil).
				Once()

			inputData := &domain.SkinAnalyzeUploadRequest{
				Images: []string{"image1.jpg", "image2.jpg"},
				Pdf:    "/path/to/pdf",
			}
			data, err := svc.CreateSkinAnalyze(
				ctx,
				inputData,
			)

			assert.NoError(t, err)
			assert.NotNil(t, data)
			mockRepo.AssertExpectations(t)
			mockLogRepo.AssertExpectations(t)
			mockHttp.AssertExpectations(t)
		})

		t.Run("Error on log create", func(t *testing.T) {
			mockHttp.On(
				"UploadImage",
				mock.Anything,
				mock.AnythingOfType("*domain.SkinAnalyzeUploadRequest"),
			).
				Return(skinAnalyze, nil).
				Once()

			mockRepo.On(
				"CreateSkinAnalyze",
				mock.Anything,
				mock.AnythingOfType("*domain.SkinAnalyze"),
			).
				Return(skinAnalyze, nil).
				Once()

			mockLogRepo.On(
				"CreateMachineSyncLog",
				mock.Anything,
				mock.AnythingOfType("*domain.MachineSyncLog"),
			).
				Return(nil, fmt.Errorf("Error")).
				Once()

			inputData := &domain.SkinAnalyzeUploadRequest{
				Images: []string{"image1.jpg", "image2.jpg"},
				Pdf:    "/path/to/pdf",
			}
			data, err := svc.CreateSkinAnalyze(
				ctx,
				inputData,
			)

			assert.NoError(t, err)
			assert.NotNil(t, data)
			mockRepo.AssertExpectations(t)
			mockLogRepo.AssertExpectations(t)
			mockHttp.AssertExpectations(t)
		})
	})

	t.Run("Success get skin analyze by id", func(t *testing.T) {
		mockRepo.On(
			"GetSkinAnalyzeByID",
			mock.Anything,
			mock.AnythingOfType("string"),
		).
			Return(skinAnalyze, nil).
			Once()

		presignedHttpRequest := v4.PresignedHTTPRequest{
			URL:    "https://example.com/presigned-url",
			Method: http.MethodGet,
		}

		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil)

		data, err := svc.GetSkinAnalyzeByID(ctx, "123")

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, skinAnalyze.Name, data.Name)
		assert.Equal(t, presignedHttpRequest.URL, skinAnalyze.PathImages[0])
		mockRepo.AssertExpectations(t)
	})

	t.Run("Success get many skin analyzes", func(t *testing.T) {
		mockRepo.On(
			"GetManySkinAnalyzes",
			mock.Anything,
			mock.AnythingOfType("*domain.SkinAnalyzeFilter"),
		).
			Return([]*domain.SkinAnalyze{skinAnalyze}, 1, nil).
			Once()
		filter := &domain.SkinAnalyzeFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
		}
		data, total, err := svc.GetManySkinAnalyzes(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, 1, total)
		assert.Equal(t, skinAnalyze.Name, data[0].Name)
		mockRepo.AssertExpectations(t)
	})
}
