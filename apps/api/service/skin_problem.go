package service

import (
	"context"
	"fmt"

	"github.com/rs/zerolog/log"

	"api/domain"
)

//go:generate mockery
type SkinProblemRepository interface {
	GetSkinProblemIndications(
		ctx context.Context,
		ids []string,
	) ([]domain.SkinProblemIndication, error)
	Create(
		ctx context.Context,
		request *domain.SkinProblemRequest,
	) (*domain.SkinProblem, error)
	GetByID(ctx context.Context, id *string) (*domain.SkinProblemResponse, error)
	UpdateByID(
		ctx context.Context,
		existingData *domain.SkinProblemResponse,
		request *domain.SkinProblemRequest,
	) (*domain.SkinProblem, error)
	DeleteByID(
		ctx context.Context,
		id *string,
	) (*domain.SkinProblem, error)
	GetMany(
		ctx context.Context,
		filter *domain.SkinProblemFilter,
	) ([]domain.SkinProblemResponse, int, error)
}

type skinProblemService struct {
	repo SkinProblemRepository
}

func NewSkinProblemService(
	repo SkinProblemRepository,
) *skinProblemService {
	return &skinProblemService{repo}
}

func (service *skinProblemService) Create(
	ctx context.Context,
	request *domain.SkinProblemRequest,
) (*domain.SkinProblem, []domain.SkinProblemIndication, error) {
	skinProblemIndicationData, err := service.repo.GetSkinProblemIndications(
		ctx,
		request.SkinProblemIndicationIDs,
	)

	if err != nil {
		return nil, nil, err
	}

	data, err := service.repo.Create(ctx, request)

	if err != nil {
		return nil, nil, err
	}

	return data, skinProblemIndicationData, nil
}

func (service *skinProblemService) GetByID(
	ctx context.Context,
	id *string,
) (*domain.SkinProblemResponse, error) {
	data, err := service.repo.GetByID(ctx, id)

	switch {
	case err != nil:
		log.Error().Err(err).Msg("Failed to get skin problem by id result")
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return data, nil
}

func (service *skinProblemService) UpdateByID(
	ctx context.Context,
	id *string,
	request *domain.SkinProblemRequest,
) (*domain.SkinProblem, []domain.SkinProblemIndication, error) {
	existingData, err := service.GetByID(ctx, id)

	if err != nil {
		return nil, nil, err
	}

	skinProblemIndicationData, err := service.repo.GetSkinProblemIndications(
		ctx,
		request.SkinProblemIndicationIDs,
	)

	if err != nil {
		return nil, nil, err
	}

	updatedData, err := service.repo.UpdateByID(
		ctx,
		existingData,
		request,
	)

	if err != nil {
		return nil, nil, err
	}

	return updatedData, skinProblemIndicationData, nil
}

func (service *skinProblemService) DeleteByID(
	ctx context.Context,
	id *string,
) (*domain.SkinProblem, error) {
	data, err := service.repo.DeleteByID(ctx, id)

	switch {
	case err != nil:
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return data, nil
}

func (service *skinProblemService) GetMany(
	ctx context.Context,
	filter *domain.SkinProblemFilter,
) ([]domain.SkinProblemResponse, int, error) {
	data, totalData, err := service.repo.GetMany(ctx, filter)

	if err != nil {
		return nil, 0, err
	}

	return data, totalData, nil
}
