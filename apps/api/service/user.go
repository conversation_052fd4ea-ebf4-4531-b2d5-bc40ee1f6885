package service

import (
	"context"
	"fmt"
	"strings"

	"golang.org/x/crypto/bcrypt"

	"api/domain"
)

//go:generate mockery
type UserRepository interface {
	Create(
		ctx context.Context,
		user *domain.User,
	) (*domain.User, error)
	GetByID(
		ctx context.Context,
		id *string,
	) (*domain.User, error)
	UpdateByID(
		ctx context.Context,
		existingData *domain.User,
		newData *domain.User,
	) (*domain.User, error)
	DeleteByID(
		ctx context.Context,
		id *string,
	) (*domain.User, error)
	GetMany(
		ctx context.Context,
		filter *domain.UserFilter,
	) ([]*domain.User, int, error)
	UpdatePassword(
		ctx context.Context,
		id *string,
		password *string,
	) error
}

type userService struct {
	repo UserRepository
}

func NewUserService(repo UserRepository) *userService {
	return &userService{repo}
}

func (service *userService) Create(
	ctx context.Context,
	request *domain.User,
) (*domain.User, error) {
	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword(
		[]byte(*request.Password),
		bcrypt.DefaultCost,
	)
	if err != nil {
		return nil, err
	}

	// Convert request to domain model
	user := request
	hashedPasswordStr := string(hashedPassword)
	user.Password = &hashedPasswordStr

	// Create user
	result, err := service.repo.Create(ctx, user)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			return nil, err
		}
		return nil, err
	}

	return result, nil
}

func (service *userService) GetByID(
	ctx context.Context,
	id *string,
) (*domain.User, error) {
	data, err := service.repo.GetByID(ctx, id)

	switch {
	case err != nil:
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return data, nil
}

func (service *userService) UpdateByID(
	ctx context.Context,
	request *domain.User,
) (*domain.User, error) {
	// Get existing user
	existingUser, err := service.repo.GetByID(ctx, &request.ID)
	if err != nil {
		return nil, err
	}

    // Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword(
		[]byte(*request.Password),
		bcrypt.DefaultCost,
	)
	if err != nil {
		return nil, err
	}

	// Convert request to domain model
	user := request
	hashedPasswordStr := string(hashedPassword)
	user.Password = &hashedPasswordStr


	// Update user
	result, err := service.repo.UpdateByID(ctx, existingUser, user)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (service *userService) DeleteByID(
	ctx context.Context,
	id *string,
) (*domain.User, error) {
	data, err := service.repo.DeleteByID(ctx, id)

	switch {
	case err != nil:
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return data, nil
}

func (service *userService) GetMany(
	ctx context.Context,
	filter *domain.UserFilter,
) (*domain.PaginationData[domain.UserResponse], error) {
	data, totalData, err := service.repo.GetMany(ctx, filter)
	if err != nil {
		return nil, err
	}

	responseData := domain.PaginationData[domain.UserResponse]{
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalData:  totalData,
		TotalPages: filter.GetTotalPages(totalData),
	}

	for _, value := range data {
		response := value.ToResponse()
		responseData.Content = append(responseData.Content, response)
	}

	return &responseData, nil
}

func (service *userService) UpdatePassword(
	ctx context.Context,
	id *string,
	password *string,
) error {
	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword(
		[]byte(*password),
		bcrypt.DefaultCost,
	)
	if err != nil {
		return err
	}

	hashedPasswordStr := string(hashedPassword)
	return service.repo.UpdatePassword(ctx, id, &hashedPasswordStr)
}
