package service

import (
	"context"
	"fmt"

	"api/domain"
)

//go:generate mockery
type UserSurveyRepository interface {
	Create(
		ctx context.Context,
		request *domain.UserSurveyRequest,
	) (*domain.UserSurvey, error)
	GetByID(
		ctx context.Context,
		id *string,
	) (*domain.UserSurvey, error)
	GetMany(
		ctx context.Context,
		filter *domain.UserSurveyFilter,
	) ([]domain.UserSurvey, int, error)
}

type userSurveyService struct {
	repo UserSurveyRepository
}

func NewUserSurveyService(repo UserSurveyRepository) *userSurveyService {
	return &userSurveyService{repo}
}

func (service *userSurveyService) Create(
	ctx context.Context,
	request *domain.UserSurveyRequest,
) (*domain.UserSurvey, error) {
	data, err := service.repo.Create(ctx, request)

	if err != nil {
		return nil, err
	}

	return data, nil
}

func (service *userSurveyService) GetByID(
	ctx context.Context,
	id *string,
) (*domain.UserSurvey, error) {
	data, err := service.repo.GetByID(ctx, id)

	switch {
	case err != nil:
		return nil, err
	case data == nil:
		return nil, fmt.Errorf(
			"Data with id %s is not found",
			*id,
		)
	}

	return data, nil
}

func (service *userSurveyService) GetMany(
	ctx context.Context,
	filter *domain.UserSurveyFilter,
) ([]domain.UserSurvey, int, error) {
	data, totalData, err := service.repo.GetMany(ctx, filter)

	if err != nil {
		return nil, 0, err
	}

	return data, totalData, nil
}
