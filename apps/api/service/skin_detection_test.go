package service_test

import (
	"context"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"

	"api/domain"
	"api/service"
	"api/service/mocks"
	"api/utils"
)

func TestSkinDetectionService(test *testing.T) {
	var (
		ctx           = context.Background()
		mockRepo      = new(mocks.SkinAnalyzeRepository)
		mockHttp      = new(mocks.SkinDetectionHttp)
		mockPresigner = new(mocks.Presigner)
		svc           = service.NewSkinDetectionService(
			mockRepo,
			mockHttp,
			mockPresigner,
		)
	)

	skinAnalyzeID := utils.DummyID
	actualAge := 30
	phoneNumber := "1234567890"
	evaluationRate := 5
	skinAge := 30
	skinCondition := "Good"
	plTexture := 75
	uvPorphyrin := 80
	uvPigmentation := 60
	uvMoisture := 70
	sensitiveArea := 70
	brownArea := 80
	uvDamage := 90
	suggestion := "Use sunscreen"
	skinAnalyze := &domain.SkinAnalyze{
		ID:             skinAnalyzeID,
		Name:           "<PERSON>",
		ActualAge:      &actualAge,
		InputDate:      "2023-10-01",
		PhoneNumber:    &phoneNumber,
		EvaluationRate: &evaluationRate,
		SkinAge:        &skinAge,
		SkinCondition:  &skinCondition,
		RGBPore:        75,
		RGBSpot:        50,
		RGBWrinkle:     70,
		PLTexture:      &plTexture,
		UVPorphyrin:    &uvPorphyrin,
		UVPigmentation: &uvPigmentation,
		UVMoisture:     &uvMoisture,
		SensitiveArea:  &sensitiveArea,
		BrownArea:      &brownArea,
		UVDamage:       &uvDamage,
		Suggestion:     &suggestion,
		PathImages:     []string{"face_aging_wrinkles.png", "face_aging_pores.png"},
		PathPDF:        "/path/to/pdf",
	}

	queryParams := domain.SkinDetectionQueryParams{
		Concern: domain.FaceAgingConcernWrinkle,
	}

	test.Run("Success detect skin wrinkles", func(t *testing.T) {
		mockRepo.On(
			"GetSkinAnalyzeByID",
			mock.Anything,
			skinAnalyzeID,
		).
			Return(skinAnalyze, nil).
			Once()

		detectWrinkleMLOutput := domain.SkinDetectionMLResponse{
			ImagePath: "uwu/face_aging_wrinkles_detected.png",
		}

		mockHttp.On(
			"DetectWrinkles",
			mock.Anything,
			mock.AnythingOfType("*domain.SkinDetectionMLRequest"),
		).
			Return(&detectWrinkleMLOutput, nil).
			Once()

		presignedHttpRequest := v4.PresignedHTTPRequest{
			URL:    "https://example.com/presigned-url",
			Method: http.MethodGet,
		}

		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		updateSkinAnalyze := skinAnalyze
		updateSkinAnalyze.PathImages = append(
			skinAnalyze.PathImages,
			"face_aging_wrinkles_detected.png",
		)

		mockRepo.On(
			"UpdateSkinAnalyzeByID",
			mock.Anything,
			skinAnalyzeID,
			updateSkinAnalyze,
		).
			Return(updateSkinAnalyze, nil).
			Once()

		data, err := svc.DetectWrinkles(
			ctx,
			skinAnalyzeID,
			&queryParams,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
		mockHttp.AssertExpectations(t)
		mockPresigner.AssertExpectations(t)
	})

	test.Run("Success detect skin pores", func(t *testing.T) {
		mockRepo.On(
			"GetSkinAnalyzeByID",
			mock.Anything,
			skinAnalyzeID,
		).
			Return(skinAnalyze, nil).
			Once()

		detectPoreMLOutput := domain.SkinDetectionMLResponse{
			ImagePath: "uwu/face_aging_pores_detected.png",
		}

		mockHttp.On(
			"DetectPores",
			mock.Anything,
			mock.AnythingOfType("*domain.SkinDetectionMLRequest"),
		).
			Return(&detectPoreMLOutput, nil).
			Once()

		presignedHttpRequest := v4.PresignedHTTPRequest{
			URL:    "https://example.com/presigned-url",
			Method: http.MethodGet,
		}

		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		updateSkinAnalyze := skinAnalyze
		updateSkinAnalyze.PathImages = append(
			skinAnalyze.PathImages,
			"face_aging_pores_detected.png",
		)

		mockRepo.On(
			"UpdateSkinAnalyzeByID",
			mock.Anything,
			skinAnalyzeID,
			updateSkinAnalyze,
		).
			Return(updateSkinAnalyze, nil).
			Once()

		data, err := svc.DetectPores(
			ctx,
			skinAnalyzeID,
			&queryParams,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
		mockHttp.AssertExpectations(t)
		mockPresigner.AssertExpectations(t)
	})
}
