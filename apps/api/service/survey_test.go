package service_test

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api/domain"
	"api/service"
	"api/service/mocks"
	"api/utils"
)

func TestSurveyService(test *testing.T) {
	var (
		ctx           = context.Background()
		mockRepo      = new(mocks.SurveyRepository)
		mockPresigner = new(mocks.Presigner)
		svc           = service.NewSurveyService(mockRepo, mockPresigner)
	)

	var (
		imageUrl = "https://example.com/s3-object"

		sharedData = domain.Survey{
			Description: "Test create survey data",
			Question:    "Apakah test nya berhasil?",
			Answers: []domain.SurveyAnswer{
				{
					Title:    "Ya",
					ImageUrl: &imageUrl,
				},
				{
					Title:    "Tidak",
					ImageUrl: nil,
				},
			},
			Type: "single_full",
		}

		sharedRequestNested = domain.SurveyRequestNested{
			Description: sharedData.Description,
			Question:    sharedData.Question,
			Answers:     sharedData.Answers,
			Type:        sharedData.Type,
			Category:    sharedData.Category,
			ChildQuestions: []domain.SurveyRequestChildQuestion{
				{
					ParentQuestionAnswer: 0,
					Description:          "Test child question",
					Question:             "Apakah test child question nya berhasil?",
					Answers:              sharedData.Answers,
					Type:                 domain.SingleFull,
					QuestionOrder:        0,
					Category:             domain.Contraindication,
				},
			},
		}

		sharedResponseNested = domain.SurveyResponseNested{
			SurveyResponse: sharedData.ToResponse(),
			ChildQuestions: []domain.SurveyResponse{},
		}

		sharedFilterData = domain.SurveyFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
		}

		sharedFilterDataNested = domain.SurveyFilterNested{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
		}
	)

	presignedHttpRequest := v4.PresignedHTTPRequest{
		URL:    "https://example.com/presigned-url",
		Method: http.MethodGet,
	}

	test.Run("Success create data", func(t *testing.T) {
		mockRepo.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.Survey"),
		).
			Return(&sharedData, nil).
			Once()

		data, err := svc.Create(
			ctx,
			&sharedData,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed create data", func(t *testing.T) {
		mockRepo.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.Survey"),
		).
			Return(nil, fmt.Errorf("Error in Create repo")).
			Once()

		data, err := svc.Create(
			ctx,
			&sharedData,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success create data nested", func(t *testing.T) {
		mockRepo.On(
			"CreateNested",
			mock.Anything,
			mock.AnythingOfType("*domain.SurveyRequestNested"),
		).
			Return(&sharedResponseNested, nil).
			Once()

		data, err := svc.CreateNested(
			ctx,
			&sharedRequestNested,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed create data nested", func(t *testing.T) {
		mockRepo.On(
			"CreateNested",
			mock.Anything,
			mock.AnythingOfType("*domain.SurveyRequestNested"),
		).
			Return(nil, fmt.Errorf("Error in Create repo")).
			Once()

		data, err := svc.CreateNested(
			ctx,
			&sharedRequestNested,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success get data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedData, nil).
			Once()

		// Mock presigner for image url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		data, err := svc.GetByID(
			ctx,
			&sharedData.ID,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed get data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(nil, fmt.Errorf("Error in GetByID repo")).
			Once()

		data, err := svc.GetByID(
			ctx,
			&sharedData.ID,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success get data by id nested", func(t *testing.T) {
		mockRepo.On(
			"GetByIDNested",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedResponseNested, nil).
			Once()

		// Mock presigner for image url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		data, err := svc.GetByIDNested(
			ctx,
			&sharedData.ID,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed get data by id nested", func(t *testing.T) {
		mockRepo.On(
			"GetByIDNested",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(nil, fmt.Errorf("Error in GetByIDNested repo")).
			Once()

		data, err := svc.GetByIDNested(
			ctx,
			&sharedData.ID,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success update data by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedData, nil).
			Once()

		// Mock presigner for image url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		mockRepo.On(
			"UpdateByID",
			mock.Anything,
			mock.AnythingOfType("*domain.Survey"),
			mock.AnythingOfType("*domain.Survey"),
		).
			Return(&sharedData, nil).
			Once()

		data, err := svc.UpdateByID(
			ctx,
			&sharedData,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed update data by id", func(t *testing.T) {
		t.Run("Error in existing data", func(t *testing.T) {
			mockRepo.On(
				"GetByID",
				mock.Anything,
				mock.AnythingOfType("*string"),
			).
				Return(nil, fmt.Errorf("Error in GetByID repo")).
				Once()

			data, err := svc.UpdateByID(
				ctx,
				&sharedData,
			)

			assert.Error(t, err)
			assert.Nil(t, data)
			mockRepo.AssertExpectations(t)
		})

		t.Run("Error in update repo", func(t *testing.T) {
			mockRepo.On(
				"GetByID",
				mock.Anything,
				mock.AnythingOfType("*string"),
			).
				Return(&sharedData, nil).
				Once()

			// Mock presigner for image url.
			mockPresigner.On(
				"GetObject",
				mock.Anything,
				mock.AnythingOfType("string"),
				mock.AnythingOfType("string"),
				mock.AnythingOfType("int64"),
			).
				Return(&presignedHttpRequest, nil).
				Once()

			mockRepo.On(
				"UpdateByID",
				mock.Anything,
				mock.AnythingOfType("*domain.Survey"),
				mock.AnythingOfType("*domain.Survey"),
			).
				Return(nil, fmt.Errorf("Error in UpdateByID repo")).
				Once()

			data, err := svc.UpdateByID(
				ctx,
				&sharedData,
			)

			assert.Error(t, err)
			assert.Nil(t, data)
			mockRepo.AssertExpectations(t)
		})
	})

	test.Run("Success update data by id nested", func(t *testing.T) {
		mockRepo.On(
			"UpdateByIDNested",
			mock.Anything,
			mock.AnythingOfType("*string"),
			mock.AnythingOfType("*domain.SurveyRequestNested"),
		).
			Return(&sharedResponseNested, nil).
			Once()

		data, err := svc.UpdateByIDNested(
			ctx,
			&sharedResponseNested.ID,
			&sharedRequestNested,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed update data by id nested", func(t *testing.T) {
		mockRepo.On(
			"UpdateByIDNested",
			mock.Anything,
			mock.AnythingOfType("*string"),
			mock.AnythingOfType("*domain.SurveyRequestNested"),
		).
			Return(nil, fmt.Errorf("Error in UpdateByID repo")).
			Once()

		data, err := svc.UpdateByIDNested(
			ctx,
			&sharedResponseNested.ID,
			&sharedRequestNested,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success delete data by id", func(t *testing.T) {
		mockRepo.On(
			"DeleteByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(&sharedData, nil).
			Once()

		data, err := svc.DeleteByID(
			ctx,
			&utils.DummyID,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed delete data by id", func(t *testing.T) {
		mockRepo.On(
			"DeleteByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(nil, fmt.Errorf("Error in DeleteByID repo")).
			Once()

		data, err := svc.DeleteByID(
			ctx,
			&utils.DummyID,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success get many data", func(t *testing.T) {
		mockRepo.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.SurveyFilter"),
		).
			Return([]domain.Survey{sharedData}, 1, nil).
			Once()

		// Mock presigner for image url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		data, totalData, err := svc.GetMany(
			ctx,
			&sharedFilterData,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.NotEmpty(t, totalData)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed get many data", func(t *testing.T) {
		mockRepo.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.SurveyFilter"),
		).
			Return(nil, 0, fmt.Errorf("Error in GetMany repo")).
			Once()

		data, totalData, err := svc.GetMany(
			ctx,
			&sharedFilterData,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		assert.Empty(t, totalData)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Success get many data nested", func(t *testing.T) {
		mockRepo.On(
			"GetManyNested",
			mock.Anything,
			mock.AnythingOfType("*domain.SurveyFilterNested"),
		).
			Return([]domain.SurveyResponseNested{sharedResponseNested}, 1, nil).
			Once()

		// Mock presigner for image url.
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil).
			Once()

		data, totalData, err := svc.GetManyNested(
			ctx,
			&sharedFilterDataNested,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.NotEmpty(t, totalData)
		mockRepo.AssertExpectations(t)
	})

	test.Run("Failed get many data nested", func(t *testing.T) {
		mockRepo.On(
			"GetManyNested",
			mock.Anything,
			mock.AnythingOfType("*domain.SurveyFilterNested"),
		).
			Return(nil, 0, fmt.Errorf("Error in GetManyNested repo")).
			Once()

		data, totalData, err := svc.GetManyNested(
			ctx,
			&sharedFilterDataNested,
		)

		assert.Error(t, err)
		assert.Nil(t, data)
		assert.Empty(t, totalData)
		mockRepo.AssertExpectations(t)
	})
}
