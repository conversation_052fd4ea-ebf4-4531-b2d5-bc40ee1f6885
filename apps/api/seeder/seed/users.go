package seed

import (
	"api/domain"
	"context"
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/bcrypt"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/joho/godotenv"
)

func SeedUsers(dbPool *pgxpool.Pool) {
	log.Println("[SEEDER]: Seeding users...")

	ctx := context.Background()
	var totalCount int

	row := dbPool.QueryRow(ctx, "SELECT COUNT(*) as total FROM users")
	err := row.Scan(&totalCount)
	if err != nil {
		log.Println("[SEEDER]: Error scanning row:", err)
		return
	}

	if totalCount > 0 {
		log.Println("[SEEDER]: Seeding users already exist...")
		return
	}

	defaultPassword, err := bcrypt.GenerateFromPassword(
		[]byte(os.Getenv("DEFAULT_PASSWORD")),
		bcrypt.DefaultCost,
	)
	if err != nil {
		log.Println("[SEEDER]: Error generating default password:", err)
		return
	}
	defaultPasswordStr := string(defaultPassword[:])
	adminPassword, err := bcrypt.GenerateFromPassword(
		[]byte(os.Getenv("ADMIN_PASSWORD")),
		bcrypt.DefaultCost,
	)
	if err != nil {
		log.Println("[SEEDER]: Error generating admin password:", err)
		return
	}
	adminPasswordStr := string(adminPassword[:])
	superAdminPassword, err := bcrypt.GenerateFromPassword(
		[]byte(os.Getenv("SUPER_ADMIN_PASSWORD")),
		bcrypt.DefaultCost,
	)
	if err != nil {
		log.Println("[SEEDER]: Error generating super admin password:", err)
		return
	}
	superAdminPasswordStr := string(superAdminPassword[:])

	users := []domain.User{
		{
			Name:        "Branch User",
			Email:       "<EMAIL>",
			Role:        domain.Branch,
			PhoneNumber: "01234567890",
			Password:    &defaultPasswordStr,
		},
		{
			Name:        "Admin",
			Email:       "<EMAIL>",
			Role:        domain.Admin,
			PhoneNumber: "01234567891",
			Password:    &adminPasswordStr,
		},
		{
			Name:        "Super Admin",
			Email:       "<EMAIL>",
			Role:        domain.Admin,
			PhoneNumber: "01234567892",
			Password:    &superAdminPasswordStr,
		},
	}

	baseQuery := `INSERT INTO USERS
        (name, email, role, phone_number, password)
    VALUES `

	var args []any
	for i, user := range users {
		if i > 0 {
			baseQuery += ", "
		}
		baseQuery += fmt.Sprintf("($%d, $%d, $%d, $%d, $%d)",
			i*5+1, i*5+2, i*5+3, i*5+4, i*5+5)
		args = append(args, user.Name, user.Email, user.Role, user.PhoneNumber, user.Password)
	}
	baseQuery += ` RETURNING id`

	_, err = dbPool.Exec(ctx, baseQuery, args...)
	if err != nil {
		log.Println("[SEEDER]: Error inserting users:", err)
		return
	}

	log.Println("[SEEDER]: Seeding users succeed...")
}

// loadEnv loads environment variables from a .env file, logging if not found
func loadEnv() {
	if err := godotenv.Load(".env"); err != nil {
		log.Println("No .env file found, reading configuration from environment variables")
	}
}
