package seed

import (
	"context"
	"log"
	"strconv"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"api/domain"
)

func SeedParameterSkinEvaluations(dbPool *pgxpool.Pool) {
	log.Println("[SEEDER]: Seeding parameter skin evaluations...")

	ctx := context.Background()
	var totalCount int

	err := dbPool.QueryRow(
		ctx,
		"SELECT COUNT(*) as total FROM parameter_skin_evaluations",
	).Scan(&totalCount)

	if err != nil {
		log.Println("[SEEDER]: Error scanning row: ", err)
		return
	}

	if totalCount > 0 {
		log.Println("[SEEDER]: Seeding parameter skin evaluations already exist...")
		return
	}

	filePath := "./seeder/assets/parameter_skin_evaluations.csv"
	contents := readCsv(filePath, ',')

	if contents == nil {
		log.Println("[SEEDER]: No parameter skin evaluations to seed")
		return
	}

	data := make([]domain.ParameterSkinEvaluation, len(contents)-1)

	for i, item := range contents {
		if i == 0 {
			continue // Skip header row
		}

		var (
			name       = item[0]
			lowerPoint = item[1]
			upperPoint = item[2]
		)

		formatName := cases.
			Title(language.English).
			String(name)

		formatLowerPoint, err := strconv.Atoi(lowerPoint)

		if err != nil {
			log.Printf(
				"[ERROR]: Failed parsing lower point for name %s",
				name,
			)
			continue
		}

		formatUpperPoint, err := strconv.Atoi(upperPoint)

		if err != nil {
			log.Printf(
				"[ERROR]: Failed parsing upper point for name %s",
				name,
			)
			continue
		}

		data[i-1] = domain.ParameterSkinEvaluation{
			Name:           formatName,
			LowerPoint:     formatLowerPoint,
			UpperPoint:     formatUpperPoint,
			ParameterOrder: i - 1,
		}
	}

	rowSrc := pgx.CopyFromSlice(
		len(data),
		func(i int) ([]any, error) {
			return []any{
				data[i].Name,
				data[i].LowerPoint,
				data[i].UpperPoint,
				data[i].ParameterOrder,
			}, nil
		},
	)

	count, err := dbPool.CopyFrom(
		ctx,
		pgx.Identifier{"parameter_skin_evaluations"},
		[]string{
			"name",
			"lower_point",
			"upper_point",
			"parameter_order",
		},
		rowSrc,
	)

	if err != nil {
		log.Println("[SEEDER]: Error CopyFrom: ", err)
		return
	}

	log.Printf("[SEEDER]: Seeding %d parameter skin evaluations succeed...", count)
}
