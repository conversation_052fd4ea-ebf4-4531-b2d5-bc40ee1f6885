package seed

import (
	"context"
	"log"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"api/domain"
)

func SeedTreatmentIntervals(dbPool *pgxpool.Pool) {
	log.Println("[SEEDER]: Seeding treatment intervals...")

	ctx := context.Background()
	var totalCount int

	err := dbPool.QueryRow(
		ctx,
		"SELECT COUNT(*) as total FROM treatment_intervals",
	).Scan(&totalCount)

	if err != nil {
		log.Println("[SEEDER]: Error scanning row: ", err)
		return
	}

	if totalCount > 0 {
		log.Println("[SEEDER]: Seeding treatment intervals already exist...")
		return
	}

	filePath := "./seeder/assets/treatment_intervals.csv"
	contents := readCsv(filePath, ',')

	if contents == nil {
		log.Println("[SEEDER]: No treatment intervals to seed")
		return
	}

	data := make([]domain.TreatmentInterval, len(contents)-1)

	for i, item := range contents {
		if i == 0 {
			continue // Skip header row
		}

		days := parseTreatmentInterval(item[0])

		if days == nil {
			continue
		}

		data[i-1] = domain.TreatmentInterval{
			Days: *days,
		}
	}

	rowSrc := pgx.CopyFromSlice(
		len(data),
		func(i int) ([]any, error) {
			return []any{
				data[i].Days,
			}, nil
		},
	)

	count, err := dbPool.CopyFrom(
		ctx,
		pgx.Identifier{"treatment_intervals"},
		[]string{"days"},
		rowSrc,
	)

	if err != nil {
		log.Println("[SEEDER]: Error CopyFrom: ", err)
		return
	}

	log.Printf("[SEEDER]: Seeding %d treatment intervals succeed...", count)
}
