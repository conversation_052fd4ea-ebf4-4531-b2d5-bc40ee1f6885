package seed

import (
	"api/domain"
	"context"
	"fmt"
	"log"

	"github.com/jackc/pgx/v5/pgxpool"
)

func SeedSkinAnalyzes(dbPool *pgxpool.Pool) {
	log.Println("[SEEDER]: Seeding skin analyzes...")

	ctx := context.Background()
	var totalCount int

	row := dbPool.QueryRow(ctx, "SELECT COUNT(*) as total FROM skin_analyzes")
	err := row.Scan(&totalCount)
	if err != nil {
		log.Println("[SEEDER]: Error scanning row:", err)
		return
	}

	if totalCount > 0 {
		log.Println("[SEEDER]: Seeding skin analyzes already exist...")
		return
	}

	skinAnalyzes := []domain.SkinAnalyze{
		{
			ID:             "7dc8b358-3327-4148-aa7d-9b6dd234dc9b",
			Name:           "Hayu",
			ActualAge:      ptr(25),
			InputDate:      "2024-12-26",
			PhoneNumber:    ptrStr("081258889778"),
			EvaluationRate: ptr(57),
			SkinAge:        ptr(28),
			SkinCondition:  ptrStr("General"),
			RGBPore:        45,
			RGBSpot:        59,
			RGBWrinkle:     77,
			PLTexture:      ptr(55),
			UVPorphyrin:    ptr(49),
			UVPigmentation: ptr(57),
			UVMoisture:     ptr(46),
			SensitiveArea:  ptr(69),
			BrownArea:      ptr(57),
			UVDamage:       ptr(59),
			Suggestion:     ptrStr("Your skin is a little bit serious, when compared to more than 40% of the same age group. It is recommended that you should strengthen your skincare regime, by paying more attention to diet & nutrition balance, take good rest, regularly perform your daily skin care."),
			PathImages: []string{
				"hayu-20250523100259/img/Brown Area.jpg",
				"hayu-20250523100259/img/PL Texture.jpg",
				"hayu-20250523100259/img/PL.jpg",
				"hayu-20250523100259/img/RGB Pore.jpg",
				"hayu-20250523100259/img/RGB Spot.jpg",
				"hayu-20250523100259/img/RGB Wrinkle.jpg",
				"hayu-20250523100259/img/RGB.jpg",
				"hayu-20250523100259/img/Sensitive Area.jpg",
				"hayu-20250523100259/img/Skin Aging.jpg",
				"hayu-20250523100259/img/Skin Beautifying.jpg",
				"hayu-20250523100259/img/UV Damage.jpg",
				"hayu-20250523100259/img/UV Moisture.jpg",
				"hayu-20250523100259/img/UV Pigmentation.jpg",
				"hayu-20250523100259/img/UV Porphyrin.jpg",
				"hayu-20250523100259/img/rgb_no_bg.png",
				"hayu-20250523100259/img/rgb_resized.jpg",
				"hayu-20250523100259/img/mask.png",
				"hayu-20250523100259/img/face_aging_pigment.png",
				"hayu-20250523100259/img/selected_area_pigment.png",
				"hayu-20250523100259/img/face_aging_wrinkles.png",
				"hayu-20250523100259/img/selected_area_wrinkles.png",
				"hayu-20250523100259/img/face_aging_acne.png",
				"hayu-20250523100259/img/selected_area_acne.png",
				"hayu-20250523100259/img/face_aging_pores.png",
				"hayu-20250523100259/img/selected_area_pores.png",
				"hayu-20250523100259/img/face_aging_scar.png",
				"hayu-20250523100259/img/selected_area_scar.png",
				"hayu-20250523100259/img/face_aging_sensitive.png",
				"hayu-20250523100259/img/selected_area_sensitive.png",
				"hayu-20250523100259/img/face_aging_beautify.png",
				"hayu-20250523100259/img/selected_area_beautify.png",
			},
			PathPDF: "hayu-20250523100259/pdf/customer_report.pdf",
			Timestamp: domain.Timestamp{
				CreatedAt: 1747994601261,
				UpdatedAt: 1749007540397,
			},
		},
		{
			ID:             "99dc650d-36d5-48bc-8e5a-b0d5cff4b1a5",
			Name:           "alfi rahma",
			ActualAge:      ptr(28),
			InputDate:      "2024-12-24",
			PhoneNumber:    ptrStr("081389123296"),
			EvaluationRate: ptr(55),
			SkinAge:        ptr(30),
			SkinCondition:  ptrStr("General"),
			RGBPore:        42,
			RGBSpot:        56,
			RGBWrinkle:     75,
			PLTexture:      ptr(51),
			UVPorphyrin:    ptr(46),
			UVPigmentation: ptr(60),
			UVMoisture:     ptr(44),
			SensitiveArea:  ptr(65),
			BrownArea:      ptr(56),
			UVDamage:       ptr(57),
			Suggestion:     ptrStr("Your skin is a little bit serious, when compared to more than 40% of the same age group. It is recommended that you should strengthen your skincare regime, by paying more attention to diet & nutrition balance, take good rest, regularly perform your daily skin care."),
			PathImages: []string{
				"alfi rahma-20250513065541/img/Brown Area.jpg",
				"alfi rahma-20250513065541/img/PL Texture.jpg",
				"alfi rahma-20250513065541/img/PL.jpg",
				"alfi rahma-20250513065541/img/RGB Pore.jpg",
				"alfi rahma-20250513065541/img/RGB Spot.jpg",
				"alfi rahma-20250513065541/img/RGB Wrinkle.jpg",
				"alfi rahma-20250513065541/img/RGB.jpg",
				"alfi rahma-20250513065541/img/Sensitive Area.jpg",
				"alfi rahma-20250513065541/img/Skin Aging.jpg",
				"alfi rahma-20250513065541/img/Skin Beautifying.jpg",
				"alfi rahma-20250513065541/img/UV Damage.jpg",
				"alfi rahma-20250513065541/img/UV Moisture.jpg",
				"alfi rahma-20250513065541/img/UV Pigmentation.jpg",
				"alfi rahma-20250513065541/img/UV Porphyrin.jpg",
				"alfi rahma-20250513065541/img/UV.jpg",
				"alfi rahma-20250513065541/img/rgb_no_bg.png",
				"alfi rahma-20250513065541/img/rgb_resized.jpg",
				"alfi rahma-20250513065541/img/mask.png",
				"alfi rahma-20250513065541/img/face_aging_wrinkles.png",
				"alfi rahma-20250513065541/img/selected_area.png",
				"alfi rahma-20250513065541/img/face_aging_acne.png",
				"alfi rahma-20250513065541/img/face_aging_pores.png",
				"alfi rahma-20250513065541/img/face_aging_scar.png",
				"alfi rahma-20250513065541/img/face_aging_sensitive.png",
				"alfi rahma-20250513065541/img/face_aging_pigment.png",
				"alfi rahma-20250513065541/img/face_aging_brightening.png",
			},
			PathPDF: "alfi rahma-20250513065541/pdf/customer_report.pdf",
			Timestamp: domain.Timestamp{
				CreatedAt: 1747119365345,
				UpdatedAt: 1747119745921,
			},
		},
		{
			ID:             "2dc337c1-90f7-4335-9e48-1b607e26bf58",
			Name:           "Ali",
			ActualAge:      ptr(30),
			InputDate:      "2025-01-05",
			PhoneNumber:    ptrStr("081282451796"),
			EvaluationRate: ptr(46),
			SkinAge:        ptr(34),
			SkinCondition:  ptrStr("General"),
			RGBPore:        35,
			RGBSpot:        51,
			RGBWrinkle:     49,
			PLTexture:      ptr(41),
			UVPorphyrin:    ptr(41),
			UVPigmentation: ptr(56),
			UVMoisture:     ptr(40),
			SensitiveArea:  ptr(50),
			BrownArea:      ptr(45),
			UVDamage:       ptr(51),
			Suggestion:     ptrStr("Your skin is serious, when compared to more than 50% of the same age group. It is recommended that you should strengthen your skincare regime, by paying more attention to diet & nutrition balance, take good rest, regularly perform your daily skin care."),
			PathImages: []string{
				"ali-20250509071655/img/Brown Area.jpg",
				"ali-20250509071656/img/PL Texture.jpg",
				"ali-20250509071656/img/PL.jpg",
				"ali-20250509071656/img/RGB Pore.jpg",
				"ali-20250509071656/img/RGB Spot.jpg",
				"ali-20250509071656/img/RGB Wrinkle.jpg",
				"ali-20250509071656/img/RGB.jpg",
				"ali-20250509071656/img/Sensitive Area.jpg",
				"ali-20250509071656/img/Skin Aging.jpg",
				"ali-20250509071657/img/Skin Beautifying.jpg",
				"ali-20250509071657/img/UV Damage.jpg",
				"ali-20250509071657/img/UV Moisture.jpg",
				"ali-20250509071657/img/UV Pigmentation.jpg",
				"ali-20250509071657/img/UV Porphyrin.jpg",
				"ali-20250509071657/img/UV.jpg",
				"ali-20250509071655/img/rgb_no_bg.png",
				"ali-20250509071656/img/rgb_resized.jpg",
				"ali-20250509071656/img/mask.png",
				"ali-20250509071656/img/face_aging_pores.png",
				"ali-20250509071656/img/face_aging_sensitive.png",
				"ali-20250509071656/img/selected_area.png",
			},
			PathPDF: "ali-20250509071657/pdf/customer_report.pdf",
			Timestamp: domain.Timestamp{
				CreatedAt: 1746775035872,
				UpdatedAt: 1747121882682,
			},
		},
		{
			ID:             "88e42510-deba-4e5b-aa10-bd128fbddfbf",
			Name:           "alma",
			ActualAge:      ptr(28),
			InputDate:      "2025-02-27",
			PhoneNumber:    ptrStr("081334380710"),
			EvaluationRate: ptr(47),
			SkinAge:        nil,
			SkinCondition:  nil,
			RGBPore:        42,
			RGBSpot:        44,
			RGBWrinkle:     58,
			PLTexture:      ptr(42),
			UVPorphyrin:    ptr(59),
			UVPigmentation: ptr(42),
			UVMoisture:     ptr(40),
			SensitiveArea:  ptr(53),
			BrownArea:      ptr(44),
			UVDamage:       ptr(46),
			Suggestion:     ptrStr("Kulit Anda serius, jika dibandingkan dengan lebih dari 50% dari kelompok usia yang sama. Disarankan agar Anda memperkuat rezim perawatan kulit Anda, dengan lebih memperhatikan diet & keseimbangan nutrisi, istirahat yang baik, secara teratur melakukan perawatan kulit harian Anda."),
			PathImages: []string{
				"alma-20250618134758/img/Daerah Coklat.jpg",
				"alma-20250618134758/img/Daerah Sensitif.jpg",
				"alma-20250618134758/img/Kelembaban UV.jpg",
				"alma-20250618134758/img/Kerusakan UV.jpg",
				"alma-20250618134758/img/Kerutan RGB.jpg",
				"alma-20250618134758/img/PL.jpg",
				"alma-20250618134758/img/Pigmentasi UV.jpg",
				"alma-20250618134758/img/Pori RGB.jpg",
				"alma-20250618134758/img/RGB.jpg",
				"alma-20250618134758/img/Tekstur PL.jpg",
				"alma-20250618134758/img/Titik RGB.jpg",
				"alma-20250618134758/img/UV Porfirin.jpg",
				"alma-20250618134758/img/UV.jpg",
				"alma-20250618134758/img/rgb_resized.jpg",
				"alma-20250618134758/img/mask.png",
				"alma-20250618134758/img/face_aging_sensitive.png",
				"alma-20250618134758/img/selected_area_sensitive.png",
				"alma-20250618134758/img/face_aging_beautify.png",
				"alma-20250618134758/img/selected_area_beautify.png",
				"alma-20250618134758/img/face_aging_pigment.png",
				"alma-20250618134758/img/selected_area_pigment.png",
			},
			PathPDF: "alma-20250618134758/pdf/CustomerReport.pdf",
			Timestamp: domain.Timestamp{
				CreatedAt: 1750254520699,
				UpdatedAt: 1750273863110,
			},
		},
	}

	baseQuery := `INSERT INTO skin_analyzes (
		id, name, actual_age, input_date, phone_number, evaluation_rate, skin_age,
		skin_condition, rgb_pore, rgb_spot, rgb_wrinkle, pl_texture, uv_porphyrin,
		uv_pigmentation, uv_moisture, sensitive_area, brown_area, uv_damage,
		suggestion, path_images, path_pdf, created_at, updated_at
	) VALUES `

	var args []any
	argCount := 1
	for i, analyze := range skinAnalyzes {
		if i > 0 {
			baseQuery += ", "
		}
		baseQuery += fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d)",
			argCount, argCount+1, argCount+2, argCount+3, argCount+4, argCount+5, argCount+6,
			argCount+7, argCount+8, argCount+9, argCount+10, argCount+11, argCount+12,
			argCount+13, argCount+14, argCount+15, argCount+16, argCount+17, argCount+18,
			argCount+19, argCount+20, argCount+21, argCount+22)

		args = append(args,
			analyze.ID, analyze.Name, analyze.ActualAge, analyze.InputDate, analyze.PhoneNumber,
			analyze.EvaluationRate, analyze.SkinAge, analyze.SkinCondition, analyze.RGBPore,
			analyze.RGBSpot, analyze.RGBWrinkle, analyze.PLTexture, analyze.UVPorphyrin,
			analyze.UVPigmentation, analyze.UVMoisture, analyze.SensitiveArea, analyze.BrownArea,
			analyze.UVDamage, analyze.Suggestion, analyze.PathImages, analyze.PathPDF,
			analyze.CreatedAt, analyze.UpdatedAt,
		)
		argCount += 23
	}

	_, err = dbPool.Exec(ctx, baseQuery, args...)
	if err != nil {
		log.Println("[SEEDER]: Error inserting skin analyzes:", err)
		return
	}

	log.Println("[SEEDER]: Seeding skin analyzes succeed...")
}

// Helper function to create pointer to int
func ptr(i int) *int {
	return &i
}

// Helper function to create pointer to string
func ptrStr(s string) *string {
	return &s
}