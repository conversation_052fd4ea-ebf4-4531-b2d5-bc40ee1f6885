package seed

import (
	"context"
	"log"
	"strconv"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"api/domain"
)

func SeedProductLists(dbPool *pgxpool.Pool) {
	log.Println("[SEEDER]: Seeding product lists...")

	ctx := context.Background()
	var totalCount int

	err := dbPool.QueryRow(
		ctx,
		"SELECT COUNT(*) as total FROM treatment_products WHERE type = 'product'",
	).Scan(&totalCount)

	if err != nil {
		log.Println("[ERROR]: Failed scanning row: ", err)
		return
	}

	if totalCount > 0 {
		log.Println("[SEEDER]: Seeding product lists already exist...")
		return
	}

	filePath := "./seeder/assets/product_lists.csv"
	contents := readCsv(filePath, ',')

	if contents == nil {
		log.Println("[SEEDER]: No product lists to seed")
		return
	}

	productsData := make([]domain.TreatmentProduct, len(contents)-1)

	for i := 0; i < len(contents); i++ {
		if i == 0 {
			continue // Skip header row
		}

		var (
			id               string
			nullableMediaUrl *string
		)

		var (
			contentType = domain.ProductType
			itemCode    = contents[i][0]
			name        = contents[i][1]
			description = contents[i][2]
			mediaUrl    = contents[i][3]
			price       = contents[i][4]
			quantity    = contents[i][5]
		)

		if mediaUrl != "" {
			nullableMediaUrl = &mediaUrl
		}

		finalPrice, err := strconv.ParseInt(price, 10, 64)
		if err != nil {
			log.Printf(
				"[ERROR]: Failed parsing price for item code %s: %v",
				itemCode,
				err,
			)
			continue
		}

		finalQuantity, err := strconv.Atoi(quantity)
		if err != nil {
			log.Printf(
				"[ERROR]: Failed parsing quantity for item code %s: %v",
				itemCode,
				err,
			)
			continue
		}

		err = dbPool.QueryRow(ctx, "SELECT gen_random_uuid()").Scan(&id)
		if err != nil {
			log.Printf(
				"[ERROR]: Failed generate id for item code %s: %v",
				itemCode,
				err,
			)
		}

		productsData[i-1] = domain.TreatmentProduct{
			ID:          id,
			ItemCode:    itemCode,
			Name:        name,
			Type:        contentType,
			Description: description,
			Price:       finalPrice,
			Quantity:    finalQuantity,
			MediaUrl:    nullableMediaUrl,
		}
	}

	rowSrc := pgx.CopyFromSlice(
		len(productsData),
		func(i int) ([]any, error) {
			return []any{
				productsData[i].ID,
				productsData[i].ItemCode,
				productsData[i].Name,
				productsData[i].Type,
				productsData[i].Description,
				productsData[i].Price,
				productsData[i].Quantity,
				productsData[i].MediaUrl,
			}, nil
		},
	)

	count, err := dbPool.CopyFrom(
		ctx,
		pgx.Identifier{"treatment_products"},
		[]string{
			"id",
			"item_code",
			"name",
			"type",
			"description",
			"price",
			"quantity",
			"media_url",
		},
		rowSrc,
	)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed CopyFrom in product list, here's why: %v",
			err,
		)
		return
	}

	log.Printf(
		"[SEEDER]: Seeding %d product lists succeed...",
		count,
	)
}
