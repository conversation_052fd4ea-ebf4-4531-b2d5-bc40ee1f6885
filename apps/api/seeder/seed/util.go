package seed

import (
	"encoding/csv"
	"io"
	"io/fs"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

func readCsv(path string, delimiter rune) [][]string {
	filePath := path

	// Get the absolute path
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		log.Println("[SEEDER]: Error getting absolute path:", err)
		return nil
	}

	// Check if file exist
	_, err = os.Stat(absPath)
	fileExist := err == nil

	// Open the CSV file
	var file fs.File
	if fileExist {
		log.Println("[SEEDER]: Using local", filepath.Base(path))

		file, err = os.Open(filepath.Clean(absPath))
		if err != nil {
			log.Println("[SEEDER]: Error opening file:", err)
			return nil
		}

	} else {
		var fullPath string

		fullDirName, baseFileName := filepath.Split(absPath)
		baseDirName := filepath.Base(fullDirName)

		switch baseDirName {
		case "assets":
			fullPath = filepath.Join("assets", baseFileName)
		default:
			fullPath = filepath.Join("assets", baseDirName, baseFileName)

		}

		log.Println("[SEEDER]: File do not exist, using embed for now returning nil", fullPath)
		return nil

	}

	defer file.Close()

	// Read all records from CSV
	reader := csv.NewReader(file)
	reader.Comma = delimiter
	records, err := reader.ReadAll()
	if err != nil {
		return nil
	}

	return records
}

func parseNullFloat64(value string) (*float64, error) {
	value = strings.TrimSpace(value)
	if value == "" || strings.EqualFold(value, "NULL") {
		return nil, nil
	}

	parsed, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return nil, err
	}

	return &parsed, nil
}

func parseNullString(value string) *string {
	value = strings.TrimSpace(value)
	if value == "" || strings.EqualFold(value, "NULL") {
		return nil
	}
	return &value
}

// Stream CSV rows as they are read instead of reading all at once.
func readCsvStream(path string, delimiter rune) (<-chan []string, error) {
	absPath, err := filepath.Abs(path)
	if err != nil {
		log.Println("[SEEDER]: Error getting absolute path:", err)
		return nil, err
	}

	file, err := os.Open(filepath.Clean(absPath))
	if err != nil {
		log.Println("[SEEDER]: Error opening file:", err)
		return nil, err
	}

	// Channel to send CSV rows
	rowChan := make(chan []string)

	// Read CSV line by line in a goroutine
	go func() {
		defer file.Close()
		defer close(rowChan)

		reader := csv.NewReader(file)
		reader.Comma = delimiter
		_, _ = reader.Read() // Skip header row

		for {
			record, err := reader.Read()
			if err == io.EOF {
				break
			}
			if err != nil {
				log.Println("[SEEDER]: Error reading record:", err)
				break
			}
			rowChan <- record
		}
	}()

	return rowChan, nil
}

func parseDate(dateStr string) (time.Time, error) {
	parsedTime, err := time.Parse("02-Jan-06", dateStr)
	if err != nil {
		return time.Time{}, err
	}

	return time.Date(
		parsedTime.Year(),
		parsedTime.Month(),
		parsedTime.Day(),
		0, 0, 0, 0,
		time.UTC,
	), nil
}

func ToPointer[T any](value T) *T {
	return &value
}

func parseTreatmentInterval(value string) *int {
	var (
		days *int

		interval            = strings.SplitN(value, " ", 2)
		interval_indicator  = strings.ToLower(interval[1])
		interval_value, err = strconv.Atoi(interval[0])
	)

	if err != nil {
		log.Printf(
			"[SEEDER]: Error parsing interval value: %v, here's why: %v",
			interval,
			err,
		)
	}

	switch interval_indicator {
	case "hari", "day", "days":
		days = &interval_value
	case "minggu", "week", "weeks":
		intervalToDays := interval_value * 7
		days = &intervalToDays
	case "bulan", "month", "months":
		intervalToDays := interval_value * 30
		days = &intervalToDays
	case "tahun", "year", "years":
		intervalToDays := interval_value * 365
		days = &intervalToDays
	default:
		log.Printf(
			"[SEEDER]: Invalid treatment interval format: %v",
			interval,
		)
	}

	return days
}
