package seed

import (
	"context"
	"log"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"api/domain"
)

func SeedSkinProblems(dbPool *pgxpool.Pool) {
	log.Println("[SEEDER]: Seeding skin problems...")

	ctx := context.Background()
	var totalCount int

	err := dbPool.QueryRow(
		ctx,
		"SELECT COUNT(*) as total FROM skin_problems",
	).Scan(&totalCount)

	if err != nil {
		log.Println("[SEEDER]: Error scanning row: ", err)
		return
	}

	if totalCount > 0 {
		log.Println("[SEEDER]: Seeding skin problems already exist...")
		return
	}

	filePath := "./seeder/assets/skin_problems.csv"
	contents := readCsv(filePath, ',')

	if contents == nil {
		log.Println("[SEEDER]: No skin problems to seed")
		return
	}

	data := make([]domain.SkinProblem, len(contents)-1)

	for i, item := range contents {
		if i == 0 {
			continue // Skip header row
		}

		name := cases.
			Title(language.English).
			String(item[0])

		data[i-1] = domain.SkinProblem{
			Name: name,
		}
	}

	rowSrc := pgx.CopyFromSlice(
		len(data),
		func(i int) ([]any, error) {
			return []any{
				data[i].Name,
			}, nil
		},
	)

	count, err := dbPool.CopyFrom(
		ctx,
		pgx.Identifier{"skin_problems"},
		[]string{"name"},
		rowSrc,
	)

	if err != nil {
		log.Println("[SEEDER]: Error CopyFrom: ", err)
		return
	}

	log.Printf("[SEEDER]: Seeding %d skin problems succeed...", count)
}
