package seed

import (
	"api/domain"
	"context"
	"fmt"
	"log"
	"strconv"
	"strings"

	"github.com/jackc/pgx/v5/pgxpool"
)

func SeedTreatments(dbPool *pgxpool.Pool) {
	log.Println("[SEEDER]: Seeding treatments...")

	ctx := context.Background()
	var totalCount int

	row := dbPool.QueryRow(ctx, "SELECT COUNT(*) as total FROM treatments")
	err := row.Scan(&totalCount)
	if err != nil {
		log.Println("[SEEDER]: Error scanning row:", err)
		return
	}

	if totalCount > 0 {
		log.Println("[SEEDER]: Seeding treatments already exist...")
		return
	}

	filePath := "./seeder/assets/treatments.csv"
	treatments := readCsv(filePath, ',')

	if treatments == nil {
		log.Println("[SEEDER]: No treatments to seed")
		return
	}

	treatmentsData := make([]domain.Treatment, len(treatments))

	for i, treatment := range treatments {
		if i == 0 {
			continue // Skip header row
		}
		priceStr := strings.TrimSpace(treatment[5])
		priceStr = strings.ReplaceAll(priceStr, "Rp", "")
		priceStr = strings.ReplaceAll(priceStr, ",", "")
		price, err := strconv.Atoi(priceStr)
		if err != nil {
			log.Println("[SEEDER]: Error converting price to int:", err)
			continue
		}
		treatmentsData[i-1] = domain.Treatment{
			Type:         treatment[0],
			SKU:          treatment[1],
			Name:         treatment[2],
			ConcernGroup: treatment[3],
			Concern:      treatment[4],
			Price:        int64(price),
		}
	}

	baseQuery := "INSERT INTO treatments (type, sku, name, concern_group, concern, price) VALUES "
	var args []any
	for i, treatment := range treatmentsData {
		if i > 0 {
			baseQuery += ", "
		}
		baseQuery += fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d)", i*6+1, i*6+2, i*6+3, i*6+4, i*6+5, i*6+6)
		args = append(args, treatment.Type, treatment.SKU, treatment.Name, treatment.ConcernGroup, treatment.Concern, treatment.Price)
	}

	_, err = dbPool.Exec(ctx, baseQuery, args...)
	if err != nil {
		log.Println("[SEEDER]: Error inserting treatments:", err)
		return
	}

	log.Println("[SEEDER]: Seeding treatments succeed...")
}
