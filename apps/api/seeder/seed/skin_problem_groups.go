package seed

import (
	"context"
	"log"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"api/domain"
)

func getSkinProblemID(
	ctx context.Context,
	dbPool *pgxpool.Pool,
	name string,
) (id *string) {
	err := dbPool.QueryRow(
		ctx,
		"SELECT id FROM skin_problems WHERE name = $1",
		name,
	).Scan(&id)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed searching skin problem %s: %v",
			name,
			err,
		)
	}

	return id
}

func getSkinProblemIndicationIDs(
	ctx context.Context,
	dbPool *pgxpool.Pool,
	skinProblemIndicationNames []string,
) []string {
	var names []string

	for i := 0; i < len(skinProblemIndicationNames); i++ {
		name := cases.
			Title(language.English).
			String(skinProblemIndicationNames[i])

		names = append(names, name)
	}

	rows, err := dbPool.Query(
		ctx,
		`
		SELECT id FROM skin_problem_indications WHERE name = any ($1)
		ORDER BY
		array_position($1, skin_problem_indications.name)
		`,
		names,
	)

	ids, err := pgx.CollectRows(
		rows,
		func(row pgx.CollectableRow) (string, error) {
			var id string
			err := row.Scan(&id)
			return id, err
		},
	)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed searching indication %v: %v",
			skinProblemIndicationNames,
			err,
		)
	}

	return ids
}

func SeedSkinProblemGroups(dbPool *pgxpool.Pool) {
	log.Println("[SEEDER]: Seeding skin problem groups...")

	ctx := context.Background()
	var totalCount int

	err := dbPool.QueryRow(
		ctx,
		"SELECT COUNT(*) as total FROM skin_problem_groups",
	).Scan(&totalCount)

	if err != nil {
		log.Println("[SEEDER]: Error scanning row: ", err)
		return
	}

	if totalCount > 0 {
		log.Println("[SEEDER]: Seeding skin problem groups already exist...")
		return
	}

	filePath := "./seeder/assets/skin_problems.csv"
	contents := readCsv(filePath, ',')

	if contents == nil {
		log.Println("[SEEDER]: No skin problem groups to seed")
		return
	}

	var data []domain.SkinProblemGroup

	for i, item := range contents {
		if i == 0 {
			continue // Skip header row
		}

		var (
			name                   = item[0]
			skinProblemIndications = item[1:]
		)

		skinProblemName := cases.
			Title(language.English).
			String(item[0])

		skinProblemID := getSkinProblemID(ctx, dbPool, skinProblemName)

		if skinProblemID == nil {
			log.Printf(
				"[WARNING]: skin problem id for name %s is not found",
				name,
			)
			continue
		}

		skinProblemIndicationIDs := getSkinProblemIndicationIDs(
			ctx,
			dbPool,
			skinProblemIndications,
		)

		for j := 0; j < len(skinProblemIndicationIDs); j++ {
			data = append(
				data,
				domain.SkinProblemGroup{
					SkinProblemID:           *skinProblemID,
					SkinProblemIndicationID: skinProblemIndicationIDs[j],
					ProblemOrder:            j,
				},
			)
		}
	}

	problemGroupRowSrc := pgx.CopyFromSlice(
		len(data),
		func(i int) ([]any, error) {
			return []any{
				data[i].SkinProblemID,
				data[i].SkinProblemIndicationID,
				data[i].ProblemOrder,
			}, nil
		},
	)

	count, err := dbPool.CopyFrom(
		ctx,
		pgx.Identifier{"skin_problem_groups"},
		[]string{
			"skin_problem_id",
			"skin_problem_indication_id",
			"problem_order",
		},
		problemGroupRowSrc,
	)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed CopyFrom in skin problem group, here's why: %v",
			err,
		)
		return
	}

	log.Printf(
		"[SEEDER]: Seeding %d skin problem groups succeed...",
		count,
	)
}
