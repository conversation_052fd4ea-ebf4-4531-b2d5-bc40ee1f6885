package seed

import (
	"context"
	"fmt"
	"log"

	"github.com/jackc/pgx/v5/pgxpool"
)

func SeedConcernAnswers(dbPool *pgxpool.Pool) {
	log.Println("[SEEDER]: Seeding concern answers...")

	ctx := context.Background()
	var totalCount int

	row := dbPool.QueryRow(ctx, "SELECT COUNT(*) as total FROM concern_answers")
	err := row.Scan(&totalCount)
	if err != nil {
		log.Println("[SEEDER]: Error scanning row:", err)
		return
	}

	if totalCount > 0 {
		log.Println("[SEEDER]: Seeding concern answers already exist...")
		return
	}

	concerns := []concernAnswer{
		{
			name:   "Pore",
			label:  "pores",
			answer: "This indicates enlarged or clogged openings in the skin, often due to excess sebum, dead skin cells, or environmental debris. Tackling this can lead to smoother texture and help prevent acne breakouts.",
		},
		{
			name:   "Spot",
			label:  "spots",
			answer: "This refers to small areas of discoloration or blemishes, often caused by acne, sun exposure, or irritation. Addressing spots helps even out skin tone and improves overall clarity.",
		},
		{
			name:   "Wrinkle",
			label:  "wrinkles",
			answer: "These suggest early signs of aging or repetitive facial movements that form lines and creases. Smoothing wrinkles promotes a more youthful look and helps preserve skin elasticity.",
		},
		{
			name:   "Texture",
			label:  "textures",
			answer: "This points to rough or uneven patches on the skin’s surface. Refining texture boosts radiance, allows for better product absorption, and creates an even base for makeup.",
		},
		{
			name:   "Porphyrin",
			label:  "porphyrin",
			answer: "This indicates an overproduction of acne-related bacteria, which can accumulate in pores and lead to inflammation. Reducing porphyrin levels can help minimize breakouts and maintain clearer skin.",
		},
		{
			name:   "Pigmentation",
			label:  "pigmentation",
			answer: "This highlights uneven melanin distribution, resulting in dark spots or patches. Targeting pigmentation issues leads to a more uniform complexion and helps control further discoloration.",
		},
		{
			name:   "Moisture",
			label:  "moisture",
			answer: "This underscores the importance of the skin’s water content and barrier function. Ensuring balanced hydration prevents dryness or excess oiliness and supports a healthy, resilient barrier.",
		},
		{
			name:   "Sensitive Area",
			label:  "sensitive_area",
			answer: "This denotes regions of skin prone to irritation, redness, or inflammation. Soothing and protecting these areas can reduce discomfort and minimize future flare-ups.",
		},
		{
			name:   "Brown Area",
			label:  "brown_area",
			answer: "This refers to localized hyperpigmentation (sun spots, freckles, etc.), often from sun exposure or past inflammation. Lightening brown areas helps even skin tone and restores overall brightness.",
		},
		{
			name:   "UV Damage",
			label:  "uv_damage",
			answer: "This suggests harm from prolonged sun exposure, contributing to fine lines, hyperpigmentation, and weakened elasticity. Protecting against UV damage with sunscreen and antioxidants is key to preventing premature aging.",
		},
	}

	// Generate every permutation of the concern answers the permutation should only take 3 values
	var concernAnswers [][]concernAnswer
	for i := 0; i < len(concerns); i++ {
		for j := 0; j < len(concerns); j++ {
			for k := 0; k < len(concerns); k++ {
				if i != j && i != k && j != k {
					concernAnswers = append(concernAnswers, []concernAnswer{
						concerns[i],
						concerns[j],
						concerns[k],
					})
				}
			}
		}
	}

	baseQuery := `INSERT INTO concern_answers
        (concern_key, answer)
    VALUES `
	var args []any
	for i, concern := range concernAnswers {
		if i > 0 {
			baseQuery += ", "
		}
		baseQuery += fmt.Sprintf("($%d, $%d)",
			i*2+1, i*2+2)
		key := fmt.Sprintf("%s_%s_%s", concern[0].label, concern[1].label, concern[2].label)
		answer := fmt.Sprintf("1.%s: %s\n2.%s: %s\n3.%s: %s", concern[0].name, concern[0].answer, concern[1].name, concern[1].answer, concern[2].name, concern[2].answer)
		args = append(args, key, answer)
	}

	_, err = dbPool.Exec(ctx, baseQuery, args...)
	if err != nil {
		log.Println("[SEEDER]: Error inserting concern answers:", err)
		return
	}

	log.Println("[SEEDER]: Seeding concern answers succed...")
}

type concernAnswer struct {
	name   string
	label  string
	answer string
}
