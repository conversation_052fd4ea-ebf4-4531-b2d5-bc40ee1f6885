package seed

import (
	"api/domain"
)

func surveyQuestionDynamicData() ([]domain.Survey, []domain.Survey) {
	var (
		firstParentQuestionID      = "614204d0-761a-4c38-887a-78f408b35b62"
		secondParentQuestionID     = "9d68eb3c-9783-4dae-88d4-ca8b03a59157"
		thirdParentQuestionID      = "9dc3fcbf-dbb4-44e3-ac95-94bd737cbe43"
		fourthParentQuestionID     = "a538b2b9-9f76-4862-8ce6-09f64cc74920"
		fifthParentQuestionID      = "cdcdc205-e7c0-4000-acb1-abb2d017c507"
		sixthParentQuestionID      = "2984abc2-8319-4aa0-a280-074e77c41883"
		seventhParentQuestionID    = "c7b48c3a-f7a7-443b-9ff2-38adb918defd"
		eighthParentQuestionID     = "07724c0d-1262-42fd-949e-e7aca79152e7"
		ninthParentQuestionID      = "76853884-7d24-437e-9e21-49a298bca4b9"
		tenthParentQuestionID      = "40ed04c6-0759-4ab9-90ec-af22ed55e90b"
		eleventhParentQuestionID   = "a5462568-96cb-4d62-8bc3-6ea19fc5a4e5"
		twelfthParentQuestionID    = "a4d5b5b9-50b1-4068-9b79-9682117f962a"
		thirteenthParentQuestionID = "664f12fe-3f94-4a4a-a2c8-1983433c4d6d"
		fourteenthParentQuestionID = "7843c1b8-59b0-4d17-bcb7-30be8b47ea32"
		fifteenthParentQuestionID  = "d1aae3a3-03c6-4c98-acd2-e02277da6c50"
		sixteenthParentQuestionID  = "0f428c82-f817-4b0b-bf47-1074c63943f0"
	)

	parentQuestions := []domain.Survey{
		{
			ID:                   firstParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui produk yang digunakan dan kebiasaan perawatan sehari-hari.",
			Question:             "Produk perawatan kulit apa saja yang saat ini Anda gunakan?",
			Answers: []domain.SurveyAnswer{
				{Title: "Hanya sabun cuci muka", Description: nil, ImageUrl: nil},
				{Title: "Saya sudah pakai serum dan pelembap", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 6,
			IsStatic:      false,
			Category:      domain.Informational,
		},
		{
			ID:                   secondParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Menggali faktor eksternal yang dapat memengaruhi kondisi kulit.",
			Question:             "Apakah Anda sering terpapar sinar matahari atau polusi?",
			Answers: []domain.SurveyAnswer{
				{
					Title:       "Ya, saya sering beraktivitas di luar ruangan",
					Description: nil,
					ImageUrl:    nil,
				},
				{
					Title:       "Tidak, saya lebih sering di dalam ruangan ber-AC",
					Description: nil,
					ImageUrl:    nil,
				},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 7,
			IsStatic:      false,
			Category:      domain.Informational,
		},
		{
			ID:                   thirdParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk menyesuaikan rekomendasi produk dengan kebutuhan pelanggan.",
			Question:             "Apakah Anda memiliki preferensi khusus untuk produk skincare, seperti yang berbahan alami atau bebas parfum?",
			Answers: []domain.SurveyAnswer{
				{Title: "Saya lebih suka yang alami", Description: nil, ImageUrl: nil},
				{
					Title:       "Tidak masalah, yang penting efektif",
					Description: nil,
					ImageUrl:    nil,
				},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 8,
			IsStatic:      false,
			Category:      domain.Informational,
		},
		{
			ID:                   fourthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Apakah Anda memiliki alergi Salmon?",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{
					Title:       "Tidak, sejauh ini tidak ada masalah alergi",
					Description: nil,
					ImageUrl:    nil,
				},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 9,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   fifthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Apakah Anda memiliki alergi Kacang-kacangan?",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{
					Title:       "Tidak, sejauh ini tidak ada masalah alergi",
					Description: nil,
					ImageUrl:    nil,
				},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 10,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   sixthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Apakah Anda sedang mengkonsumsi obat hormonal? (misalnya, KB atau terapi hormon)",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 11,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   seventhParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Apakah Anda sedang mengkonsumsi obat Anti Biotik?",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 12,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   eighthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Apakah Anda sedang mengkonsumsi obat pengencer darah? (kurang dari 3 bulan)",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 13,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   sixteenthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Apakah Anda sedang mengonsumsi Obat Isotretinoin?",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 14,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   ninthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Apakah Anda sedang menyusui?",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 15,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   tenthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Apakah Anda memiliki riwayat penyakit autoimun?",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 16,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   eleventhParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Apakah Anda sedang memiliki penyakit Diabetes tidak terkontrol?",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 17,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   twelfthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Apakah Anda memiliki riwayat Penyakit Kolesterol Tinggi?",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 18,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   thirteenthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Apakah Anda memiliki riwayat Penyakit Pernah Kanker?",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 19,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   fourteenthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Apakah Anda memiliki riwayat penyakit Darah Tinggi?",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 20,
			IsStatic:      false,
			Category:      domain.Informational,
		},
		{
			ID:                   fifteenthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Apakah Anda mempunyai pola makan yang baik?",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 21,
			IsStatic:      false,
			Category:      domain.Informational,
		},
	}

	childQuestions := []domain.Survey{
		{
			ParentQuestionID:     &firstParentQuestionID,
			ParentQuestionAnswer: ToPointer(1),
			Description:          "Untuk mengetahui produk yang digunakan dan kebiasaan perawatan sehari-hari.",
			Question:             "Apakah memakai bahan aktif? (e.g. AHA, BHA, Retinol)",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 0,
			IsStatic:      false,
			Category:      domain.Informational,
		},
		{
			ParentQuestionID:     &fifteenthParentQuestionID,
			ParentQuestionAnswer: ToPointer(1),
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Makanan Tidak Sehat Seperti Apa yang Anda Makan?",
			Answers: []domain.SurveyAnswer{
				{Title: "Sweet Food", Description: nil, ImageUrl: nil},
				{Title: "Spicy Food", Description: nil, ImageUrl: nil},
				{Title: "High-Protein Food", Description: nil, ImageUrl: nil},
				{Title: "Nuts", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 0,
			IsStatic:      false,
			Category:      domain.Informational,
		},
	}

	return parentQuestions, childQuestions
}

func surveyQuestionStaticData() ([]domain.Survey, []domain.Survey) {
	var (
		firstParentQuestionID  = "1f04ea29-b556-4793-80ca-f0314f49917d"
		secondParentQuestionID = "2097dda1-544a-4d7a-89c6-6314bcefa592"
		thirdParentQuestionID  = "959ac8b0-b1a7-4da6-beaf-70063c83fc53"
		fourthParentQuestionID = "94e22537-8ff3-4702-ac26-87c0a60139cb"
		fifthParentQuestionID  = "cef51648-1f44-43dd-944f-a677d37f8db4"
		sixthParentQuestionID  = "4864e913-0c7f-458f-9b19-dd091ccb254e"
	)

	parentQuestions := []domain.Survey{
		{
			ID:                   firstParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Bertujuan untuk melanjutkan pada rekomendasi treatment atau tidak.",
			Question:             "Apakah Anda sedang hamil atau dalam pengobatan kanker (chemotherapy, immunotherapy, radiotherapy)?",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 0,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ID:                   secondParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Pilih jenis kulit Anda",
			Answers: []domain.SurveyAnswer{
				{Title: "Normal", Description: nil, ImageUrl: nil},
				{Title: "Combination", Description: nil, ImageUrl: nil},
				{Title: "Oily", Description: nil, ImageUrl: nil},
				{Title: "Dehydration", Description: nil, ImageUrl: nil},
				{Title: "Sensitive", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 1,
			IsStatic:      true,
			Category:      domain.Informational,
		},
		{
			ID:                   thirdParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Apakah Anda memiliki masalah kulit tertentu yang ingin diatasi?",
			Answers: []domain.SurveyAnswer{
				{Title: "Wrinkle", Description: nil, ImageUrl: nil},
				{Title: "Acne", Description: nil, ImageUrl: nil},
				{Title: "Pores", Description: nil, ImageUrl: nil},
				{Title: "Scar", Description: nil, ImageUrl: nil},
				{Title: "Redness", Description: nil, ImageUrl: nil},
				{Title: "Pigmentation", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 2,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ID:                   fourthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Apakah Anda memiliki concern tertentu?",
			Answers: []domain.SurveyAnswer{
				{Title: "Milia / Wartz", Description: nil, ImageUrl: nil},
				{Title: "Chubby Cheeks / Double Chin", Description: nil, ImageUrl: nil},
				{Title: "Dark Lips", Description: nil, ImageUrl: nil},
				{Title: "Dark Circle", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 3,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ID:                   fifthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Apakah Anda memiliki tujuan lain?",
			Answers: []domain.SurveyAnswer{
				{Title: "Lifting", Description: nil, ImageUrl: nil},
				{Title: "Contouring", Description: nil, ImageUrl: nil},
				{Title: "Enhancement & Filling", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 4,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ID:                   sixthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk menyesuaikan rekomendasi produk dengan kebutuhan pelanggan.",
			Question:             "Apakah Anda melakukan treatment dalam 6 bulan terakhir?",
			Answers: []domain.SurveyAnswer{
				{Title: "Ya, Saya Melakukan Treatment", Description: nil, ImageUrl: nil},
				{Title: "Tidak", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 5,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
	}

	secondChildQuestions := []domain.Survey{
		{
			ParentQuestionID:     &thirdParentQuestionID,
			ParentQuestionAnswer: ToPointer(0),
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Pilih area",
			Answers: []domain.SurveyAnswer{
				{Title: "Area 1 (Dahi-Mata)", Description: nil, ImageUrl: nil},
				{Title: "Area 2 (Mata-Mulut)", Description: nil, ImageUrl: nil},
				{Title: "Area 3 (Mulut-Dagu)", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 0,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ParentQuestionID:     &thirdParentQuestionID,
			ParentQuestionAnswer: ToPointer(1),
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Pilih area",
			Answers: []domain.SurveyAnswer{
				{Title: "Area 1 (Dahi-Mata)", Description: nil, ImageUrl: nil},
				{Title: "Area 2 (Mata-Mulut)", Description: nil, ImageUrl: nil},
				{Title: "Area 3 (Mulut-Dagu)", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 1,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ParentQuestionID:     &thirdParentQuestionID,
			ParentQuestionAnswer: ToPointer(2),
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Pilih area",
			Answers: []domain.SurveyAnswer{
				{Title: "Area 1 (Dahi-Mata)", Description: nil, ImageUrl: nil},
				{Title: "Area 2 (Mata-Mulut)", Description: nil, ImageUrl: nil},
				{Title: "Area 3 (Mulut-Dagu)", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 2,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ParentQuestionID:     &thirdParentQuestionID,
			ParentQuestionAnswer: ToPointer(3),
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Pilih area",
			Answers: []domain.SurveyAnswer{
				{Title: "Area 1 (Dahi-Mata)", Description: nil, ImageUrl: nil},
				{Title: "Area 2 (Mata-Mulut)", Description: nil, ImageUrl: nil},
				{Title: "Area 3 (Mulut-Dagu)", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 3,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ParentQuestionID:     &thirdParentQuestionID,
			ParentQuestionAnswer: ToPointer(4),
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Pilih area",
			Answers: []domain.SurveyAnswer{
				{Title: "Area 1 (Dahi-Mata)", Description: nil, ImageUrl: nil},
				{Title: "Area 2 (Mata-Mulut)", Description: nil, ImageUrl: nil},
				{Title: "Area 3 (Mulut-Dagu)", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 4,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ParentQuestionID:     &thirdParentQuestionID,
			ParentQuestionAnswer: ToPointer(5),
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Pilih area",
			Answers: []domain.SurveyAnswer{
				{Title: "Area 1 (Dahi-Mata)", Description: nil, ImageUrl: nil},
				{Title: "Area 2 (Mata-Mulut)", Description: nil, ImageUrl: nil},
				{Title: "Area 3 (Mulut-Dagu)", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 5,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
	}

	fourthChildQuestion := []domain.Survey{
		{
			ParentQuestionID:     &sixthParentQuestionID,
			ParentQuestionAnswer: ToPointer(0),
			Description:          "Untuk menyesuaikan rekomendasi produk dengan kebutuhan pelanggan.",
			Question:             "Apakah Anda melakukan treatment dalam 6 bulan terakhir?",
			Answers: []domain.SurveyAnswer{
				{Title: "treatment-interval", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SurveyDropdown,
			QuestionOrder: 0,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
	}

	childQuestions := append(secondChildQuestions, fourthChildQuestion...)

	return parentQuestions, childQuestions
}
