package seed

import (
	"api/domain"
	"context"
	"fmt"
	"log"

	"github.com/jackc/pgx/v5/pgxpool"
)

func SeedConcernGroups(dbPool *pgxpool.Pool) {
	log.Println("[SEEDER]: Seeding concern groups...")

	ctx := context.Background()
	var totalCount int

	row := dbPool.QueryRow(ctx, "SELECT COUNT(*) as total FROM concern_groups")
	err := row.Scan(&totalCount)
	if err != nil {
		log.Println("[SEEDER]: Error scanning row:", err)
		return
	}

	if totalCount > 0 {
		log.Println("[SEEDER]: Seeding concern groups already exist...")
		return
	}

	filePath := "./seeder/assets/concern_groups.csv"
	concernGroups := readCsv(filePath, ',')

	if concernGroups == nil {
		log.Println("[SEEDER]: No concern groups to seed")
		return
	}

	concernGroupsData := make([]domain.ConcernGroup, len(concernGroups)-1)

	for i, treatment := range concernGroups {
		if i == 0 {
			continue // Skip header row
		}
		concernGroupsData[i-1] = domain.ConcernGroup{
			Name:    treatment[0],
			Concern: treatment[1],
		}
	}

	baseQuery := "INSERT INTO concern_groups (name, concern) VALUES "
	var args []any
	for i, concernGroup := range concernGroupsData {
		if i > 0 {
			baseQuery += ", "
		}
		baseQuery += fmt.Sprintf("($%d, $%d)", i*2+1, i*2+2)
		args = append(args, concernGroup.Name, concernGroup.Concern)
	}

	_, err = dbPool.Exec(ctx, baseQuery, args...)
	if err != nil {
		log.Println("[SEEDER]: Error inserting concern groups:", err)
		return
	}

	log.Println("[SEEDER]: Seeding concern groups succed...")
}
