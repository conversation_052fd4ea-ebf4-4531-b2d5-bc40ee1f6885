package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	seeder "api/seeder/seed"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/joho/godotenv"
)

func main() {
	loadEnv()
	dbPool, err := setupDatabase()
	if err != nil {
		fmt.Printf("failed to set up database: %v \n", err)
		return
	}

	defer dbPool.Close()

	if err := Seed(dbPool); err != nil {
		log.Fatalf("Failed to seed database: %v", err)
		return
	}

	log.Println("Database seeding completed successfully")
}

func Seed(dbPool *pgxpool.Pool) error {
	var timings []TimingResult
	totalStart := time.Now()

	// measure execution time
	timings = append(timings, measureExecutionTime("SeedUsers", func() {
		seeder.SeedUsers(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedTreatments", func() {
		seeder.SeedTreatments(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedConcernGroups", func() {
		seeder.SeedConcernGroups(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedConcernAnswers", func() {
		seeder.SeedConcernAnswers(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedParameterSkinEvaluations", func() {
		seeder.SeedParameterSkinEvaluations(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedSurveyQuestions", func() {
		seeder.SeedSurveyQuestions(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedTreatmentCategories", func() {
		seeder.SeedTreatmentCategories(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedSkinProblemIndications", func() {
		seeder.SeedSkinProblemIndications(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedSkinProblems", func() {
		seeder.SeedSkinProblems(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedSkinProblemGroups", func() {
		seeder.SeedSkinProblemGroups(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedTreatmentIntervals", func() {
		seeder.SeedTreatmentIntervals(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedTreatmentLists", func() {
		seeder.SeedTreatmentLists(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedProductLists", func() {
		seeder.SeedProductLists(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedSkinAnalyzes", func() {
		seeder.SeedSkinAnalyzes(dbPool)
	}))

	totalDuration := time.Since(totalStart)

	// show the duration results
	fmt.Println("\nSeeder Execution Times:")
	fmt.Println("------------------------")
	for _, timing := range timings {
		fmt.Printf("%s: %v ms\n", timing.Operation, timing.Duration.Milliseconds())
	}
	fmt.Printf("\nTotal Execution Time: %v ms\n", totalDuration.Milliseconds())

	return nil
}

type TimingResult struct {
	Operation string
	Duration  time.Duration
}

// measureExecutionTime
func measureExecutionTime(operation string, fn func()) TimingResult {
	start := time.Now()
	fn()
	duration := time.Since(start)
	return TimingResult{
		Operation: operation,
		Duration:  duration,
	}
}

// loadEnv loads environment variables from a .env file, logging if not found
func loadEnv() {
	if err := godotenv.Load(".env"); err != nil {
		log.Println("No .env file found, reading configuration from environment variables")
	}
}

// setupDatabase initializes and verifies the database connection
func setupDatabase() (*pgxpool.Pool, error) {
	dbURL := os.Getenv("DATABASE_URL")
	config, err := pgxpool.ParseConfig(dbURL)
	if err != nil {
		return nil, fmt.Errorf("unable to parse database URL: %w", err)
	}

	dbPool, err := pgxpool.NewWithConfig(context.Background(), config)
	if err != nil {
		return nil, fmt.Errorf("unable to connect to database: %w", err)
	}

	if err := dbPool.Ping(context.Background()); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return dbPool, nil
}
