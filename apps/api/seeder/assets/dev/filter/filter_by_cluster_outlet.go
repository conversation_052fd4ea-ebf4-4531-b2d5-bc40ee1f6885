package main

import (
	"encoding/csv"
	"fmt"
	"os"
)

// Load unique outlet IDs from the second CSV
func loadUniqueOutlets(filename string) (map[string]struct{}, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	reader.Read() // Skip the header

	outlets := make(map[string]struct{})
	for {
		record, err := reader.Read()
		if err != nil {
			break // EOF or error
		}
		outlets[record[0]] = struct{}{} // Record the unique outlet ID
	}
	return outlets, nil
}

// Filter rows from the first CSV based on unique outlets from the second CSV
func filterCSV(inputFile, outputFile string, outlets map[string]struct{}) error {
	file, err := os.Open(inputFile)
	if err != nil {
		return err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	writer, err := os.Create(outputFile)
	if err != nil {
		return err
	}
	defer writer.Close()
	csvWriter := csv.<PERSON><PERSON>riter(writer)
	defer csvWriter.Flush()

	header, err := reader.Read() // Read header
	if err != nil {
		return err
	}
	csvWriter.Write(header) // Write header to output

	for {
		record, err := reader.Read()
		if err != nil {
			break // EOF or error
		}
		outletID := record[2] // a.outlet_id is in the 3rd column (index 2)
		if _, exists := outlets[outletID]; exists {
			csvWriter.Write(record) // Write matching record
		}
	}
	return nil
}

func main() {
	// Load unique outlet IDs from the second CSV
	outlets, err := loadUniqueOutlets("../polaris_outlet_reference_filtered.csv")
	if err != nil {
		fmt.Println("Error loading unique outlets:", err)
		return
	}

	// Filter and save the filtered results from the first CSV
	err = filterCSV("filtered_output.csv", "polaris_daily_outlet_filtered_clustered_all.csv", outlets)
	if err != nil {
		fmt.Println("Error filtering CSV:", err)
	} else {
		fmt.Println("Filtered CSV saved to polaris_daily_outlet_filtered_clustered_all.csv")
	}
}
