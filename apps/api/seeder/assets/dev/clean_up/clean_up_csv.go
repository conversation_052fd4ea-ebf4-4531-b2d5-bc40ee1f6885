package main

import (
	"encoding/csv"
	"fmt"
	"io"
	"os"
)

// Checks if any column in a record is empty and logs which columns are empty
func hasEmptyColumn(record []string) bool {
	emptyColumns := []int{}
	for i, column := range record {
		if column == "" {
			emptyColumns = append(emptyColumns, i)
		}
	}
	if len(emptyColumns) > 0 {
		return true
	}
	return false
}

// Filters out rows with empty columns and writes the result to an output file
func filterCSV(inputFile, outputFile string) error {
	inFile, err := os.Open(inputFile)
	if err != nil {
		return err
	}
	defer inFile.Close()

	reader := csv.NewReader(inFile)
	reader.Comma = '|'          // Set the field delimiter to pipe
	reader.FieldsPerRecord = -1 // Allow variable number of fields
	reader.LazyQuotes = true    // Handle unescaped quotes gracefully

	outFile, err := os.Create(outputFile)
	if err != nil {
		return err
	}
	defer outFile.Close()

	writer := csv.NewWriter(outFile)
	defer writer.Flush()

	header, err := reader.Read() // Read the header row
	if err != nil {
		return err
	}
	writer.Write(header) // Write header to output

	expectedFieldCount := len(header)

	rowCount := 0
	lineNumber := 1 // Account for header

	for {
		record, err := reader.Read()
		lineNumber++
		if err == io.EOF {
			break // Reached the end of the file
		}
		if err != nil {
			fmt.Printf("Error reading record at line %d: %v\n", lineNumber, err)
			continue // Skip this record and continue with the next
		}

		// Check if record has the expected number of fields
		if len(record) != expectedFieldCount {
			fmt.Printf("Skipping record at line %d due to incorrect number of fields (expected %d, got %d): %v\n", lineNumber, expectedFieldCount, len(record), record)
			continue // Skip this record
		}

		// Only write records that do not have empty columns
		if !hasEmptyColumn(record) {
			err = writer.Write(record)
			if err != nil {
				fmt.Printf("Error writing record at line %d: %v\n", lineNumber, err)
				continue
			}
			rowCount++
		}
	}

	fmt.Println("Total rows written to output:", rowCount)
	return nil
}

func main() {
	inputFile := "polaris_daily_outlet_update_big.csv" // Path to your large CSV file
	outputFile := "filtered_output.csv"                // Output file to save the filtered results

	err := filterCSV(inputFile, outputFile)
	if err != nil {
		fmt.Println("Error filtering CSV:", err)
	} else {
		fmt.Println("Filtered CSV saved to", outputFile)
	}
}

