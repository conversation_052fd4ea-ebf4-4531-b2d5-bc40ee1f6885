{
    "editor.defaultFormatter": "biomejs.biome",
    "typescript.enablePromptUseWorkspaceTsdk": false,
    "files.associations": {
        "*.yml.example": "yaml",
        "*.css": "tailwindcss",
        "*.plist": "xml"
    },
    "search.exclude": {
        "**/.output": true,
        "**/build": true,
        "**/dist": true,
        "**/templates": true
    },
    "tailwindCSS.experimental.classRegex": [
        [
            "clsx\\(([^)]*)\\)",
            "(?:'|\"|`)([^']*)(?:'|\"|`)"
        ],
        "cn\\(([^)]*)\\)", // cn(xxx)
        "clx\\(([^)]*)\\)", // clx(xxx)
        "cx\\(([^)]*)\\)" // cx(xxx)
    ],
    "tailwindCSS.files.exclude": [
        "**/.github/**",
        "**/*.{md,js,ts,json}"
    ],
    "[toml]": {
        "editor.defaultFormatter": "tamasfe.even-better-toml"
    },
    "[markdown]": {
        "editor.defaultFormatter": "darkriszty.markdown-table-prettify"
    },
    "xcodeIntegration.projectPath": "/Users/<USER>/Projects/euromedica-aizer"
}
